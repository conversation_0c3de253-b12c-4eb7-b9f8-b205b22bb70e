#!/usr/bin/env python
"""
Script de démonstration pour tester les nouvelles fonctionnalités de filtrage des utilisateurs
"""
import os
import sys
import django
import requests
import json
from datetime import date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock.settings')
django.setup()

from base.models import User
from entreprise.models import Entreprise
from magasins.models import Magasin, LocalisationMagasin
from entrepot.models import Entrepot
from responsable_magasin.models import Responsable_magasin
from responsable_entrepot.models import ResponsableEntrepot
from vendeurs.models import Vendeur
from personalisation_user.models import UserProfile


def create_test_data():
    """Créer des données de test"""
    print("🔧 Création des données de test...")
    
    # Créer une entreprise
    entreprise, created = Entreprise.objects.get_or_create(
        nom="Demo Entreprise",
        defaults={
            'adresse': "123 Demo Street",
            'nif': "1234567890123",
            'date_creation': date.today()
        }
    )
    print(f"✅ Entreprise: {entreprise.nom} ({'créée' if created else 'existante'})")
    
    # Créer un entrepôt
    entrepot, created = Entrepot.objects.get_or_create(
        nom="Demo Entrepôt",
        defaults={
            'adresse': "456 Warehouse Ave",
            'capacite_stockage': 1000,
            'entreprise': entreprise
        }
    )
    print(f"✅ Entrepôt: {entrepot.nom} ({'créé' if created else 'existant'})")
    
    # Créer une localisation pour le magasin
    localisation, created = LocalisationMagasin.objects.get_or_create(
        adresse="789 Store Blvd",
        defaults={
            'ville': "Demo City",
            'code_postal': "12345"
        }
    )
    
    # Créer un magasin
    magasin, created = Magasin.objects.get_or_create(
        nom="Demo Magasin",
        defaults={
            'localisation': localisation,
            'entreprise': entreprise,
            'entrepot': entrepot
        }
    )
    print(f"✅ Magasin: {magasin.nom} ({'créé' if created else 'existant'})")
    
    # Créer un utilisateur admin
    admin_user, created = User.objects.get_or_create(
        username="demo_admin",
        defaults={
            'nom': "Admin",
            'prenom': "Demo",
            'email': "<EMAIL>",
            'telephone': "1111111111",
            'adresse': "Admin Address",
            'role': User.Role.ADMIN
        }
    )
    if created:
        admin_user.set_password("demo123")
        admin_user.save()
    
    # Créer le profil admin
    profile, created = UserProfile.objects.get_or_create(
        user=admin_user,
        defaults={
            'entreprise': entreprise,
            'role': 'ADMIN'
        }
    )
    print(f"✅ Admin: {admin_user.username} ({'créé' if created else 'existant'})")
    
    # Créer un responsable magasin
    resp_magasin_user, created = User.objects.get_or_create(
        username="demo_resp_magasin",
        defaults={
            'nom': "Responsable",
            'prenom': "Magasin",
            'email': "<EMAIL>",
            'telephone': "2222222222",
            'adresse': "Resp Address",
            'role': User.Role.RESPONSABLE_MAGASIN
        }
    )
    if created:
        resp_magasin_user.set_password("demo123")
        resp_magasin_user.save()
        
        Responsable_magasin.objects.create(
            user=resp_magasin_user,
            code_responsable_magasin="RESP-123456",
            magasin=magasin,
            date_embauche=date.today()
        )
    print(f"✅ Responsable Magasin: {resp_magasin_user.username} ({'créé' if created else 'existant'})")
    
    # Créer un responsable entrepôt
    resp_entrepot_user, created = User.objects.get_or_create(
        username="demo_resp_entrepot",
        defaults={
            'nom': "Responsable",
            'prenom': "Entrepot",
            'email': "<EMAIL>",
            'telephone': "3333333333",
            'adresse': "Resp Ent Address",
            'role': User.Role.RESPONSABLE_ENTREPOT
        }
    )
    if created:
        resp_entrepot_user.set_password("demo123")
        resp_entrepot_user.save()
        
        ResponsableEntrepot.objects.create(
            user=resp_entrepot_user,
            entrepot=entrepot,
            code_responsable="RESP-ENT-123456",
            date_embauche=date.today()
        )
    print(f"✅ Responsable Entrepôt: {resp_entrepot_user.username} ({'créé' if created else 'existant'})")
    
    # Créer un vendeur
    vendeur_user, created = User.objects.get_or_create(
        username="demo_vendeur",
        defaults={
            'nom': "Vendeur",
            'prenom': "Demo",
            'email': "<EMAIL>",
            'telephone': "4444444444",
            'adresse': "Vendeur Address",
            'role': User.Role.VENDEUR
        }
    )
    if created:
        vendeur_user.set_password("demo123")
        vendeur_user.save()
        
        Vendeur.objects.create(
            user=vendeur_user,
            code_vendeur="VEND-123456",
            magasin=magasin,
            date_embauche=date.today()
        )
    print(f"✅ Vendeur: {vendeur_user.username} ({'créé' if created else 'existant'})")
    
    return {
        'entreprise': entreprise,
        'magasin': magasin,
        'entrepot': entrepot,
        'admin_user': admin_user
    }


def test_api_endpoints(data):
    """Tester les endpoints API"""
    print("\n🧪 Test des endpoints API...")
    
    base_url = "http://localhost:8000/api/personalisation-user"
    
    # Obtenir un token d'authentification
    auth_response = requests.post("http://localhost:8000/api/auth/login/", {
        'telephone': data['admin_user'].telephone,
        'password': 'demo123'
    })
    
    if auth_response.status_code != 200:
        print("❌ Erreur d'authentification")
        return
    
    token = auth_response.json().get('access')
    headers = {'Authorization': f'Bearer {token}'}
    
    print("✅ Authentification réussie")
    
    # Test 1: Filtrer par entreprise
    print("\n📋 Test 1: Filtrage par entreprise")
    response = requests.get(
        f"{base_url}/filter-by-entreprise/",
        params={'entreprise_id': str(data['entreprise'].id)},
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès - {result['total_users']} utilisateurs trouvés")
        print(f"   Entreprise: {result['entreprise']['nom']}")
        for user in result['users']:
            print(f"   - {user['prenom']} {user['nom']} ({user['role']})")
    else:
        print(f"❌ Erreur: {response.status_code} - {response.text}")
    
    # Test 2: Filtrer par magasin
    print("\n🏪 Test 2: Filtrage par magasin")
    response = requests.get(
        f"{base_url}/filter-by-magasin/",
        params={'magasin_id': str(data['magasin'].id)},
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès - {result['total_users']} utilisateurs trouvés")
        print(f"   Magasin: {result['magasin']['nom']}")
        for user in result['users']:
            print(f"   - {user['prenom']} {user['nom']} ({user['role']})")
    else:
        print(f"❌ Erreur: {response.status_code} - {response.text}")
    
    # Test 3: Filtrage avancé
    print("\n🔍 Test 3: Filtrage avancé (entreprise + rôle)")
    response = requests.get(
        f"{base_url}/filter-advanced/",
        params={
            'entreprise_id': str(data['entreprise'].id),
            'role': 'RESPONSABLE_MAGASIN'
        },
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès - {result['total_users']} responsables magasin trouvés")
        for user in result['users']:
            print(f"   - {user['prenom']} {user['nom']} ({user['role']})")
    else:
        print(f"❌ Erreur: {response.status_code} - {response.text}")


def main():
    """Fonction principale"""
    print("🚀 Démonstration des fonctionnalités de filtrage des utilisateurs")
    print("=" * 60)
    
    try:
        # Créer les données de test
        data = create_test_data()
        
        # Tester les endpoints API
        test_api_endpoints(data)
        
        print("\n✅ Démonstration terminée avec succès!")
        print("\n📝 Endpoints disponibles:")
        print("   - GET /api/personalisation-user/filter-by-entreprise/?entreprise_id=<uuid>")
        print("   - GET /api/personalisation-user/filter-by-magasin/?magasin_id=<uuid>")
        print("   - GET /api/personalisation-user/filter-advanced/?entreprise_id=<uuid>&role=<role>")
        print("   - GET /api/personalisation-user/filter-advanced/?magasin_id=<uuid>&role=<role>")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
