from rest_framework import serializers
from .models import Categorie

class CategorieSerializer(serializers.ModelSerializer):
    class Meta:
        model = Categorie
        fields = ['id_categorie', 'nom']

    def validate_nom(self, value):
        if Categorie.objects.filter(nom=value).exclude(id_categorie=self.instance.id_categorie if self.instance else None).exists():
            raise serializers.ValidationError("Une catégorie avec ce nom existe déjà.")
        return value