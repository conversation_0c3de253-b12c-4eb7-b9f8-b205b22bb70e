# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0001_initial'),
        ('inventaire', '0001_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='detailinventaire',
            name='produit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit'),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='entrepot',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventaires', to='entrepot.entrepot'),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='magasin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventaires', to='magasins.magasin'),
        ),
        migrations.AddField(
            model_name='inventaire',
            name='responsable',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventaires', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='detailinventaire',
            name='inventaire',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='inventaire.inventaire'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['date_planification'], name='inventaire__date_pl_474d64_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['magasin'], name='inventaire__magasin_cb39e8_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['entrepot'], name='inventaire__entrepo_360cef_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['statut'], name='inventaire__statut_a771d3_idx'),
        ),
        migrations.AddIndex(
            model_name='inventaire',
            index=models.Index(fields=['type_inventaire'], name='inventaire__type_in_fd21e5_idx'),
        ),
        migrations.AddIndex(
            model_name='detailinventaire',
            index=models.Index(fields=['produit'], name='inventaire__produit_6b9a4a_idx'),
        ),
        migrations.AddIndex(
            model_name='detailinventaire',
            index=models.Index(fields=['date_comptage'], name='inventaire__date_co_7e1a9d_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='detailinventaire',
            unique_together={('inventaire', 'produit')},
        ),
    ]
