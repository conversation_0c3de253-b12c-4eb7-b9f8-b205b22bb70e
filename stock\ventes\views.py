from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Vente, DetailVente
from .serializers import VenteSerializer, DetailVenteSerializer
from base.permissions import VentePermissions, IsAdmin, IsResponsableMagasin, VenteOwnerPermission
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.contrib.auth import get_user_model

User = get_user_model()

class VenteListCreateView(APIView):
    permission_classes = [IsAuthenticated, VentePermissions]

    @swagger_auto_schema(
        operation_description="Récupérer la liste des ventes",
        manual_parameters=[
            openapi.Parameter(
                'magasin_id',
                openapi.IN_QUERY,
                description="ID du magasin pour filtrer les ventes",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_UUID,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Liste des ventes",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        # Récupérer le magasin_id des paramètres de requête
        magasin_id = request.query_params.get('magasin_id')
        
        # Filtrer les ventes par magasin si spécifié
        if magasin_id:
            ventes = Vente.objects.filter(magasin_id=magasin_id)
        else:
            # Si l'utilisateur est un vendeur, ne montrer que ses ventes
            if request.user.role == 'VENDEUR':
                ventes = Vente.objects.filter(vendeur__user=request.user)
            # Si l'utilisateur est un responsable de magasin, montrer les ventes de son magasin
            elif request.user.role == 'RESPONSABLE_MAGASIN':
                ventes = Vente.objects.filter(magasin__responsable_magasin__user=request.user)
            else:
                ventes = Vente.objects.all()

        serializer = VenteSerializer(ventes, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer une nouvelle vente",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['magasin', 'mode_paiement', 'produits'],
            properties={
                'magasin': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_UUID,
                    description="ID du magasin"
                ),
                'client': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_UUID,
                    nullable=True,
                    description="ID du client (optionnel pour vente anonyme)"
                ),
                'vendeur': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_UUID,
                    nullable=True,
                    description="ID du vendeur (auto-rempli si utilisateur vendeur)"
                ),
                'mode_paiement': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['ESPECES', 'CARTE', 'CHEQUE', 'VIREMENT'],
                    description="Mode de paiement"
                ),
                'client_nom': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    nullable=True,
                    description="Nom du client pour vente anonyme (optionnel)"
                ),
                'client_telephone': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    nullable=True,
                    description="Téléphone du client pour vente anonyme (optionnel)"
                ),
                'produits': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        required=['produit', 'quantite'],
                        properties={
                            'produit': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_UUID,
                                description="ID du produit"
                            ),
                            'quantite': openapi.Schema(
                                type=openapi.TYPE_INTEGER,
                                minimum=1,
                                description="Quantité à vendre"
                            )
                        }
                    ),
                    description="Liste des produits à vendre"
                )
            }
        ),
        responses={
            201: openapi.Response(
                description="Vente créée avec succès",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id_vente': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                        'numero_facture': openapi.Schema(type=openapi.TYPE_STRING),
                        'client': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                        'client_details': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True),
                        'vendeur': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                        'vendeur_details': openapi.Schema(type=openapi.TYPE_OBJECT, nullable=True),
                        'magasin': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                        'magasin_details': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'montant_total': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                        'montant_ht': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                        'montant_tva': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                        'date_vente': openapi.Schema(type=openapi.TYPE_STRING, format='datetime'),
                        'mode_paiement': openapi.Schema(type=openapi.TYPE_STRING),
                        'details': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        )
                    }
                )
            ),
            400: "Bad Request - Données invalides ou stock insuffisant",
            403: "Forbidden - Permissions insuffisantes"
        }
    )
    def post(self, request):
        # Auto-remplissage intelligent selon le rôle de l'utilisateur
        data = request.data.copy()

        # Si l'utilisateur est un vendeur, auto-remplir ses informations
        if request.user.role == 'VENDEUR':
            try:
                vendeur = request.user.vendeur
                # Auto-remplir le vendeur et le magasin si non spécifiés
                if not data.get('vendeur'):
                    data['vendeur'] = vendeur.id
                if not data.get('magasin'):
                    data['magasin'] = vendeur.magasin.id

                # Vérifier que le vendeur ne modifie pas ces champs pour d'autres valeurs
                if data.get('vendeur') != vendeur.id:
                    return Response(
                        {"error": "Un vendeur ne peut créer des ventes qu'en son nom"},
                        status=status.HTTP_403_FORBIDDEN
                    )
                if data.get('magasin') != vendeur.magasin.id:
                    return Response(
                        {"error": "Un vendeur ne peut créer des ventes que pour son magasin"},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except:
                return Response(
                    {"error": "Profil vendeur non trouvé"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Récupérer les données (potentiellement auto-remplies)
        magasin_id = data.get('magasin')
        produits = data.get('produits', [])
        mode_paiement = data.get('mode_paiement', 'ESPECES')  # Valeur par défaut

        # Validations
        if not magasin_id:
            return Response(
                {"error": "Le magasin est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not produits:
            return Response(
                {"error": "Au moins un produit est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if mode_paiement not in dict(Vente.MODE_PAIEMENT_CHOICES):
            return Response(
                {"error": "Mode de paiement invalide"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Vérifier que le vendeur appartient au magasin (si spécifié)
        vendeur_id = data.get('vendeur')
        if vendeur_id:
            from vendeurs.models import Vendeur
            try:
                vendeur = Vendeur.objects.get(id=vendeur_id)
                if str(vendeur.magasin.id) != str(magasin_id):
                    return Response(
                        {"error": "Le vendeur doit appartenir au magasin spécifié"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Vendeur.DoesNotExist:
                return Response(
                    {"error": "Vendeur non trouvé"},
                    status=status.HTTP_404_NOT_FOUND
                )

        try:
            # Créer la vente
            vente_data = {
                'magasin': magasin_id,
                'client': request.data.get('client'),
                'vendeur': vendeur_id,
                'mode_paiement': mode_paiement
            }
            serializer = VenteSerializer(data=vente_data)
            if serializer.is_valid():
                vente = serializer.save()

                # Ajouter les produits
                for produit_data in produits:
                    produit_id = produit_data.get('produit')
                    quantite = produit_data.get('quantite')

                    if not produit_id or not quantite:
                        vente.delete()  # Annuler la vente si un produit est invalide
                        return Response(
                            {"error": "Produit et quantité requis pour chaque produit"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    try:
                        detail = DetailVente.ajouterProduit(vente.id_vente, produit_id, quantite)
                    except Exception as e:
                        vente.delete()  # Annuler la vente en cas d'erreur
                        return Response(
                            {"error": str(e)},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Recharger la vente pour avoir les montants à jour
                vente.refresh_from_db()
                return Response(VenteSerializer(vente).data, status=status.HTTP_201_CREATED)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class VenteDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)
            # Vérifier les permissions
            if request.user.role == 'VENDEUR' and vente.vendeur.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            if request.user.role == 'RESPONSABLE_MAGASIN' and vente.magasin.responsable_magasin.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer = VenteSerializer(vente)
            return Response(serializer.data)
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)
            serializer = VenteSerializer(vente, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Vente.DoesNotExist:
            return Response({"error": "Vente non trouvée"}, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Annuler une vente (restaure le stock)",
        responses={
            204: "Vente annulée avec succès",
            403: "Accès non autorisé",
            404: "Vente non trouvée"
        }
    )
    def delete(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)

            # Vérifications de sécurité
            # Un vendeur ne peut annuler que ses propres ventes
            if request.user.role == 'VENDEUR':
                if not hasattr(request.user, 'vendeur') or vente.vendeur != request.user.vendeur:
                    return Response(
                        {"error": "Vous ne pouvez annuler que vos propres ventes"},
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Un responsable de magasin ne peut annuler que les ventes de son magasin
            elif request.user.role == 'RESPONSABLE_MAGASIN':
                if not hasattr(request.user, 'responsable_magasin'):
                    return Response(
                        {"error": "Accès non autorisé"},
                        status=status.HTTP_403_FORBIDDEN
                    )
                responsable = request.user.responsable_magasin
                if vente.magasin not in responsable.magasins_geres.all():
                    return Response(
                        {"error": "Vous ne pouvez annuler que les ventes de vos magasins"},
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Restaurer le stock pour chaque produit de la vente
            for detail in vente.details.all():
                try:
                    detail.retirerProduit()  # Cette méthode restaure le stock
                except Exception as e:
                    return Response(
                        {"error": f"Erreur lors de la restauration du stock: {str(e)}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Sauvegarder les informations de la vente avant suppression
            vente_info = {
                'numero_facture': vente.numero_facture,
                'montant_total': vente.montant_total,
                'date_vente': vente.date_vente
            }

            vente.delete()

            return Response({
                'message': 'Vente annulée avec succès',
                'vente_annulee': vente_info
            }, status=status.HTTP_200_OK)

        except Vente.DoesNotExist:
            return Response({"error": "Vente non trouvée"}, status=status.HTTP_404_NOT_FOUND)

class AddProduitVenteView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Ajouter un produit à une vente existante",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['produit', 'quantite'],
            properties={
                'produit': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'quantite': openapi.Schema(type=openapi.TYPE_INTEGER, minimum=1)
            }
        ),
        responses={
            201: openapi.Response(
                description="Produit ajouté à la vente",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            403: "Forbidden",
            404: "Not Found"
        }
    )
    def post(self, request, vente_id):
        try:
            vente = Vente.objects.get(id_vente=vente_id)
            # Vérifier les permissions
            if request.user.role == 'VENDEUR' and vente.vendeur.user != request.user:
                return Response(
                    {"error": "Vous n'avez pas accès à cette vente"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            produit_id = request.data.get('produit')
            quantite = request.data.get('quantite')
            
            if not produit_id or not quantite:
                return Response(
                    {"error": "Produit et quantité requis"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Vérifier que le produit appartient au magasin
            from produit.models import Produit
            try:
                produit = Produit.objects.get(id_produit=produit_id)
                if produit.magasin != vente.magasin:
                    return Response(
                        {"error": "Le produit doit appartenir au magasin de la vente"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Produit.DoesNotExist:
                return Response(
                    {"error": "Produit non trouvé"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            detail = DetailVente.ajouterProduit(vente_id, produit_id, quantite)
            serializer = DetailVenteSerializer(detail)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

class FinaliserVenteView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, id):
        try:
            vente = Vente.objects.get(id_vente=id)
            
            # Vérifier qu'il y a des produits dans la vente
            if not vente.details.exists():
                return Response(
                    {"error": "Impossible de finaliser une vente sans produits"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Vérifier que le montant total est supérieur à 0
            if vente.montant_total <= 0:
                return Response(
                    {"error": "Le montant total doit être supérieur à 0"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Mettre à jour le mode de paiement
            mode_paiement = request.data.get('mode_paiement')
            if not mode_paiement:
                return Response(
                    {"error": "Le mode de paiement est requis"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if mode_paiement not in dict(Vente.MODE_PAIEMENT_CHOICES):
                return Response(
                    {"error": "Mode de paiement invalide"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            vente.mode_paiement = mode_paiement
            vente.save()

            return Response(VenteSerializer(vente).data)

        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )


class VentesByUserView(APIView):
    """Vue pour récupérer les ventes d'un utilisateur spécifique"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les ventes d'un utilisateur spécifique",
        responses={
            200: openapi.Response(
                description="Liste des ventes de l'utilisateur",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            404: "Utilisateur non trouvé",
            403: "Accès non autorisé"
        }
    )
    def get(self, request, userid):
        try:
            # Vérifier que l'utilisateur existe
            user = User.objects.get(id=userid)

            # Vérifications de sécurité
            # Un vendeur ne peut voir que ses propres ventes
            if request.user.role == 'VENDEUR' and request.user.id != userid:
                return Response(
                    {"error": "Vous ne pouvez voir que vos propres ventes"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Un responsable de magasin ne peut voir que les ventes de son magasin
            if request.user.role == 'RESPONSABLE_MAGASIN':
                # Vérifier si l'utilisateur demandé est un vendeur de son magasin
                if user.role == 'VENDEUR':
                    try:
                        vendeur = user.vendeur
                        responsable = request.user.responsable_magasin
                        if vendeur.magasin not in responsable.magasins_geres.all():
                            return Response(
                                {"error": "Vous ne pouvez voir que les ventes des vendeurs de vos magasins"},
                                status=status.HTTP_403_FORBIDDEN
                            )
                    except:
                        return Response(
                            {"error": "Utilisateur non trouvé ou non autorisé"},
                            status=status.HTTP_403_FORBIDDEN
                        )

            # Récupérer les ventes selon le rôle de l'utilisateur
            if user.role == 'VENDEUR':
                try:
                    vendeur = user.vendeur
                    ventes = Vente.objects.filter(vendeur=vendeur).order_by('-date_vente')
                except:
                    ventes = Vente.objects.none()
            else:
                # Pour les autres rôles, retourner une liste vide ou gérer selon les besoins
                ventes = Vente.objects.none()

            serializer = VenteSerializer(ventes, many=True)
            return Response({
                'user_id': userid,
                'user_name': f"{user.prenom} {user.nom}",
                'role': user.role,
                'total_ventes': ventes.count(),
                'ventes': serializer.data
            })

        except User.DoesNotExist:
            return Response(
                {"error": "Utilisateur non trouvé"},
                status=status.HTTP_404_NOT_FOUND
            )


class ProduitsStockVendeurView(APIView):
    """Vue pour que les vendeurs consultent les produits et leur stock"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les produits et leur stock pour un vendeur",
        manual_parameters=[
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description="Rechercher par nom ou référence de produit",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'categorie',
                openapi.IN_QUERY,
                description="Filtrer par catégorie",
                type=openapi.TYPE_STRING,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Liste des produits avec stock",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            403: "Accès non autorisé"
        }
    )
    def get(self, request):
        # Vérifier que l'utilisateur est un vendeur
        if request.user.role != 'VENDEUR':
            return Response(
                {"error": "Seuls les vendeurs peuvent accéder à cette ressource"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            vendeur = request.user.vendeur
            magasin = vendeur.magasin

            # Importer les modèles nécessaires
            from produit.models import Produit
            from stock_Pme.models import Stock

            # Récupérer les produits du magasin
            produits = Produit.objects.filter(magasin=magasin)

            # Filtres optionnels
            search = request.query_params.get('search')
            if search:
                produits = produits.filter(
                    Q(nom__icontains=search) | Q(reference__icontains=search)
                )

            categorie = request.query_params.get('categorie')
            if categorie:
                produits = produits.filter(categorie__nom__icontains=categorie)

            # Construire la réponse avec les informations de stock
            produits_data = []
            for produit in produits:
                # Récupérer le stock pour ce produit dans ce magasin
                try:
                    stock = Stock.objects.get(produit=produit, magasin=magasin)
                    stock_info = {
                        'quantite_disponible': stock.quantite_disponible,
                        'seuil_alerte': stock.seuil_alerte,
                        'en_alerte': stock.vérifierSeuilAlerte()
                    }
                except Stock.DoesNotExist:
                    stock_info = {
                        'quantite_disponible': 0,
                        'seuil_alerte': 0,
                        'en_alerte': True
                    }

                produit_data = {
                    'id_produit': produit.id_produit,
                    'nom': produit.nom,
                    'reference': produit.reference,
                    'prix_vente': produit.prix_vente,
                    'prix_achat': produit.prix_achat,
                    'TVA': produit.TVA,
                    'unite_mesure': produit.unite_mesure,
                    'description': produit.description,
                    'categorie': produit.categorie.nom if produit.categorie else None,
                    'stock': stock_info
                }
                produits_data.append(produit_data)

            return Response({
                'magasin': magasin.nom,
                'vendeur': f"{vendeur.user.prenom} {vendeur.user.nom}",
                'total_produits': len(produits_data),
                'produits': produits_data
            })

        except Exception as e:
            return Response(
                {"error": f"Erreur lors de la récupération des produits: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class VenteRapideVendeurView(APIView):
    """Vue pour création rapide de vente par un vendeur"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Créer une vente rapidement (auto-remplissage vendeur/magasin)",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['produits', 'mode_paiement'],
            properties={
                'client': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_UUID,
                    nullable=True,
                    description="ID du client (optionnel)"
                ),
                'mode_paiement': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['ESPECES', 'CARTE', 'CHEQUE', 'VIREMENT'],
                    description="Mode de paiement"
                ),
                'client_nom': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    nullable=True,
                    description="Nom du client pour vente anonyme (optionnel)"
                ),
                'client_telephone': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    nullable=True,
                    description="Téléphone du client pour vente anonyme (optionnel)"
                ),
                'produits': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        required=['produit', 'quantite'],
                        properties={
                            'produit': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_UUID,
                                description="ID du produit"
                            ),
                            'quantite': openapi.Schema(
                                type=openapi.TYPE_INTEGER,
                                minimum=1,
                                description="Quantité à vendre"
                            )
                        }
                    ),
                    description="Liste des produits à vendre"
                )
            }
        ),
        responses={
            201: openapi.Response(
                description="Vente créée avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            403: "Accès non autorisé"
        }
    )
    def post(self, request):
        # Vérifier que l'utilisateur est un vendeur
        if request.user.role != 'VENDEUR':
            return Response(
                {"error": "Seuls les vendeurs peuvent créer des ventes via cette interface"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            vendeur = request.user.vendeur
            magasin = vendeur.magasin

            # Récupérer les données de la requête
            client_id = request.data.get('client')
            mode_paiement = request.data.get('mode_paiement', 'ESPECES')
            produits = request.data.get('produits', [])

            if not produits:
                return Response(
                    {"error": "Au moins un produit est requis"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Valider le mode de paiement
            if mode_paiement not in dict(Vente.MODE_PAIEMENT_CHOICES):
                return Response(
                    {"error": "Mode de paiement invalide"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Récupérer le client si spécifié
            client = None
            if client_id:
                try:
                    from clients.models import Client
                    client = Client.objects.get(id=client_id)
                except Client.DoesNotExist:
                    return Response(
                        {"error": "Client non trouvé"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Créer la vente avec auto-remplissage
            vente_data = {
                'client': client,
                'vendeur': vendeur,
                'magasin': magasin,
                'mode_paiement': mode_paiement,
                'entreprise': magasin.entreprise
            }

            vente = Vente.objects.create(**vente_data)

            # Ajouter les produits
            for produit_data in produits:
                try:
                    produit_id = produit_data.get('produit')
                    quantite = produit_data.get('quantite')

                    if not produit_id or not quantite:
                        vente.delete()
                        return Response(
                            {"error": "ID produit et quantité requis pour chaque produit"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    DetailVente.ajouterProduit(vente.id_vente, produit_id, quantite)

                except Exception as e:
                    vente.delete()
                    return Response(
                        {"error": f"Erreur lors de l'ajout du produit: {str(e)}"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Recalculer les montants
            vente.calculer_montant_total()

            # Retourner la vente créée
            serializer = VenteSerializer(vente)
            return Response({
                'message': 'Vente créée avec succès',
                'vente': serializer.data
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": f"Erreur lors de la création de la vente: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TableauBordVendeurView(APIView):
    """Tableau de bord pour les vendeurs avec statistiques et actions rapides"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer le tableau de bord du vendeur",
        manual_parameters=[
            openapi.Parameter(
                'periode',
                openapi.IN_QUERY,
                description="Période pour les statistiques (jour, semaine, mois, annee)",
                type=openapi.TYPE_STRING,
                required=False,
                default='mois'
            )
        ],
        responses={
            200: openapi.Response(
                description="Tableau de bord du vendeur",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            403: "Accès non autorisé"
        }
    )
    def get(self, request):
        # Vérifier que l'utilisateur est un vendeur
        if request.user.role != 'VENDEUR':
            return Response(
                {"error": "Seuls les vendeurs peuvent accéder à ce tableau de bord"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            vendeur = request.user.vendeur
            magasin = vendeur.magasin

            # Récupérer la période demandée
            periode = request.query_params.get('periode', 'mois')

            # Importer les modules nécessaires
            from django.utils import timezone
            from datetime import datetime, timedelta
            from django.db.models import Sum, Count, Avg

            # Calculer les dates selon la période
            now = timezone.now()
            if periode == 'jour':
                date_debut = now.replace(hour=0, minute=0, second=0, microsecond=0)
            elif periode == 'semaine':
                date_debut = now - timedelta(days=now.weekday())
                date_debut = date_debut.replace(hour=0, minute=0, second=0, microsecond=0)
            elif periode == 'mois':
                date_debut = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            elif periode == 'annee':
                date_debut = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                date_debut = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # Récupérer les ventes du vendeur pour la période
            ventes_periode = Vente.objects.filter(
                vendeur=vendeur,
                date_vente__gte=date_debut
            )

            # Calculer les statistiques
            stats_periode = ventes_periode.aggregate(
                total_ventes=Count('id_vente'),
                chiffre_affaires=Sum('montant_total'),
                panier_moyen=Avg('montant_total')
            )

            # Statistiques globales du vendeur
            stats_globales = Vente.objects.filter(vendeur=vendeur).aggregate(
                total_ventes_global=Count('id_vente'),
                chiffre_affaires_global=Sum('montant_total')
            )

            # Top 5 des produits vendus dans la période
            from django.db.models import F
            top_produits = DetailVente.objects.filter(
                vente__vendeur=vendeur,
                vente__date_vente__gte=date_debut
            ).values(
                'produit__nom',
                'produit__reference'
            ).annotate(
                quantite_totale=Sum('quantite_vendue'),
                ca_produit=Sum('montant_total')
            ).order_by('-quantite_totale')[:5]

            # Ventes récentes (5 dernières)
            ventes_recentes = Vente.objects.filter(
                vendeur=vendeur
            ).order_by('-date_vente')[:5]

            ventes_recentes_data = []
            for vente in ventes_recentes:
                ventes_recentes_data.append({
                    'id_vente': vente.id_vente,
                    'numero_facture': vente.numero_facture,
                    'montant_total': vente.montant_total,
                    'date_vente': vente.date_vente,
                    'client': f"{vente.client.nom} {vente.client.prenom}" if vente.client else "Client anonyme",
                    'mode_paiement': vente.get_mode_paiement_display()
                })

            # Objectifs et performance (exemple)
            # Vous pouvez ajuster selon vos besoins métier
            objectif_mensuel = 50000  # Exemple d'objectif
            progression_objectif = (stats_periode['chiffre_affaires'] or 0) / objectif_mensuel * 100 if objectif_mensuel > 0 else 0

            # Actions rapides disponibles
            actions_rapides = [
                {
                    'nom': 'Nouvelle vente',
                    'description': 'Créer une nouvelle vente rapidement',
                    'endpoint': '/api/ventes/vendeur/vente-rapide/',
                    'methode': 'POST'
                },
                {
                    'nom': 'Consulter produits',
                    'description': 'Voir les produits et leur stock',
                    'endpoint': '/api/ventes/vendeur/produits/',
                    'methode': 'GET'
                },
                {
                    'nom': 'Mes ventes',
                    'description': 'Voir toutes mes ventes',
                    'endpoint': f'/api/ventes/user/{request.user.id}/',
                    'methode': 'GET'
                }
            ]

            return Response({
                'vendeur': {
                    'nom': f"{vendeur.user.prenom} {vendeur.user.nom}",
                    'code_vendeur': vendeur.code_vendeur,
                    'magasin': magasin.nom,
                    'taux_commission': vendeur.taux_commission
                },
                'periode': {
                    'type': periode,
                    'date_debut': date_debut,
                    'date_fin': now
                },
                'statistiques_periode': {
                    'total_ventes': stats_periode['total_ventes'] or 0,
                    'chiffre_affaires': float(stats_periode['chiffre_affaires'] or 0),
                    'panier_moyen': float(stats_periode['panier_moyen'] or 0)
                },
                'statistiques_globales': {
                    'total_ventes': stats_globales['total_ventes_global'] or 0,
                    'chiffre_affaires': float(stats_globales['chiffre_affaires_global'] or 0)
                },
                'performance': {
                    'objectif_mensuel': objectif_mensuel,
                    'progression_objectif': round(progression_objectif, 2)
                },
                'top_produits': list(top_produits),
                'ventes_recentes': ventes_recentes_data,
                'actions_rapides': actions_rapides
            })

        except Exception as e:
            return Response(
                {"error": f"Erreur lors de la récupération du tableau de bord: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InfosPreRemplissageView(APIView):
    """Endpoint pour obtenir les informations de pré-remplissage selon le rôle"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Obtenir les informations de pré-remplissage pour les formulaires",
        responses={
            200: openapi.Response(
                description="Informations de pré-remplissage",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            )
        }
    )
    def get(self, request):
        try:
            user = request.user
            infos = {
                'user': {
                    'id': user.id,
                    'nom': user.nom,
                    'prenom': user.prenom,
                    'role': user.role
                },
                'auto_remplissage': {},
                'options_disponibles': {}
            }

            # Informations spécifiques selon le rôle
            if user.role == 'VENDEUR':
                try:
                    vendeur = user.vendeur
                    infos['auto_remplissage'] = {
                        'vendeur_id': vendeur.id,
                        'vendeur_nom': f"{user.prenom} {user.nom}",
                        'magasin_id': vendeur.magasin.id,
                        'magasin_nom': vendeur.magasin.nom,
                        'code_vendeur': vendeur.code_vendeur
                    }

                    # Options disponibles pour un vendeur (limitées à son magasin)
                    from produit.models import Produit
                    from clients.models import Client

                    produits = Produit.objects.filter(magasin=vendeur.magasin)
                    clients = Client.objects.filter(magasin=vendeur.magasin)

                    infos['options_disponibles'] = {
                        'produits': [
                            {
                                'id': p.id_produit,
                                'nom': p.nom,
                                'reference': p.reference,
                                'prix_vente': p.prix_vente
                            } for p in produits[:20]  # Limiter pour les performances
                        ],
                        'clients': [
                            {
                                'id': c.id,
                                'nom': f"{c.nom} {c.prenom}",
                                'telephone': c.telephone
                            } for c in clients[:20]
                        ],
                        'modes_paiement': [
                            {'value': key, 'label': value}
                            for key, value in Vente.MODE_PAIEMENT_CHOICES
                        ]
                    }
                except:
                    infos['error'] = "Profil vendeur non trouvé"

            elif user.role == 'RESPONSABLE_MAGASIN':
                try:
                    responsable = user.responsable_magasin
                    magasins = responsable.magasins_geres.all()

                    infos['options_disponibles'] = {
                        'magasins': [
                            {
                                'id': m.id,
                                'nom': m.nom
                            } for m in magasins
                        ],
                        'modes_paiement': [
                            {'value': key, 'label': value}
                            for key, value in Vente.MODE_PAIEMENT_CHOICES
                        ]
                    }
                except:
                    infos['error'] = "Profil responsable non trouvé"

            elif user.role == 'ADMIN':
                # Admin a accès à tout
                from magasins.models import Magasin
                magasins = Magasin.objects.all()

                infos['options_disponibles'] = {
                    'magasins': [
                        {
                            'id': m.id,
                            'nom': m.nom
                        } for m in magasins
                    ],
                    'modes_paiement': [
                        {'value': key, 'label': value}
                        for key, value in Vente.MODE_PAIEMENT_CHOICES
                    ]
                }

            return Response(infos)

        except Exception as e:
            return Response(
                {"error": f"Erreur lors de la récupération des informations: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )