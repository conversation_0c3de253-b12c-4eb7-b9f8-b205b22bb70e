from django.urls import path
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .views import get_notes, RegisterView, LoginView,LogoutView, RefreshTokenView ,DeleteUserView

urlpatterns = [
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),    
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('delete/', DeleteUserView.as_view(), name='delete_user_legacy'),
    path('delete/<uuid:user_id>/', DeleteUserView.as_view(), name='delete_user'),
    path('notes/', get_notes, name='notes'),
]