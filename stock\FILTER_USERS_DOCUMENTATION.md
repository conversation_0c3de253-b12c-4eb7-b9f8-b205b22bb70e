# Documentation - Filtrage des Utilisateurs par Entreprise et Magasin

## Vue d'ensemble

Cette fonctionnalité permet de filtrer et récupérer les utilisateurs selon différents critères :
- Par entreprise
- Par magasin
- Filtrage avancé avec combinaison de critères

## Endpoints Disponibles

### 1. Filtrage par Entreprise

**Endpoint:** `GET /api/personalisation-user/filter-by-entreprise/`

**Paramètres:**
- `entreprise_id` (requis) : UUID de l'entreprise

**Réponse:**
```json
{
    "entreprise": {
        "id": "uuid-entreprise",
        "nom": "Nom de l'entreprise"
    },
    "total_users": 4,
    "users": [
        {
            "id_utilisateur": "uuid-user",
            "username": "admin_user",
            "nom": "Nom",
            "prenom": "Prénom",
            "email": "<EMAIL>",
            "role": "ADMIN",
            "entreprise": "uuid-entreprise",
            "magasin_id": null,
            "magasin_nom": null
        }
    ]
}
```

**Utilisateurs retournés:**
- <PERSON><PERSON> (via UserProfile)
- Responsables magasin (via leurs magasins)
- Vendeurs (via leurs magasins)
- Responsables entrepôt (via leurs entrepôts)

### 2. Filtrage par Magasin

**Endpoint:** `GET /api/personalisation-user/filter-by-magasin/`

**Paramètres:**
- `magasin_id` (requis) : UUID du magasin

**Réponse:**
```json
{
    "magasin": {
        "id": "uuid-magasin",
        "nom": "Nom du magasin",
        "entreprise": {
            "id": "uuid-entreprise",
            "nom": "Nom de l'entreprise"
        }
    },
    "total_users": 2,
    "users": [
        {
            "id_utilisateur": "uuid-user",
            "username": "resp_magasin",
            "nom": "Responsable",
            "prenom": "Magasin",
            "role": "RESPONSABLE_MAGASIN",
            "magasin_id": "uuid-magasin",
            "magasin_nom": "Nom du magasin"
        }
    ]
}
```

**Utilisateurs retournés:**
- Responsables magasin du magasin spécifié
- Vendeurs du magasin spécifié

### 3. Filtrage Avancé

**Endpoint:** `GET /api/personalisation-user/filter-advanced/`

**Paramètres:**
- `entreprise_id` (optionnel) : UUID de l'entreprise
- `magasin_id` (optionnel) : UUID du magasin
- `role` (optionnel) : Rôle à filtrer (ADMIN, RESPONSABLE_MAGASIN, VENDEUR, RESPONSABLE_ENTREPOT)
- `actif` (optionnel) : Statut actif (true/false)

**Note:** Au moins `entreprise_id` ou `magasin_id` doit être fourni.

**Réponse:**
```json
{
    "entreprise": {
        "id": "uuid-entreprise",
        "nom": "Nom de l'entreprise"
    },
    "filters_applied": {
        "role": "RESPONSABLE_MAGASIN",
        "actif": null
    },
    "total_users": 1,
    "users": [...]
}
```

## Relations entre Utilisateurs et Entités

### Schéma des Relations

```
Entreprise
├── UserProfile (Admins)
├── Magasins
│   ├── Responsable_magasin
│   └── Vendeurs
└── Entrepôts
    └── ResponsableEntrepot
```

### Logique de Filtrage

1. **Par Entreprise:**
   - Admins : `User.profile.entreprise = entreprise`
   - Responsables magasin : `User.responsable_magasin.magasin.entreprise = entreprise`
   - Vendeurs : `User.vendeur.magasin.entreprise = entreprise`
   - Responsables entrepôt : `User.responsableentrepot.entrepot.entreprise = entreprise`

2. **Par Magasin:**
   - Responsables magasin : `User.responsable_magasin.magasin = magasin`
   - Vendeurs : `User.vendeur.magasin = magasin`

## Exemples d'Utilisation

### Exemple 1: Récupérer tous les utilisateurs d'une entreprise

```bash
curl -X GET "http://localhost:8000/api/personalisation-user/filter-by-entreprise/?entreprise_id=123e4567-e89b-12d3-a456-************" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### Exemple 2: Récupérer les utilisateurs d'un magasin

```bash
curl -X GET "http://localhost:8000/api/personalisation-user/filter-by-magasin/?magasin_id=123e4567-e89b-12d3-a456-426614174001" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### Exemple 3: Filtrage avancé - Responsables magasin d'une entreprise

```bash
curl -X GET "http://localhost:8000/api/personalisation-user/filter-advanced/?entreprise_id=123e4567-e89b-12d3-a456-************&role=RESPONSABLE_MAGASIN" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### Exemple 4: Filtrage avancé - Vendeurs actifs d'un magasin

```bash
curl -X GET "http://localhost:8000/api/personalisation-user/filter-advanced/?magasin_id=123e4567-e89b-12d3-a456-426614174001&role=VENDEUR&actif=true" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## Permissions

- **Authentification requise:** Oui
- **Rôle requis:** ADMIN
- Seuls les administrateurs peuvent accéder à ces endpoints

## Gestion des Erreurs

### Codes d'Erreur Possibles

- `400 Bad Request` : Paramètres manquants ou invalides
- `401 Unauthorized` : Token d'authentification manquant ou invalide
- `403 Forbidden` : Permissions insuffisantes (non-admin)
- `404 Not Found` : Entreprise ou magasin non trouvé

### Exemples de Réponses d'Erreur

```json
{
    "error": "ID entreprise requis"
}
```

```json
{
    "error": "Entreprise non trouvée"
}
```

```json
{
    "error": "Au moins un paramètre entreprise_id ou magasin_id est requis"
}
```

## Tests

### Exécuter les Tests

```bash
# Tests unitaires
python manage.py test personalisation_user.test_filter_users

# Script de démonstration
python demo_filter_users.py
```

### Données de Test

Le script `demo_filter_users.py` crée automatiquement :
- 1 entreprise "Demo Entreprise"
- 1 entrepôt "Demo Entrepôt"
- 1 magasin "Demo Magasin"
- 4 utilisateurs (admin, responsable magasin, responsable entrepôt, vendeur)

## Intégration Frontend

### Exemple avec JavaScript/Fetch

```javascript
// Récupérer les utilisateurs d'une entreprise
async function getUsersByEntreprise(entrepriseId, token) {
    const response = await fetch(
        `/api/personalisation-user/filter-by-entreprise/?entreprise_id=${entrepriseId}`,
        {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        }
    );
    
    if (response.ok) {
        return await response.json();
    } else {
        throw new Error(`Erreur: ${response.status}`);
    }
}

// Utilisation
getUsersByEntreprise('123e4567-e89b-12d3-a456-************', 'your-token')
    .then(data => {
        console.log(`${data.total_users} utilisateurs trouvés`);
        data.users.forEach(user => {
            console.log(`${user.prenom} ${user.nom} (${user.role})`);
        });
    })
    .catch(error => console.error(error));
```

## Notes Techniques

- Les utilisateurs sont triés par nom puis prénom
- Les doublons sont automatiquement supprimés
- Les relations sont optimisées avec `select_related` et `prefetch_related`
- Support des UUID pour tous les identifiants
- Validation stricte des paramètres d'entrée
