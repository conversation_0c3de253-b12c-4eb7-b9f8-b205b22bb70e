from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import ResponsableEntrepot
from entrepot.models import Entrepot

User = get_user_model()


class ResponsableEntrepotModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='resp_entrepot',
            telephone='1234567890',
            nom='<PERSON><PERSON>',
            prenom='Jean',
            email='<EMAIL>',
            adresse='123 Rue Test',
            role=User.Role.RESPONSABLE_ENTREPOT
        )
        self.entrepot = Entrepot.objects.create(
            nom='Entrepôt Test',
            adresse='456 Avenue Test',
            capacite_stockage=1000
        )

    def test_create_responsable_entrepot(self):
        responsable = ResponsableEntrepot.objects.create(
            user=self.user,
            entrepot=self.entrepot,
            date_embauche='2024-01-01'
        )
        self.assertTrue(responsable.code_responsable.startswith('RESP-ENT-'))
        self.assertEqual(responsable.user, self.user)
        self.assertEqual(responsable.entrepot, self.entrepot)
        self.assertTrue(responsable.actif)

    def test_str_representation(self):
        responsable = ResponsableEntrepot.objects.create(
            user=self.user,
            entrepot=self.entrepot,
            date_embauche='2024-01-01'
        )
        expected = f"Jean Dupont - Entrepôt Test ({responsable.code_responsable})"
        self.assertEqual(str(responsable), expected)

    def test_get_performance(self):
        responsable = ResponsableEntrepot.objects.create(
            user=self.user,
            entrepot=self.entrepot,
            date_embauche='2024-01-01'
        )
        performance = responsable.get_performance()
        self.assertIn('total_produits_geres', performance)
        self.assertIn('stocks_en_alerte', performance)
        self.assertIn('taux_alerte', performance)


class ResponsableEntrepotAPITest(APITestCase):
    def setUp(self):
        self.admin_user = User.objects.create_user(
            username='admin',
            telephone='0987654321',
            nom='Admin',
            prenom='Super',
            email='<EMAIL>',
            adresse='789 Admin Street',
            role=User.Role.ADMIN
        )
        self.user = User.objects.create_user(
            username='resp_entrepot',
            telephone='1234567890',
            nom='Dupont',
            prenom='Jean',
            email='<EMAIL>',
            adresse='123 Rue Test',
            role=User.Role.RESPONSABLE_ENTREPOT
        )
        self.entrepot = Entrepot.objects.create(
            nom='Entrepôt Test',
            adresse='456 Avenue Test',
            capacite_stockage=1000
        )

    def test_create_responsable_entrepot(self):
        self.client.force_authenticate(user=self.admin_user)
        data = {
            'user': self.user.id_utilisateur,
            'entrepot': self.entrepot.id_entrepot,
            'date_embauche': '2024-01-01',
            'notes': 'Test notes'
        }
        response = self.client.post('/responsables-entrepot/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(ResponsableEntrepot.objects.filter(user=self.user).exists())

    def test_list_responsables_entrepot(self):
        ResponsableEntrepot.objects.create(
            user=self.user,
            entrepot=self.entrepot,
            date_embauche='2024-01-01'
        )
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get('/responsables-entrepot/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
