import uuid
from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class NotificationType(models.Model):
    """Types de notifications disponibles dans le système"""
    
    PRIORITY_CHOICES = [
        ('LOW', 'Basse'),
        ('MEDIUM', '<PERSON>yenne'),
        ('HIGH', 'Haute'),
        ('CRITICAL', 'Critique'),
    ]
    
    CATEGORY_CHOICES = [
        ('STOCK', 'Gestion de Stock'),
        ('VENTE', 'Ventes'),
        ('ACHAT', 'Achats'),
        ('INVENTAIRE', 'Inventaire'),
        ('FINANCE', 'Finance'),
        ('SYSTEM', 'Système'),
        ('SECURITY', 'Sécurité'),
        ('USER', 'Utilisateur'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    categorie = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    priorite_defaut = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')
    template_email = models.TextField(blank=True, help_text="Template HTML pour email")
    template_sms = models.CharField(max_length=160, blank=True, help_text="Template SMS (160 caractères max)")
    template_push = models.CharField(max_length=100, blank=True, help_text="Template notification push")
    template_in_app = models.TextField(blank=True, help_text="Template notification in-app")
    actif = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Type de Notification"
        verbose_name_plural = "Types de Notifications"
        indexes = [
            models.Index(fields=['categorie']),
            models.Index(fields=['priorite_defaut']),
        ]

    def __str__(self):
        return f"{self.nom} ({self.get_categorie_display()})"


class NotificationPreference(models.Model):
    """Préférences de notification par utilisateur"""
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notification_preferences'
    )
    
    # Canaux de notification activés
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)
    
    # Préférences par catégorie
    stock_notifications = models.BooleanField(default=True)
    vente_notifications = models.BooleanField(default=True)
    achat_notifications = models.BooleanField(default=True)
    inventaire_notifications = models.BooleanField(default=True)
    finance_notifications = models.BooleanField(default=True)
    system_notifications = models.BooleanField(default=True)
    security_notifications = models.BooleanField(default=True)
    
    # Horaires de notification
    quiet_hours_start = models.TimeField(default='22:00', help_text="Début des heures silencieuses")
    quiet_hours_end = models.TimeField(default='08:00', help_text="Fin des heures silencieuses")
    weekend_notifications = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Préférence de Notification"
        verbose_name_plural = "Préférences de Notifications"

    def __str__(self):
        return f"Préférences de {self.user.username}"

    def is_notification_allowed(self, notification_type, current_time=None):
        """Vérifie si une notification est autorisée selon les préférences"""
        if current_time is None:
            current_time = timezone.now()
        
        # Vérifier les heures silencieuses
        current_time_only = current_time.time()
        if self.quiet_hours_start <= self.quiet_hours_end:
            # Heures silencieuses dans la même journée
            if self.quiet_hours_start <= current_time_only <= self.quiet_hours_end:
                return False
        else:
            # Heures silencieuses à cheval sur deux jours
            if current_time_only >= self.quiet_hours_start or current_time_only <= self.quiet_hours_end:
                return False
        
        # Vérifier les weekends
        if not self.weekend_notifications and current_time.weekday() >= 5:
            return False
        
        # Vérifier les préférences par catégorie
        category_preferences = {
            'STOCK': self.stock_notifications,
            'VENTE': self.vente_notifications,
            'ACHAT': self.achat_notifications,
            'INVENTAIRE': self.inventaire_notifications,
            'FINANCE': self.finance_notifications,
            'SYSTEM': self.system_notifications,
            'SECURITY': self.security_notifications,
        }
        
        return category_preferences.get(notification_type.categorie, True)


class Notification(models.Model):
    """Notification envoyée à un utilisateur"""
    
    STATUS_CHOICES = [
        ('PENDING', 'En attente'),
        ('SENT', 'Envoyée'),
        ('DELIVERED', 'Délivrée'),
        ('READ', 'Lue'),
        ('FAILED', 'Échec'),
        ('CANCELLED', 'Annulée'),
    ]
    
    CHANNEL_CHOICES = [
        ('EMAIL', 'Email'),
        ('SMS', 'SMS'),
        ('PUSH', 'Push'),
        ('IN_APP', 'In-App'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type_notification = models.ForeignKey(NotificationType, on_delete=models.CASCADE)
    destinataire = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    
    # Contenu de la notification
    titre = models.CharField(max_length=200)
    message = models.TextField()
    donnees_contexte = models.JSONField(default=dict, blank=True, help_text="Données contextuelles pour les templates")
    
    # Référence à l'objet lié (optionnel)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.CharField(max_length=100, null=True, blank=True)
    objet_lie = GenericForeignKey('content_type', 'object_id')
    
    # Statut et canal
    canal = models.CharField(max_length=10, choices=CHANNEL_CHOICES)
    statut = models.CharField(max_length=15, choices=STATUS_CHOICES, default='PENDING')
    priorite = models.CharField(max_length=10, choices=NotificationType.PRIORITY_CHOICES)
    
    # Dates importantes
    date_creation = models.DateTimeField(auto_now_add=True)
    date_envoi = models.DateTimeField(null=True, blank=True)
    date_lecture = models.DateTimeField(null=True, blank=True)
    date_expiration = models.DateTimeField(null=True, blank=True)
    
    # Métadonnées
    tentatives_envoi = models.PositiveIntegerField(default=0)
    erreur_envoi = models.TextField(blank=True)
    identifiant_externe = models.CharField(max_length=200, blank=True, help_text="ID du service externe (Twilio, Firebase, etc.)")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
        indexes = [
            models.Index(fields=['destinataire', 'statut']),
            models.Index(fields=['type_notification']),
            models.Index(fields=['canal']),
            models.Index(fields=['priorite']),
            models.Index(fields=['date_creation']),
            models.Index(fields=['date_expiration']),
        ]
        ordering = ['-date_creation']

    def __str__(self):
        return f"{self.titre} - {self.destinataire.username} ({self.get_canal_display()})"

    def clean(self):
        if self.date_expiration and self.date_expiration <= timezone.now():
            raise ValidationError("La date d'expiration ne peut pas être dans le passé.")

    def marquer_comme_lue(self):
        """Marque la notification comme lue"""
        if self.statut in ['SENT', 'DELIVERED']:
            self.statut = 'READ'
            self.date_lecture = timezone.now()
            self.save(update_fields=['statut', 'date_lecture', 'updated_at'])

    def marquer_comme_envoyee(self, identifiant_externe=None):
        """Marque la notification comme envoyée"""
        self.statut = 'SENT'
        self.date_envoi = timezone.now()
        if identifiant_externe:
            self.identifiant_externe = identifiant_externe
        self.save(update_fields=['statut', 'date_envoi', 'identifiant_externe', 'updated_at'])

    def marquer_comme_echouee(self, erreur):
        """Marque la notification comme échouée"""
        self.statut = 'FAILED'
        self.erreur_envoi = str(erreur)
        self.tentatives_envoi += 1
        self.save(update_fields=['statut', 'erreur_envoi', 'tentatives_envoi', 'updated_at'])

    @property
    def est_expiree(self):
        """Vérifie si la notification est expirée"""
        if self.date_expiration:
            return timezone.now() > self.date_expiration
        return False

    @property
    def est_lue(self):
        """Vérifie si la notification a été lue"""
        return self.statut == 'READ'


class NotificationBatch(models.Model):
    """Lot de notifications pour envoi groupé"""
    
    STATUS_CHOICES = [
        ('PENDING', 'En attente'),
        ('PROCESSING', 'En cours'),
        ('COMPLETED', 'Terminé'),
        ('FAILED', 'Échec'),
        ('CANCELLED', 'Annulé'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    type_notification = models.ForeignKey(NotificationType, on_delete=models.CASCADE)
    
    # Critères de sélection des destinataires
    criteres_selection = models.JSONField(default=dict, help_text="Critères pour sélectionner les destinataires")
    
    # Statut du lot
    statut = models.CharField(max_length=15, choices=STATUS_CHOICES, default='PENDING')
    total_destinataires = models.PositiveIntegerField(default=0)
    notifications_envoyees = models.PositiveIntegerField(default=0)
    notifications_echouees = models.PositiveIntegerField(default=0)
    
    # Planification
    date_planification = models.DateTimeField(null=True, blank=True)
    date_debut_traitement = models.DateTimeField(null=True, blank=True)
    date_fin_traitement = models.DateTimeField(null=True, blank=True)
    
    # Créateur du lot
    createur = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Lot de Notifications"
        verbose_name_plural = "Lots de Notifications"
        indexes = [
            models.Index(fields=['statut']),
            models.Index(fields=['date_planification']),
            models.Index(fields=['createur']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.nom} - {self.total_destinataires} destinataires"

    @property
    def taux_succes(self):
        """Calcule le taux de succès de l'envoi"""
        if self.total_destinataires == 0:
            return 0
        return (self.notifications_envoyees / self.total_destinataires) * 100

    @property
    def taux_echec(self):
        """Calcule le taux d'échec de l'envoi"""
        if self.total_destinataires == 0:
            return 0
        return (self.notifications_echouees / self.total_destinataires) * 100
