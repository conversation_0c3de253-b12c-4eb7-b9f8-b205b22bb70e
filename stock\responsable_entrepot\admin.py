from django.contrib import admin
from .models import ResponsableEntrepot


@admin.register(ResponsableEntrepot)
class ResponsableEntrepotAdmin(admin.ModelAdmin):
    list_display = [
        'code_responsable', 'user_nom_complet', 'entrepot', 
        'date_embauche', 'actif', 'created_at'
    ]
    list_filter = ['actif', 'date_embauche', 'entrepot', 'created_at']
    search_fields = [
        'code_responsable', 'user__nom', 'user__prenom', 
        'user__email', 'entrepot__nom'
    ]
    readonly_fields = ['code_responsable', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Informations de base', {
            'fields': ('user', 'entrepot', 'code_responsable')
        }),
        ('Détails professionnels', {
            'fields': ('date_embauche', 'notes', 'actif')
        }),
        ('Métadonnées', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def user_nom_complet(self, obj):
        return f"{obj.user.prenom} {obj.user.nom}"
    user_nom_complet.short_description = "Nom complet"
    user_nom_complet.admin_order_field = 'user__nom'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'entrepot')

    def save_model(self, request, obj, form, change):
        if not change:  # Nouveau responsable
            # Le code sera généré automatiquement par le modèle
            pass
        super().save_model(request, obj, form, change)
