from django.core.management.base import BaseCommand
from notifications.models import NotificationType

class Command(BaseCommand):
    help = 'Initialise les types de notifications par défaut'

    def handle(self, *args, **kwargs):
        notification_types = [
            {
                'nom': 'BIENVENUE',
                'description': 'Notification de bienvenue pour les nouveaux utilisateurs',
                'categorie': 'USER',
                'priorite_defaut': 'LOW',
                'template_email': 'Bienvenue sur notre plateforme !',
                'template_sms': 'Bienvenue sur notre plateforme !',
                'template_push': 'Bienvenue !',
                'template_in_app': 'Bienvenue sur notre plateforme !',
                'actif': True
            },
            {
                'nom': 'ALERTE_STOCK',
                'description': 'Alerte de niveau de stock bas',
                'categorie': 'STOCK',
                'priorite_defaut': 'HIGH',
                'template_email': 'Le niveau de stock est bas pour {produit}',
                'template_sms': 'Stock bas: {produit}',
                'template_push': 'Stock bas: {produit}',
                'template_in_app': 'Le niveau de stock est bas pour {produit}',
                'actif': True
            },
            {
                'nom': 'COMMANDE_RECUE',
                'description': 'Notification de commande reçue',
                'categorie': 'VENTE',
                'priorite_defaut': 'MEDIUM',
                'template_email': 'Nouvelle commande reçue : {commande}',
                'template_sms': 'Nouvelle commande: {commande}',
                'template_push': 'Nouvelle commande',
                'template_in_app': 'Nouvelle commande reçue : {commande}',
                'actif': True
            },
            {
                'nom': 'LIVRAISON',
                'description': 'Notification de livraison',
                'categorie': 'VENTE',
                'priorite_defaut': 'MEDIUM',
                'template_email': 'Livraison en cours pour la commande {commande}',
                'template_sms': 'Livraison: {commande}',
                'template_push': 'Livraison en cours',
                'template_in_app': 'Livraison en cours pour la commande {commande}',
                'actif': True
            }
        ]

        for type_data in notification_types:
            NotificationType.objects.get_or_create(
                nom=type_data['nom'],
                defaults={
                    'description': type_data['description'],
                    'categorie': type_data['categorie'],
                    'priorite_defaut': type_data['priorite_defaut'],
                    'template_email': type_data['template_email'],
                    'template_sms': type_data['template_sms'],
                    'template_push': type_data['template_push'],
                    'template_in_app': type_data['template_in_app'],
                    'actif': type_data['actif']
                }
            )
            self.stdout.write(
                self.style.SUCCESS(f'Type de notification "{type_data["nom"]}" créé avec succès')
            ) 