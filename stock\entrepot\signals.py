from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Entrepot, Location
from stock_Pme.models import Stock
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Entrepot)
def validate_entrepot(sender, instance, **kwargs):
    """
    Valide l'entrepôt avant la sauvegarde
    """
    if instance.capacite_stockage < 0:
        raise ValueError("La capacité de stockage doit être positive")
    
    # Vérifier si le nom est unique
    if Entrepot.objects.filter(nom=instance.nom).exclude(id_entrepot=instance.id_entrepot).exists():
        raise ValueError("Un entrepôt avec ce nom existe déjà")

@receiver(post_save, sender=Entrepot)
def create_locations_for_entrepot(sender, instance, created, **kwargs):
    """
    Crée automatiquement les locations pour chaque type de stock lors de la création d'un entrepôt
    """
    if created:
        try:
            with transaction.atomic():
                for type_stock, type_display in instance.TYPES_STOCK:
                    Location.objects.create(
                        entrepot=instance,
                        nom=f"Zone {type_display}",
                        description=f"Zone dédiée aux {type_display.lower()}",
                        type_stock=type_stock
                    )
                logger.info(f"Locations créées pour l'entrepôt {instance.nom}")
        except Exception as e:
            logger.error(f"Erreur lors de la création des locations: {str(e)}")

@receiver(post_delete, sender=Entrepot)
def handle_entrepot_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'un entrepôt
    """
    try:
        with transaction.atomic():
            # Mettre à jour les stocks associés à cet entrepôt
            stocks = Stock.objects.filter(entrepot=instance)
            for stock in stocks:
                # Trouver un autre entrepôt compatible
                autre_entrepot = Entrepot.objects.filter(
                    statut=True,
                    type_stock=stock.produit.type_stock
                ).exclude(id_entrepot=instance.id_entrepot).first()
                
                if autre_entrepot:
                    stock.entrepot = autre_entrepot
                    stock.save(update_fields=['entrepot'])
                    logger.info(f"Stock {stock.id} déplacé vers l'entrepôt {autre_entrepot.nom}")
                else:
                    logger.warning(f"Aucun entrepôt alternatif trouvé pour le stock {stock.id}")
            
            logger.info(f"Entrepôt {instance.nom} supprimé avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de l'entrepôt: {str(e)}")

@receiver(pre_save, sender=Location)
def validate_location(sender, instance, **kwargs):
    """
    Valide la location avant la sauvegarde
    """
    if not instance.entrepot.verifier_compatibilite_stock(instance.type_stock):
        raise ValueError(
            f"Cette location ne peut pas stocker des {instance.get_type_stock_display()}. "
            f"L'entrepôt n'accepte que : {', '.join([type_display for _, type_display in instance.entrepot.TYPES_STOCK])}"
        )

@receiver(post_delete, sender=Location)
def handle_location_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'une location
    """
    try:
        with transaction.atomic():
            # Mettre à jour les stocks associés à cette location
            stocks = Stock.objects.filter(location=instance)
            for stock in stocks:
                # Trouver une autre location compatible dans le même entrepôt
                autre_location = Location.objects.filter(
                    entrepot=instance.entrepot,
                    type_stock=stock.produit.type_stock
                ).exclude(id=instance.id).first()
                
                if autre_location:
                    stock.location = autre_location
                    stock.save(update_fields=['location'])
                    logger.info(f"Stock {stock.id} déplacé vers la location {autre_location.nom}")
                else:
                    logger.warning(f"Aucune location alternative trouvée pour le stock {stock.id}")
            
            logger.info(f"Location {instance.nom} supprimée avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de la location: {str(e)}") 