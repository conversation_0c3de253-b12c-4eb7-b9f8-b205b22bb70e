from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .serializers import ResponsableMagasinSerializer, ResponsableMagasinCreateSerializer, ResponsableMagasinUpdateSerializer
from .models import Responsable_magasin
from base.views import IsAdmin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


class ResponsableMagasinListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Lister tous les responsables de magasin",
        responses={
            200: ResponsableMagasinSerializer(many=True),
            400: "Bad Request"
        }
    )
    def get(self, request):
        responsables_magasin = Responsable_magasin.objects.all()
        serializer = ResponsableMagasinSerializer(responsables_magasin, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau responsable de magasin",
        request_body=ResponsableMagasinCreateSerializer,
        responses={
            201: openapi.Response(
                description="Responsable de magasin créé avec succès",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'responsable': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        """Créer un nouveau responsable magasin avec son utilisateur"""
        serializer = ResponsableMagasinCreateSerializer(data=request.data)
        if serializer.is_valid():
            responsable = serializer.save()
            # Retourner les détails du responsable créé
            response_serializer = ResponsableMagasinSerializer(responsable)
            return Response({
                'responsable': response_serializer.data,
                'message': 'Responsable magasin créé avec succès'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'errors': serializer.errors,
            'message': 'Erreur lors de la création du responsable magasin'
        }, status=status.HTTP_400_BAD_REQUEST)

class ResponsableMagasinDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un responsable de magasin spécifique",
        responses={
            200: openapi.Response(
                description="Détails du responsable de magasin",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            reponsable_magasin = Responsable_magasin.objects.get(id=id)
            serializer = ResponsableMagasinSerializer(reponsable_magasin)
            return Response(serializer.data)
        except Responsable_magasin.DoesNotExist:
            return Response({"error": "Responsable magasin non trouvé"}, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un responsable de magasin existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'date_embauche': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'notes': openapi.Schema(type=openapi.TYPE_STRING),
                'user_data': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Données utilisateur à mettre à jour",
                    properties={
                        'nom': openapi.Schema(type=openapi.TYPE_STRING),
                        'prenom': openapi.Schema(type=openapi.TYPE_STRING),
                        'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                        'telephone': openapi.Schema(type=openapi.TYPE_STRING),
                        'adresse': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            }
        ),
        responses={
            200: openapi.Response(
                description="Responsable de magasin mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            reponsable_magasin = Responsable_magasin.objects.get(id=id)
            serializer = ResponsableMagasinUpdateSerializer(reponsable_magasin, data=request.data, partial=True)
            if serializer.is_valid():
                updated_responsable = serializer.save()
                # Retourner la réponse avec le serializer de lecture
                response_serializer = ResponsableMagasinSerializer(updated_responsable)
                return Response(response_serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Responsable_magasin.DoesNotExist:
            return Response({"error": "Responsable magasin non trouvé"}, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un responsable de magasin",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            reponsable_magasin = Responsable_magasin.objects.get(id=id)
            reponsable_magasin.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Responsable_magasin.DoesNotExist:
            return Response({"error": "Responsable_magasin"}, status=status.HTTP_404_NOT_FOUND)







