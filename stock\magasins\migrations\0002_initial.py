# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('magasins', '0001_initial'),
        ('responsable_magasin', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='magasin',
            name='responsable_magasin',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='magasins_geres', to='responsable_magasin.responsable_magasin'),
        ),
        migrations.AddIndex(
            model_name='magasin',
            index=models.Index(fields=['nom'], name='magasins_ma_nom_df0e6e_idx'),
        ),
        migrations.AddIndex(
            model_name='magasin',
            index=models.Index(fields=['responsable_magasin'], name='magasins_ma_respons_d93c7d_idx'),
        ),
        migrations.AddIndex(
            model_name='magasin',
            index=models.Index(fields=['entrepot'], name='magasins_ma_entrepo_52c162_idx'),
        ),
    ]
