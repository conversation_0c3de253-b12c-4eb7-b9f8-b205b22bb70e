import uuid
from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator


class User(AbstractUser):
    class Role(models.TextChoices):
        ADMIN = 'ADMIN', _('Admin')
        VENDEUR = 'VENDEUR', _('Vendeur')
        FOURNISSEUR = 'FOURNISSEUR', _('Fournisseur')
        RESPONSABLE_ENTREPOT = 'RESPONSABLE_ENTREPOT', _('Responsable_entrepot')
        RESPONSABLE_MAGASIN = 'RESPONSABLE_MAGASIN', _('Responsable_magasin')

    id_utilisateur = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = models.CharField(max_length=150, unique=True)
    nom = models.CharField(max_length=150)
    prenom = models.Char<PERSON>ield(max_length=150)
    email = models.EmailField(blank=True, null=True)
    telephone = models.CharField(max_length=20 , unique=True)
    adresse = models.TextField()
    role = models.CharField(max_length=20, choices=Role.choices)
    statut = models.BooleanField(default=True)
    createdAt = models.DateTimeField(auto_now_add=True)    
    avatar = models.ImageField(upload_to='avatars/', null = True ,blank = True , validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png'])])
    
    @property
    def avatar_url(self):
        if self.avatar and hasattr(self.avatar, 'url'):
            return self.avatar.url
        return None

    # Retirer les anciens champs d'AbstractUser
    first_name = None
    last_name = None

    USERNAME_FIELD = 'telephone'
    REQUIRED_FIELDS = ['username', 'nom', 'prenom', 'adresse', 'role']

    def __str__(self):
        return f"{self.prenom} {self.nom} ({self.email})"

class Note(models.Model):
    description = models.CharField(max_length=300)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notes')
    
    def __str__(self):
        return f"Note de {self.owner.username}: {self.description[:30]}..."