#!/usr/bin/env python3
"""
Script pour corriger automatiquement les schémas OpenAPI dans toutes les applications Django.
Ce script identifie les incohérences entre les modèles et les schémas OpenAPI définis dans les vues.
"""

import os
import re
import ast
from pathlib import Path

def extract_model_fields(model_file_path):
    """Extrait les champs d'un modèle Django"""
    with open(model_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    fields = {}
    try:
        tree = ast.parse(content)
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                for item in node.body:
                    if isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name) and target.id.endswith('Field'):
                                # C'est un champ de modèle
                                if hasattr(item, 'value') and hasattr(item.value, 'func'):
                                    field_name = item.value.func.attr if hasattr(item.value.func, 'attr') else 'Unknown'
                                    fields[target.id] = {
                                        'type': field_name,
                                        'args': []
                                    }
    except:
        pass
    
    return fields

def extract_openapi_schemas(views_file_path):
    """Extrait les schémas OpenAPI d'un fichier de vues"""
    with open(views_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    schemas = []
    # Recherche des décorateurs @swagger_auto_schema
    pattern = r'@swagger_auto_schema\s*\(\s*(.*?)\s*\)'
    matches = re.findall(pattern, content, re.DOTALL)
    
    for match in matches:
        # Recherche des propriétés dans le schéma
        properties_pattern = r'properties\s*=\s*{([^}]+)}'
        properties_match = re.search(properties_pattern, match, re.DOTALL)
        if properties_match:
            properties_str = properties_match.group(1)
            # Extraire les noms des propriétés
            prop_pattern = r"'([^']+)'\s*:\s*openapi\.Schema'
            props = re.findall(prop_pattern, properties_str)
            schemas.append(props)
    
    return schemas

def find_inconsistencies():
    """Trouve les incohérences entre les modèles et les schémas OpenAPI"""
    base_path = Path("stock")
    inconsistencies = []
    
    for app_dir in base_path.iterdir():
        if app_dir.is_dir() and not app_dir.name.startswith('.'):
            model_file = app_dir / "models.py"
            views_file = app_dir / "views.py"
            
            if model_file.exists() and views_file.exists():
                print(f"Analyzing {app_dir.name}...")
                
                # Extraire les champs du modèle
                model_fields = extract_model_fields(model_file)
                
                # Extraire les schémas OpenAPI
                openapi_schemas = extract_openapi_schemas(views_file)
                
                # Comparer
                for i, schema in enumerate(openapi_schemas):
                    for field in schema:
                        if field not in model_fields:
                            inconsistencies.append({
                                'app': app_dir.name,
                                'schema_index': i,
                                'field': field,
                                'type': 'missing_in_model'
                            })
                
                for field in model_fields:
                    for schema in openapi_schemas:
                        if field not in schema:
                            inconsistencies.append({
                                'app': app_dir.name,
                                'field': field,
                                'type': 'missing_in_schema'
                            })
    
    return inconsistencies

def generate_correction_script(inconsistencies):
    """Génère un script de correction basé sur les incohérences trouvées"""
    script_content = """# Script de correction généré automatiquement
# Ce script corrige les schémas OpenAPI pour qu'ils correspondent aux modèles

"""
    
    for inconsistency in inconsistencies:
        if inconsistency['type'] == 'missing_in_model':
            script_content += f"# WARNING: Field '{inconsistency['field']}' in {inconsistency['app']} schema but not in model\n"
        elif inconsistency['type'] == 'missing_in_schema':
            script_content += f"# WARNING: Field '{inconsistency['field']}' in {inconsistency['app']} model but not in schema\n"
    
    return script_content

if __name__ == "__main__":
    print("Analyzing OpenAPI schemas...")
    inconsistencies = find_inconsistencies()
    
    if inconsistencies:
        print(f"\nFound {len(inconsistencies)} inconsistencies:")
        for inc in inconsistencies:
            print(f"- {inc['app']}: {inc['field']} ({inc['type']})")
        
        # Générer le script de correction
        correction_script = generate_correction_script(inconsistencies)
        with open("openapi_corrections.py", "w") as f:
            f.write(correction_script)
        
        print("\nCorrection script generated: openapi_corrections.py")
    else:
        print("No inconsistencies found!")