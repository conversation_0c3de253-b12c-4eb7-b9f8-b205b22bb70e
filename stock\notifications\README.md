# 📱 Application Notifications

## 🎯 Vue d'ensemble

L'application **Notifications** est un système complet de gestion des notifications pour le projet de gestion de stock PME. Elle permet d'envoyer des notifications via différents canaux (email, SMS, push, in-app) et de gérer les préférences utilisateur.

## 🏗️ Architecture

### Modèles principaux

1. **NotificationType** - Types de notifications disponibles
2. **NotificationPreference** - Préférences utilisateur
3. **Notification** - Notifications individuelles
4. **NotificationBatch** - Lots de notifications groupées

### Services

- **NotificationService** - Service principal de gestion
- **EmailNotificationService** - Envoi d'emails
- **SMSNotificationService** - Envoi de SMS (Twilio)
- **PushNotificationService** - Notifications push (Firebase)
- **InAppNotificationService** - Notifications in-app

## 🚀 Installation et Configuration

### 1. Dépendances

Ajoutez au `requirements.txt` :
```
requests==2.31.0
twilio==8.10.0
```

### 2. Variables d'environnement

```bash
# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********

# Push (Firebase)
FIREBASE_SERVER_KEY=your-firebase-key

# Configuration
EMAIL_NOTIFICATIONS_ENABLED=True
SMS_NOTIFICATIONS_ENABLED=False
PUSH_NOTIFICATIONS_ENABLED=True
NOTIFICATION_BATCH_SIZE=100
NOTIFICATION_RETRY_ATTEMPTS=3
```

### 3. Migrations

```bash
python manage.py makemigrations notifications
python manage.py migrate
```

### 4. Types de notifications par défaut

```bash
python manage.py create_notification_types
```

## 📊 Types de notifications disponibles

### Stock
- `ALERTE_STOCK_BAS` - Stock en dessous du seuil
- `STOCK_EPUISE` - Rupture de stock

### Ventes
- `NOUVELLE_VENTE` - Nouvelle vente réalisée
- `OBJECTIF_VENTE_ATTEINT` - Objectif de vente atteint

### Achats
- `NOUVEL_ACHAT` - Nouvelle commande fournisseur
- `COMMANDE_LIVREE` - Commande livrée

### Inventaire
- `INVENTAIRE_PLANIFIE` - Inventaire planifié
- `INVENTAIRE_TERMINE` - Inventaire terminé

### Système
- `BIENVENUE` - Message de bienvenue
- `MAINTENANCE_PROGRAMMEE` - Maintenance programmée

### Sécurité
- `CONNEXION_SUSPECTE` - Connexion suspecte détectée

## 🔧 Utilisation

### Créer et envoyer une notification

```python
from notifications.services import NotificationService

# Notification simple
notification = NotificationService.creer_notification(
    type_notification='ALERTE_STOCK_BAS',
    destinataire=user,
    titre='Stock bas',
    message='Le stock de produit X est bas',
    canal='EMAIL',
    priorite='HIGH'
)

# Envoyer immédiatement
NotificationService.envoyer_notification(notification)

# Ou créer et envoyer en une fois
NotificationService.creer_et_envoyer_notification(
    type_notification='NOUVELLE_VENTE',
    destinataires=[user1, user2],
    titre='Nouvelle vente',
    message='Vente de 100€ réalisée',
    donnees_contexte={'montant': 100},
    canal='IN_APP'
)
```

### Signaux automatiques

Les notifications sont automatiquement déclenchées par les signaux Django :

- **Stock bas** → Notification aux responsables
- **Nouvelle vente** → Notification au responsable magasin
- **Commande livrée** → Notification aux administrateurs
- **Inventaire terminé** → Notification avec écarts

## 🌐 API Endpoints

### Types de notifications
- `GET /api/notifications/types/` - Liste des types
- `POST /api/notifications/types/` - Créer un type
- `PUT /api/notifications/types/{id}/` - Modifier un type

### Préférences utilisateur
- `GET /api/notifications/preferences/` - Mes préférences
- `PUT /api/notifications/preferences/` - Modifier mes préférences

### Notifications
- `GET /api/notifications/notifications/` - Mes notifications
- `POST /api/notifications/notifications/` - Créer des notifications
- `POST /api/notifications/notifications/{id}/marquer_comme_lue/` - Marquer comme lue
- `GET /api/notifications/notifications/statistiques/` - Statistiques

### Actions groupées
- `POST /api/notifications/notifications/marquer-toutes-lues/` - Tout marquer comme lu
- `POST /api/notifications/envoyer/` - Envoyer notification personnalisée

## 🎛️ Administration Django

L'interface d'administration permet de :

- Gérer les types de notifications
- Voir toutes les notifications
- Modifier les préférences utilisateur
- Gérer les lots de notifications
- Actions groupées (marquer comme lu, renvoyer)

## 🧹 Maintenance

### Nettoyage des anciennes notifications

```bash
# Supprimer les notifications de plus de 30 jours
python manage.py cleanup_notifications --days 30

# Simulation sans suppression
python manage.py cleanup_notifications --days 30 --dry-run

# Conserver les notifications non lues
python manage.py cleanup_notifications --days 30 --keep-unread
```

## 🔒 Sécurité et Permissions

- **Authentification requise** pour toutes les API
- **Isolation des données** par utilisateur
- **Permissions par rôle** pour l'administration
- **Validation des données** côté serveur
- **Protection CSRF** activée

## 📈 Monitoring et Logs

Les logs sont générés pour :
- Envoi de notifications
- Échecs d'envoi
- Erreurs de configuration
- Statistiques d'utilisation

## 🚀 Fonctionnalités avancées

### Notifications en lot
- Envoi groupé pour performance
- Suivi du taux de succès
- Planification différée

### Templates personnalisables
- Templates HTML pour emails
- Templates SMS (160 caractères)
- Templates push et in-app
- Variables de contexte dynamiques

### Préférences granulaires
- Activation par canal
- Filtrage par catégorie
- Heures silencieuses
- Notifications weekend

### Intégrations externes
- **Twilio** pour SMS
- **Firebase** pour push
- **SMTP** pour emails
- Extensible pour autres services

## 🔮 Évolutions futures

- [ ] Notifications WebSocket temps réel
- [ ] Templates visuels avec éditeur
- [ ] Analyse des taux d'ouverture
- [ ] A/B testing des messages
- [ ] Intégration Slack/Teams
- [ ] Notifications vocales
- [ ] Machine Learning pour optimisation

## 🐛 Dépannage

### Problèmes courants

1. **Emails non envoyés**
   - Vérifier la configuration SMTP
   - Contrôler les logs Django
   - Tester avec un email simple

2. **SMS non envoyés**
   - Vérifier les credentials Twilio
   - Contrôler le format du numéro
   - Vérifier le crédit Twilio

3. **Notifications non reçues**
   - Vérifier les préférences utilisateur
   - Contrôler les heures silencieuses
   - Vérifier les filtres par catégorie

### Logs utiles

```python
import logging
logger = logging.getLogger('notifications')
logger.setLevel(logging.DEBUG)
```

## 📞 Support

Pour toute question ou problème :
- Consulter les logs Django
- Vérifier la documentation API (Swagger)
- Contacter l'équipe de développement
