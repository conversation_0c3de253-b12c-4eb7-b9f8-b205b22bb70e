from rest_framework import serializers
from .models import Fournisseur
from base.serializer import UserSerializer

class FournisseurSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    
    class Meta:
        model = Fournisseur
        fields = ['id', 'reference_fiscale', 'notes', 'user_details']

class CreateFournisseurSerializer(serializers.Serializer):
    user_data = UserSerializer()
    reference_fiscale = serializers.CharField()
    notes = serializers.CharField(required=False)

    def create(self, validated_data):
        user_data = validated_data.pop('user_data')
        user_data['role'] = 'FOURNISSEUR'
        user_serializer = UserSerializer(data=user_data)
        user_serializer.is_valid(raise_exception=True)
        user = user_serializer.save()
        
        return Fournisseur.objects.create(user=user, **validated_data)