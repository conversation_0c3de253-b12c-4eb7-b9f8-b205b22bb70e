from django.core.management.base import BaseCommand
from django.db import connection
from performance.query_optimizer import DatabaseOptimizer


class Command(BaseCommand):
    help = 'Optimise la base de données (index, statistiques, nettoyage)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-indexes',
            action='store_true',
            help='Créer les index recommandés'
        )
        parser.add_argument(
            '--analyze-tables',
            action='store_true',
            help='Analyser les tables pour les statistiques'
        )
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='Nettoyer la base de données (VACUUM)'
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Exécuter toutes les optimisations'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulation sans exécution'
        )

    def handle(self, *args, **options):
        create_indexes = options['create_indexes'] or options['all']
        analyze_tables = options['analyze_tables'] or options['all']
        vacuum = options['vacuum'] or options['all']
        dry_run = options['dry_run']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🔍 Mode simulation - Aucune modification ne sera effectuée')
            )

        self.stdout.write(
            self.style.SUCCESS('🚀 Optimisation de la base de données')
        )

        try:
            if create_indexes:
                self._create_indexes(dry_run)

            if analyze_tables:
                self._analyze_tables(dry_run)

            if vacuum:
                self._vacuum_database(dry_run)

            # Afficher les statistiques finales
            self._display_database_stats()

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erreur lors de l\'optimisation: {e}')
            )

    def _create_indexes(self, dry_run):
        """Crée les index recommandés"""
        self.stdout.write('\n📊 Création des index recommandés...')

        suggested_indexes = DatabaseOptimizer.suggest_indexes()

        if not suggested_indexes:
            self.stdout.write('   ✅ Aucun index supplémentaire recommandé')
            return

        with connection.cursor() as cursor:
            for i, index_sql in enumerate(suggested_indexes, 1):
                self.stdout.write(f'   {i}. {index_sql}')

                if not dry_run:
                    try:
                        cursor.execute(index_sql)
                        self.stdout.write(
                            self.style.SUCCESS(f'      ✅ Index créé avec succès')
                        )
                    except Exception as e:
                        if 'already exists' in str(e).lower():
                            self.stdout.write(
                                self.style.WARNING(f'      ⚠️  Index déjà existant')
                            )
                        else:
                            self.stdout.write(
                                self.style.ERROR(f'      ❌ Erreur: {e}')
                            )
                else:
                    self.stdout.write(
                        self.style.HTTP_INFO(f'      🔍 [SIMULATION] Index à créer')
                    )

    def _analyze_tables(self, dry_run):
        """Analyse les tables SQLite pour mettre à jour les statistiques"""
        self.stdout.write('\n📈 Analyse des tables SQLite...')

        with connection.cursor() as cursor:
            # Récupérer toutes les tables utilisateur
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name;
            """)
            tables = [row[0] for row in cursor.fetchall()]

            for table in tables:
                self.stdout.write(f'   📊 Analyse de {table}...')

                if not dry_run:
                    try:
                        # SQLite utilise ANALYZE pour mettre à jour les statistiques
                        cursor.execute(f'ANALYZE {table};')
                        self.stdout.write(
                            self.style.SUCCESS(f'      ✅ Analysée avec succès')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'      ❌ Erreur: {e}')
                        )
                else:
                    self.stdout.write(
                        self.style.HTTP_INFO(f'      🔍 [SIMULATION] Table à analyser')
                    )

    def _vacuum_database(self, dry_run):
        """Nettoie la base de données SQLite"""
        self.stdout.write('\n🧹 Nettoyage de la base de données SQLite...')

        if not dry_run:
            try:
                with connection.cursor() as cursor:
                    # VACUUM pour SQLite - récupère l'espace libre
                    self.stdout.write('   🔄 Exécution de VACUUM...')
                    cursor.execute('VACUUM;')

                    # Optimiser les pragmas SQLite
                    self.stdout.write('   ⚙️  Optimisation des paramètres SQLite...')
                    cursor.execute('PRAGMA optimize;')

                    # Analyser toutes les tables
                    self.stdout.write('   📊 Analyse globale...')
                    cursor.execute('ANALYZE;')

                    self.stdout.write(
                        self.style.SUCCESS('   ✅ Nettoyage SQLite terminé avec succès')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Erreur lors du nettoyage SQLite: {e}')
                )
        else:
            self.stdout.write(
                self.style.HTTP_INFO('   🔍 [SIMULATION] VACUUM et optimisation SQLite à exécuter')
            )

    def _display_database_stats(self):
        """Affiche les statistiques de la base de données SQLite"""
        self.stdout.write('\n📊 Statistiques de la base de données SQLite:')

        try:
            import os
            from django.conf import settings

            # Chemin et taille du fichier SQLite
            db_path = settings.DATABASES['default']['NAME']
            if os.path.exists(db_path):
                db_size_bytes = os.path.getsize(db_path)
                db_size_mb = db_size_bytes / (1024 * 1024)
                self.stdout.write(f'   💾 Taille du fichier: {db_size_mb:.2f} MB ({db_size_bytes:,} bytes)')
                self.stdout.write(f'   📁 Chemin: {db_path}')
            else:
                self.stdout.write('   ⚠️  Fichier de base de données non trouvé')
                return

            with connection.cursor() as cursor:
                # Informations générales SQLite
                cursor.execute("PRAGMA user_version;")
                user_version = cursor.fetchone()[0]

                cursor.execute("PRAGMA journal_mode;")
                journal_mode = cursor.fetchone()[0]

                cursor.execute("PRAGMA synchronous;")
                synchronous = cursor.fetchone()[0]

                self.stdout.write(f'   ⚙️  Mode journal: {journal_mode}')
                self.stdout.write(f'   ⚙️  Synchronisation: {synchronous}')
                self.stdout.write(f'   📝 Version utilisateur: {user_version}')

                # Nombre de tables et index
                cursor.execute("""
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%';
                """)
                table_count = cursor.fetchone()[0]

                cursor.execute("""
                    SELECT COUNT(*) FROM sqlite_master
                    WHERE type='index' AND name NOT LIKE 'sqlite_%';
                """)
                index_count = cursor.fetchone()[0]

                self.stdout.write(f'   📋 Tables: {table_count}')
                self.stdout.write(f'   📊 Index: {index_count}')

                # Top 5 des tables par nombre d'enregistrements
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name;
                """)
                tables = cursor.fetchall()

                table_stats = []
                for (table_name,) in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                        count = cursor.fetchone()[0]
                        table_stats.append((table_name, count))
                    except:
                        continue

                # Trier par nombre d'enregistrements
                table_stats.sort(key=lambda x: x[1], reverse=True)

                if table_stats:
                    self.stdout.write('   📋 Top 5 des tables par nombre d\'enregistrements:')
                    for table_name, count in table_stats[:5]:
                        self.stdout.write(f'      • {table_name}: {count:,} enregistrements')

                # Informations sur les index
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='index' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name;
                """)
                indexes = cursor.fetchall()

                if indexes:
                    self.stdout.write('   📊 Index personnalisés:')
                    for (index_name,) in indexes[:10]:  # Limiter à 10
                        self.stdout.write(f'      • {index_name}')

                # Analyse de l'intégrité
                cursor.execute("PRAGMA integrity_check(5);")
                integrity_results = cursor.fetchall()

                if integrity_results and integrity_results[0][0] == 'ok':
                    self.stdout.write('   ✅ Intégrité de la base: OK')
                else:
                    self.stdout.write('   ⚠️  Problèmes d\'intégrité détectés:')
                    for (result,) in integrity_results[:3]:
                        self.stdout.write(f'      • {result}')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Erreur récupération statistiques SQLite: {e}')
            )

        self.stdout.write(
            self.style.SUCCESS('\n🎉 Optimisation SQLite terminée!')
        )
