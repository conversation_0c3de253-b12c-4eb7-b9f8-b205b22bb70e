@echo off
REM 🚀 Script de démarrage optimisé pour SQLite - Windows
REM Phase 2 Performance - Configuration SQLite

echo 🚀 Démarrage de l'application avec SQLite optimisé...
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)
echo ✅ Python détecté

REM Créer les dossiers nécessaires
echo 📁 Création des dossiers...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "static" mkdir static
if not exist "media" mkdir media
echo ✅ Dossiers créés

REM Vérifier si l'environnement virtuel existe
if not exist "venv" (
    echo ⚠️  Environnement virtuel non trouvé, création...
    python -m venv venv
    echo ✅ Environnement virtuel créé
)

REM Activer l'environnement virtuel
echo 🔧 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Impossible d'activer l'environnement virtuel
    pause
    exit /b 1
)
echo ✅ Environnement virtuel activé

REM Installer les dépendances
echo 📦 Installation des dépendances...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Erreur lors de l'installation des dépendances
    pause
    exit /b 1
)
echo ✅ Dépendances installées

REM Installer les dépendances de performance
echo ⚡ Installation des dépendances de performance...
pip install django-redis psutil
if errorlevel 1 (
    echo ⚠️  Certaines dépendances de performance n'ont pas pu être installées
) else (
    echo ✅ Dépendances de performance installées
)

REM Vérifier Redis (optionnel)
redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Redis non disponible
    echo ℹ️  L'application fonctionnera sans cache Redis
    set REDIS_AVAILABLE=false
) else (
    echo ✅ Redis disponible et fonctionnel
    set REDIS_AVAILABLE=true
)

REM Migrations de base de données
echo 🗄️  Préparation de la base de données SQLite...

REM Créer les migrations si nécessaire
python manage.py makemigrations
echo ✅ Migrations vérifiées

REM Appliquer les migrations
python manage.py migrate
if errorlevel 1 (
    echo ❌ Erreur lors de la configuration de la base de données
    pause
    exit /b 1
)
echo ✅ Base de données SQLite configurée

REM Créer les types de notifications
echo 📱 Configuration des notifications...
python manage.py create_notification_types
echo ✅ Types de notifications configurés

REM Optimiser la base de données SQLite
echo ⚡ Optimisation de SQLite...
python manage.py optimize_database --create-indexes --analyze-tables
echo ✅ Base de données SQLite optimisée

REM Collecter les fichiers statiques
echo 📄 Collection des fichiers statiques...
python manage.py collectstatic --noinput
echo ✅ Fichiers statiques collectés

REM Créer un superutilisateur si nécessaire
echo 👤 Vérification du superutilisateur...
python manage.py shell -c "from base.models import User; print('Superutilisateur existant' if User.objects.filter(role='ADMIN').exists() else 'Création...'); User.objects.create_superuser(username='admin', email='<EMAIL>', password='admin123', telephone='0000000000', nom='Admin', prenom='System', adresse='System Address') if not User.objects.filter(role='ADMIN').exists() else None; print('admin/admin123' if not User.objects.filter(role='ADMIN').exists() else '')"

echo.
echo 🎉 Configuration terminée !
echo.
echo ℹ️  === INFORMATIONS DE DÉMARRAGE ===
echo ℹ️  Base de données: SQLite
if "%REDIS_AVAILABLE%"=="true" (
    echo ℹ️  Cache: Redis activé
) else (
    echo ℹ️  Cache: Cache Django par défaut
)
echo ℹ️  Environnement: Développement
echo ℹ️  Superutilisateur: admin / admin123
echo.

REM Proposer de démarrer le serveur
set /p choice="🚀 Démarrer le serveur de développement ? (y/N): "
if /i "%choice%"=="y" (
    echo.
    echo 🌐 Démarrage du serveur Django...
    echo.
    echo ✅ Serveur disponible sur: http://localhost:8000
    echo ✅ Admin: http://localhost:8000/admin
    echo ✅ API Docs: http://localhost:8000/swagger/
    echo ✅ Performance: http://localhost:8000/api/stocks/performance/
    echo.
    echo ℹ️  Arrêt avec Ctrl+C
    echo.
    
    REM Démarrer le serveur
    python manage.py runserver
) else (
    echo.
    echo ℹ️  Pour démarrer le serveur manuellement:
    echo ℹ️    venv\Scripts\activate.bat
    echo ℹ️    python manage.py runserver
    echo.
    echo ℹ️  URLs importantes:
    echo ℹ️    http://localhost:8000 - Application
    echo ℹ️    http://localhost:8000/admin - Administration
    echo ℹ️    http://localhost:8000/swagger/ - Documentation API
    echo ℹ️    http://localhost:8000/api/stocks/performance/ - Métriques
    echo.
    echo ℹ️  Commandes utiles:
    echo ℹ️    python manage.py performance_monitor - Monitoring temps réel
    echo ℹ️    python manage.py optimize_database --all - Optimisation SQLite
    echo ℹ️    python manage.py run_load_tests - Tests de charge
    echo.
    pause
)

echo.
echo ✅ 🎉 Prêt à l'emploi avec SQLite optimisé !
pause
