"""
Middleware de performance pour monitorer automatiquement les requêtes
"""

import time
import logging
from django.conf import settings
from django.db import connection
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from .monitoring import PerformanceLogger
from .cache_service import CacheService

logger = logging.getLogger(__name__)


class PerformanceMonitoringMiddleware(MiddlewareMixin):
    """Middleware pour monitorer les performances des requêtes"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Début du monitoring de la requête"""
        request._performance_start_time = time.time()
        request._performance_initial_queries = len(connection.queries) if settings.DEBUG else 0
        
        # Ajouter des headers de cache si approprié
        if request.method == 'GET':
            self._add_cache_headers(request)
    
    def process_response(self, request, response):
        """Fin du monitoring et logging des métriques"""
        
        # Calculer les métriques
        end_time = time.time()
        start_time = getattr(request, '_performance_start_time', end_time)
        response_time = end_time - start_time
        
        # Compter les requêtes DB
        final_queries = len(connection.queries) if settings.DEBUG else 0
        initial_queries = getattr(request, '_performance_initial_queries', 0)
        db_queries_count = final_queries - initial_queries
        
        # Ajouter les headers de performance
        response['X-Response-Time'] = f"{response_time:.3f}s"
        if settings.DEBUG:
            response['X-DB-Queries'] = str(db_queries_count)
        
        # Logger les requêtes lentes
        if response_time > 1.0:  # Plus d'1 seconde
            logger.warning(
                f"Slow request: {request.method} {request.path} - "
                f"Time: {response_time:.3f}s - "
                f"Queries: {db_queries_count} - "
                f"Status: {response.status_code}"
            )
        
        # Stocker les métriques pour analyse
        self._store_request_metrics(request, response, response_time, db_queries_count)
        
        # Alertes automatiques
        self._check_performance_alerts(response_time, db_queries_count)
        
        return response
    
    def _add_cache_headers(self, request):
        """Ajoute des headers de cache appropriés"""
        # Headers de cache pour les ressources statiques
        if any(request.path.endswith(ext) for ext in ['.css', '.js', '.png', '.jpg', '.ico']):
            request._cache_control = 'public, max-age=86400'  # 24 heures
        
        # Headers pour les APIs de données
        elif request.path.startswith('/api/'):
            if 'alertes' in request.path or 'summary' in request.path:
                request._cache_control = 'private, max-age=300'  # 5 minutes
            elif 'performance' in request.path:
                request._cache_control = 'private, max-age=60'   # 1 minute
    
    def _store_request_metrics(self, request, response, response_time, db_queries):
        """Stocke les métriques de la requête pour analyse"""
        try:
            # Clé pour les métriques de cette endpoint
            endpoint_key = f"metrics:{request.method}:{request.path_info}"
            
            # Récupérer les métriques existantes
            current_metrics = cache.get(endpoint_key, [])
            
            # Ajouter la nouvelle métrique
            metric = {
                'timestamp': time.time(),
                'response_time': response_time,
                'db_queries': db_queries,
                'status_code': response.status_code,
                'user_id': str(request.user.id_utilisateur) if hasattr(request, 'user') and request.user.is_authenticated else None
            }
            
            current_metrics.append(metric)
            
            # Garder seulement les 100 dernières métriques
            if len(current_metrics) > 100:
                current_metrics = current_metrics[-100:]
            
            # Stocker pour 24h
            cache.set(endpoint_key, current_metrics, 86400)
            
        except Exception as e:
            logger.error(f"Erreur stockage métriques requête: {str(e)}")
    
    def _check_performance_alerts(self, response_time, db_queries):
        """Vérifie et déclenche des alertes de performance"""
        alerts = []
        
        # Alerte temps de réponse
        if response_time > 2.0:
            alerts.append({
                'type': 'slow_response',
                'level': 'warning',
                'message': f'Temps de réponse lent: {response_time:.3f}s',
                'value': response_time
            })
        
        # Alerte nombre de requêtes DB
        if db_queries > 20:
            alerts.append({
                'type': 'high_db_queries',
                'level': 'warning',
                'message': f'Nombre élevé de requêtes DB: {db_queries}',
                'value': db_queries
            })
        
        # Envoyer les alertes si nécessaire
        if alerts:
            self._send_performance_alerts(alerts)
    
    def _send_performance_alerts(self, alerts):
        """Envoie des alertes de performance"""
        try:
            # Limiter les alertes pour éviter le spam
            alert_key = "performance_alerts_sent"
            last_alert_time = cache.get(alert_key, 0)
            current_time = time.time()
            
            # Envoyer une alerte maximum toutes les 5 minutes
            if current_time - last_alert_time > 300:
                from notifications.services import NotificationService
                from base.models import User
                
                # Envoyer aux administrateurs
                admins = User.objects.filter(role='ADMIN', statut=True)
                
                if admins.exists():
                    alert_messages = [alert['message'] for alert in alerts]
                    
                    NotificationService.creer_et_envoyer_notification(
                        type_notification='PERFORMANCE_ALERT',
                        destinataires=list(admins),
                        titre='Alerte Performance Système',
                        message=f"Alertes détectées: {', '.join(alert_messages)}",
                        donnees_contexte={'alerts': alerts},
                        canal='IN_APP',
                        priorite='HIGH'
                    )
                
                # Marquer l'alerte comme envoyée
                cache.set(alert_key, current_time, 300)
                
        except Exception as e:
            logger.error(f"Erreur envoi alertes performance: {str(e)}")


class CacheControlMiddleware(MiddlewareMixin):
    """Middleware pour gérer automatiquement les headers de cache"""
    
    def process_response(self, request, response):
        """Ajoute les headers de cache appropriés"""
        
        # Ne pas mettre en cache les erreurs
        if response.status_code >= 400:
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
            return response
        
        # Headers de cache basés sur le type de contenu
        if hasattr(request, '_cache_control'):
            response['Cache-Control'] = request._cache_control
        elif request.path.startswith('/api/'):
            # APIs par défaut
            if request.method == 'GET':
                response['Cache-Control'] = 'private, max-age=60'
            else:
                response['Cache-Control'] = 'no-cache'
        
        # ETag pour les ressources statiques
        if request.method == 'GET' and response.status_code == 200:
            if not response.has_header('ETag'):
                # Générer un ETag simple basé sur le contenu
                import hashlib
                content_hash = hashlib.md5(response.content).hexdigest()[:16]
                response['ETag'] = f'"{content_hash}"'
        
        return response


class DatabaseQueryCountMiddleware(MiddlewareMixin):
    """Middleware pour compter et limiter les requêtes DB"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.max_queries = getattr(settings, 'MAX_DB_QUERIES_PER_REQUEST', 50)
        super().__init__(get_response)
    
    def process_request(self, request):
        """Début du comptage des requêtes"""
        if settings.DEBUG:
            request._db_queries_start = len(connection.queries)
    
    def process_response(self, request, response):
        """Vérification du nombre de requêtes"""
        if settings.DEBUG:
            start_queries = getattr(request, '_db_queries_start', 0)
            end_queries = len(connection.queries)
            total_queries = end_queries - start_queries
            
            # Ajouter header informatif
            response['X-DB-Query-Count'] = str(total_queries)
            
            # Alerte si trop de requêtes
            if total_queries > self.max_queries:
                logger.warning(
                    f"High DB query count: {total_queries} queries for "
                    f"{request.method} {request.path}"
                )
                
                # Ajouter header d'avertissement
                response['X-DB-Query-Warning'] = f"High query count: {total_queries}"
        
        return response


class CompressionMiddleware(MiddlewareMixin):
    """Middleware pour la compression des réponses"""
    
    def process_response(self, request, response):
        """Ajoute les headers de compression"""
        
        # Vérifier si le client supporte la compression
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        
        if 'gzip' in accept_encoding and len(response.content) > 1024:
            # Marquer pour compression (sera géré par le serveur web)
            response['Vary'] = 'Accept-Encoding'
        
        return response


class SecurityHeadersMiddleware(MiddlewareMixin):
    """Middleware pour ajouter des headers de sécurité"""
    
    def process_response(self, request, response):
        """Ajoute les headers de sécurité"""
        
        # Headers de sécurité de base
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # CSP pour les APIs
        if request.path.startswith('/api/'):
            response['Content-Security-Policy'] = "default-src 'none'"
        
        return response
