from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import AchatMagasin
from .serializers import AchatMagasinSerializer
from base.permissions import AchatPermissions, IsAdmin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class AchatMagasinListCreateView(APIView):
    permission_classes = [IsAuthenticated, AchatPermissions]

    @swagger_auto_schema(
        operation_description="Récupérer la liste des achats",
        responses={
            200: openapi.Response(
                description="Liste des achats",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        achats = AchatMagasin.objects.all()
        serializer = AchatMagasinSerializer(achats, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouvel achat",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['fournisseur', 'date_reception_prevue'],
            properties={
                'fournisseur': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'date_reception_prevue': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'statut': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            201: openapi.Response(
                description="Achat créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        serializer = AchatMagasinSerializer(data=request.data)
        if serializer.is_valid():
            achat = serializer.save()
            return Response(AchatMagasinSerializer(achat).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AchatMagasinDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un achat spécifique",
        responses={
            200: openapi.Response(
                description="Détails de l'achat",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            achat = AchatMagasin.objects.get(id_achat=id)
            serializer = AchatMagasinSerializer(achat)
            return Response(serializer.data)
        except AchatMagasin.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un achat existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'fournisseur': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'date_reception_prevue': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'statut': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(
                description="Achat mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            achat = AchatMagasin.objects.get(id_achat=id)
            serializer = AchatMagasinSerializer(achat, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except AchatMagasin.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un achat",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            achat = AchatMagasin.objects.get(id_achat=id)
            achat.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except AchatMagasin.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)