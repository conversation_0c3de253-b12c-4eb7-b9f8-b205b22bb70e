from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Categorie
from produit.models import Produit
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Categorie)
def validate_categorie(sender, instance, **kwargs):
    """
    Valide la catégorie avant la sauvegarde
    """
    if not instance.nom:
        raise ValueError("Le nom de la catégorie ne peut pas être vide")
    
    # Vérifier si le nom est unique (en excluant l'instance actuelle pour les mises à jour)
    if Categorie.objects.filter(nom=instance.nom).exclude(id_categorie=instance.id_categorie).exists():
        raise ValueError("Une catégorie avec ce nom existe déjà")

@receiver(post_save, sender=Categorie)
def update_products_category(sender, instance, created, **kwargs):
    """
    Met à jour les produits associés à la catégorie
    """
    try:
        with transaction.atomic():
            # Mettre à jour les références des produits de cette catégorie
            products = Produit.objects.filter(categorie=instance)
            for product in products:
                if product.reference and product.reference.startswith(instance.nom[:3].upper()):
                    # La référence est déjà à jour
                    continue
                # Mettre à jour la référence du produit
                prefix = instance.nom[:3].upper()
                last_product = Produit.objects.filter(
                    reference__startswith=prefix
                ).order_by('-reference').first()
                
                if last_product and last_product.reference:
                    last_number = int(last_product.reference[len(prefix):])
                    new_number = last_number + 1
                else:
                    new_number = 1
                    
                product.reference = f"{prefix}{new_number:04d}"
                product.save(update_fields=['reference'])
                
            logger.info(f"Références des produits mis à jour pour la catégorie {instance.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des références des produits: {str(e)}")

@receiver(post_delete, sender=Categorie)
def handle_categorie_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'une catégorie
    """
    try:
        with transaction.atomic():
            # Mettre à jour les produits de cette catégorie
            products = Produit.objects.filter(categorie=instance)
            for product in products:
                product.categorie = None
                # Mettre à jour la référence avec le préfixe par défaut
                if product.reference:
                    new_reference = f"PRD{product.reference[3:]}"
                    product.reference = new_reference
                product.save(update_fields=['categorie', 'reference'])
            
            logger.info(f"Produits mis à jour après la suppression de la catégorie {instance.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des produits: {str(e)}")

@receiver(post_save, sender=Categorie)
def log_categorie_changes(sender, instance, created, **kwargs):
    """
    Enregistre les changements de catégorie dans les logs
    """
    if created:
        logger.info(f"Nouvelle catégorie créée: {instance.nom}")
    else:
        logger.info(f"Catégorie mise à jour: {instance.nom}") 