from rest_framework import serializers
from .models import Statistique
from magasins.serializers import MagasinSerializer
from entrepot.serializers import EntrepotSerializer

class StatistiqueSerializer(serializers.ModelSerializer):
    magasin_details = MagasinSerializer(source='magasin', read_only=True)
    entrepot_details = EntrepotSerializer(source='entrepot', read_only=True)

    class Meta:
        model = Statistique
        fields = [
            'id', 'entreprise', 'magasin', 'entrepot', 'periode',
            # Statistiques de ventes
            'total_ventes', 'chiffre_affaire', 'montant_ht', 'montant_tva',
            # Statistiques financières
            'marge_brute', 'taux_marge', 'cout_achat', 'benefice_net',
            # Statistiques de stock
            'total_produits', 'valeur_stock', 'taux_rotation',
            'produits_populaires', 'produits_en_alerte',
            # Statistiques de clients
            'nouveaux_clients', 'clients_actifs', 'taux_fidelite',
            'panier_moyen',
            # Statistiques de facturation
            'factures_emises', 'factures_payees', 'montant_impaye',
            'delai_paiement_moyen',
            # Statistiques de performance
            'temps_traitement_moyen', 'taux_retour', 'satisfaction_client',
            # Métadonnées
            'date_statistique', 'created_at',
            # Détails des relations
            'magasin_details', 'entrepot_details'
        ]
        read_only_fields = fields

class GenerateStatistiqueSerializer(serializers.Serializer):
    magasin_id = serializers.UUIDField(required=False)
    entrepot_id = serializers.UUIDField(required=False)
    periode = serializers.ChoiceField(
        choices=['JOUR', 'SEMAINE', 'MOIS', 'ANNEE'],
        default='JOUR'
    )
