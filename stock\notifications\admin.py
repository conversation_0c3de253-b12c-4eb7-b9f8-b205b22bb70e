from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import NotificationType, NotificationPreference, Notification, NotificationBatch


@admin.register(NotificationType)
class NotificationTypeAdmin(admin.ModelAdmin):
    """Administration des types de notifications"""
    
    list_display = [
        'nom', 'categorie', 'priorite_defaut', 'actif', 
        'created_at', 'notifications_count'
    ]
    list_filter = ['categorie', 'priorite_defaut', 'actif', 'created_at']
    search_fields = ['nom', 'description']
    ordering = ['categorie', 'nom']
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('nom', 'description', 'categorie', 'priorite_defaut', 'actif')
        }),
        ('Templates de notification', {
            'fields': ('template_email', 'template_sms', 'template_push', 'template_in_app'),
            'classes': ('collapse',)
        }),
    )
    
    def notifications_count(self, obj):
        """Compte le nombre de notifications de ce type"""
        count = obj.notification_set.count()
        if count > 0:
            url = reverse('admin:notifications_notification_changelist')
            return format_html(
                '<a href="{}?type_notification__id__exact={}">{} notifications</a>',
                url, obj.id, count
            )
        return '0 notifications'
    notifications_count.short_description = 'Notifications'


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    """Administration des préférences de notifications"""
    
    list_display = [
        'user', 'email_enabled', 'sms_enabled', 'push_enabled', 
        'in_app_enabled', 'weekend_notifications', 'updated_at'
    ]
    list_filter = [
        'email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled',
        'weekend_notifications', 'stock_notifications', 'vente_notifications'
    ]
    search_fields = ['user__username', 'user__email', 'user__nom', 'user__prenom']
    ordering = ['user__username']
    
    fieldsets = (
        ('Utilisateur', {
            'fields': ('user',)
        }),
        ('Canaux de notification', {
            'fields': ('email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled')
        }),
        ('Préférences par catégorie', {
            'fields': (
                'stock_notifications', 'vente_notifications', 'achat_notifications',
                'inventaire_notifications', 'finance_notifications', 
                'system_notifications', 'security_notifications'
            )
        }),
        ('Horaires', {
            'fields': ('quiet_hours_start', 'quiet_hours_end', 'weekend_notifications'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Administration des notifications"""
    
    list_display = [
        'titre', 'destinataire', 'type_notification', 'canal', 
        'statut', 'priorite', 'date_creation', 'est_lue_display'
    ]
    list_filter = [
        'canal', 'statut', 'priorite', 'type_notification__categorie',
        'date_creation', 'date_envoi', 'date_lecture'
    ]
    search_fields = [
        'titre', 'message', 'destinataire__username', 
        'destinataire__email', 'destinataire__nom', 'destinataire__prenom'
    ]
    ordering = ['-date_creation']
    readonly_fields = [
        'id', 'date_creation', 'date_envoi', 'date_lecture', 
        'tentatives_envoi', 'identifiant_externe', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('type_notification', 'destinataire', 'titre', 'message')
        }),
        ('Configuration', {
            'fields': ('canal', 'priorite', 'date_expiration', 'donnees_contexte')
        }),
        ('Objet lié', {
            'fields': ('content_type', 'object_id'),
            'classes': ('collapse',)
        }),
        ('Statut et dates', {
            'fields': (
                'statut', 'date_creation', 'date_envoi', 'date_lecture',
                'tentatives_envoi', 'erreur_envoi', 'identifiant_externe'
            ),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['marquer_comme_lues', 'renvoyer_notifications']
    
    def est_lue_display(self, obj):
        """Affiche si la notification est lue avec une icône"""
        if obj.est_lue:
            return format_html('<span style="color: green;">✓ Lue</span>')
        else:
            return format_html('<span style="color: red;">✗ Non lue</span>')
    est_lue_display.short_description = 'Statut lecture'
    
    def marquer_comme_lues(self, request, queryset):
        """Action pour marquer les notifications sélectionnées comme lues"""
        count = 0
        for notification in queryset:
            if not notification.est_lue:
                notification.marquer_comme_lue()
                count += 1
        
        self.message_user(
            request,
            f'{count} notification(s) marquée(s) comme lue(s).'
        )
    marquer_comme_lues.short_description = 'Marquer comme lues'
    
    def renvoyer_notifications(self, request, queryset):
        """Action pour renvoyer les notifications échouées"""
        from .services import NotificationService
        
        count = 0
        for notification in queryset.filter(statut='FAILED'):
            if NotificationService.envoyer_notification(notification):
                count += 1
        
        self.message_user(
            request,
            f'{count} notification(s) renvoyée(s) avec succès.'
        )
    renvoyer_notifications.short_description = 'Renvoyer les notifications échouées'


@admin.register(NotificationBatch)
class NotificationBatchAdmin(admin.ModelAdmin):
    """Administration des lots de notifications"""
    
    list_display = [
        'nom', 'type_notification', 'statut', 'total_destinataires',
        'notifications_envoyees', 'notifications_echouees', 
        'taux_succes_display', 'created_at'
    ]
    list_filter = [
        'statut', 'type_notification__categorie', 'date_planification', 'created_at'
    ]
    search_fields = ['nom', 'description', 'createur__username']
    ordering = ['-created_at']
    readonly_fields = [
        'id', 'total_destinataires', 'notifications_envoyees', 
        'notifications_echouees', 'date_debut_traitement', 
        'date_fin_traitement', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Informations générales', {
            'fields': ('nom', 'description', 'type_notification', 'createur')
        }),
        ('Configuration', {
            'fields': ('criteres_selection', 'date_planification')
        }),
        ('Statistiques', {
            'fields': (
                'statut', 'total_destinataires', 'notifications_envoyees',
                'notifications_echouees', 'date_debut_traitement', 'date_fin_traitement'
            ),
            'classes': ('collapse',)
        }),
    )
    
    def taux_succes_display(self, obj):
        """Affiche le taux de succès avec une barre de progression"""
        taux = obj.taux_succes
        if taux >= 90:
            color = 'green'
        elif taux >= 70:
            color = 'orange'
        else:
            color = 'red'
        
        return format_html(
            '<div style="width: 100px; background-color: #f0f0f0; border-radius: 3px;">'
            '<div style="width: {}%; background-color: {}; height: 20px; border-radius: 3px; '
            'text-align: center; color: white; font-size: 12px; line-height: 20px;">'
            '{:.1f}%</div></div>',
            taux, color, taux
        )
    taux_succes_display.short_description = 'Taux de succès'


# Configuration de l'admin
admin.site.site_header = "Administration des Notifications"
admin.site.site_title = "Notifications Admin"
admin.site.index_title = "Gestion des Notifications"
