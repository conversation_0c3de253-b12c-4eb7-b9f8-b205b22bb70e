from rest_framework import serializers
from .models import Facture
from stock.ventes.serializers import VenteSerializer

class FactureSerializer(serializers.ModelSerializer):
    vente_details = VenteSerializer(source='vente', read_only=True)

    class Meta:
        model = Facture
        fields = [
            'id_facture', 'vente', 'vente_details', 'numero_facture',
            'date_emission', 'date_echeance', 'statut', 'montant_ht',
            'montant_tva', 'montant_total', 'created_at', 'updated_at'
        ]
        read_only_fields = ['numero_facture', 'date_emission', 'montant_ht', 'montant_tva', 'montant_total'] 