from functools import wraps
from django.http import HttpResponseForbidden
from personalisation_user.models import UserProfile

def super_admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return HttpResponseForbidden("Accès non autorisé")
        
        try:
            profile = UserProfile.objects.get(user=request.user)
            if profile.role != 'SUPER_ADMIN':
                return HttpResponseForbidden("Accès réservé au Super Administrateur")
        except UserProfile.DoesNotExist:
            return HttpResponseForbidden("Profil utilisateur non trouvé")
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view 