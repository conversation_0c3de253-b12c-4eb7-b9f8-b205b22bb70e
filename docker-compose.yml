version: "3.8"

services:
  web:
    build: .
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn stock.wsgi:application --bind 0.0.0.0:8000"
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./data:/app/data  # Volume pour SQLite
    ports:
      - "8000:8000"
    environment:
      DEBUG: "False"
      ALLOWED_HOSTS: "0.0.0.0,localhost"
      REDIS_HOST: redis
      REDIS_PORT: 6379
      DATABASE_PATH: /app/data/db.sqlite3
    depends_on:
      redis:
        condition: service_healthy

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis

volumes:
  redis_data:
