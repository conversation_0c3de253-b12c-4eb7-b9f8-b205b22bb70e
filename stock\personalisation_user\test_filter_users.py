"""
Tests pour les nouvelles fonctionnalités de filtrage des utilisateurs
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from base.models import User
from entreprise.models import Entreprise
from magasins.models import Magasin, LocalisationMagasin
from entrepot.models import Entrepot
from responsable_magasin.models import Responsable_magasin
from responsable_entrepot.models import ResponsableEntrepot
from vendeurs.models import Vendeur
from personalisation_user.models import UserProfile
from datetime import date


class FilterUsersTestCase(TestCase):
    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer une entreprise
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123",
            date_creation=date.today()
        )
        
        # Créer un entrepôt
        self.entrepot = Entrepot.objects.create(
            nom="Test Entrepôt",
            adresse="456 Warehouse Ave",
            capacite_stockage=1000,
            entreprise=self.entreprise
        )
        
        # Créer une localisation pour le magasin
        self.localisation = LocalisationMagasin.objects.create(
            adresse="789 Store Blvd",
            ville="Test City",
            code_postal="12345"
        )
        
        # Créer un magasin
        self.magasin = Magasin.objects.create(
            nom="Test Magasin",
            localisation=self.localisation,
            entreprise=self.entreprise,
            entrepot=self.entrepot
        )
        
        # Créer un utilisateur admin
        self.admin_user = User.objects.create_user(
            username="admin_test",
            nom="Admin",
            prenom="Test",
            email="<EMAIL>",
            telephone="1234567890",
            adresse="Admin Address",
            role=User.Role.ADMIN,
            password="testpass123"
        )
        
        # Créer le profil admin
        UserProfile.objects.create(
            user=self.admin_user,
            entreprise=self.entreprise,
            role='ADMIN'
        )
        
        # Créer un utilisateur responsable magasin
        self.resp_magasin_user = User.objects.create_user(
            username="resp_magasin_test",
            nom="Responsable",
            prenom="Magasin",
            email="<EMAIL>",
            telephone="1234567891",
            adresse="Resp Address",
            role=User.Role.RESPONSABLE_MAGASIN,
            password="testpass123"
        )
        
        # Créer le responsable magasin
        self.resp_magasin = Responsable_magasin.objects.create(
            user=self.resp_magasin_user,
            code_responsable_magasin="RESP-123456",
            magasin=self.magasin,
            date_embauche=date.today()
        )
        
        # Créer un utilisateur responsable entrepôt
        self.resp_entrepot_user = User.objects.create_user(
            username="resp_entrepot_test",
            nom="Responsable",
            prenom="Entrepot",
            email="<EMAIL>",
            telephone="1234567892",
            adresse="Resp Ent Address",
            role=User.Role.RESPONSABLE_ENTREPOT,
            password="testpass123"
        )
        
        # Créer le responsable entrepôt
        self.resp_entrepot = ResponsableEntrepot.objects.create(
            user=self.resp_entrepot_user,
            entrepot=self.entrepot,
            code_responsable="RESP-ENT-123456",
            date_embauche=date.today()
        )
        
        # Créer un utilisateur vendeur
        self.vendeur_user = User.objects.create_user(
            username="vendeur_test",
            nom="Vendeur",
            prenom="Test",
            email="<EMAIL>",
            telephone="1234567893",
            adresse="Vendeur Address",
            role=User.Role.VENDEUR,
            password="testpass123"
        )
        
        # Créer le vendeur
        self.vendeur = Vendeur.objects.create(
            user=self.vendeur_user,
            code_vendeur="VEND-123456",
            magasin=self.magasin,
            date_embauche=date.today()
        )
        
        # Client API
        self.client = APIClient()
        self.client.force_authenticate(user=self.admin_user)
    
    def test_filter_users_by_entreprise(self):
        """Test du filtrage des utilisateurs par entreprise"""
        url = reverse('filter-users-by-entreprise')
        response = self.client.get(url, {'entreprise_id': str(self.entreprise.id)})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('entreprise', response.data)
        self.assertIn('total_users', response.data)
        self.assertIn('users', response.data)
        
        # Vérifier que tous les utilisateurs de l'entreprise sont retournés
        self.assertEqual(response.data['total_users'], 4)  # admin, resp_magasin, resp_entrepot, vendeur
        
        # Vérifier les informations de l'entreprise
        self.assertEqual(response.data['entreprise']['nom'], self.entreprise.nom)
    
    def test_filter_users_by_magasin(self):
        """Test du filtrage des utilisateurs par magasin"""
        url = reverse('filter-users-by-magasin')
        response = self.client.get(url, {'magasin_id': str(self.magasin.id)})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('magasin', response.data)
        self.assertIn('total_users', response.data)
        self.assertIn('users', response.data)
        
        # Vérifier que seuls les utilisateurs du magasin sont retournés
        self.assertEqual(response.data['total_users'], 2)  # resp_magasin, vendeur
        
        # Vérifier les informations du magasin
        self.assertEqual(response.data['magasin']['nom'], self.magasin.nom)
    
    def test_filter_users_advanced_by_entreprise_and_role(self):
        """Test du filtrage avancé par entreprise et rôle"""
        url = reverse('filter-users-advanced')
        response = self.client.get(url, {
            'entreprise_id': str(self.entreprise.id),
            'role': User.Role.RESPONSABLE_MAGASIN
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_users'], 1)
        self.assertEqual(response.data['users'][0]['role'], User.Role.RESPONSABLE_MAGASIN)
    
    def test_filter_users_advanced_by_magasin_and_role(self):
        """Test du filtrage avancé par magasin et rôle"""
        url = reverse('filter-users-advanced')
        response = self.client.get(url, {
            'magasin_id': str(self.magasin.id),
            'role': User.Role.VENDEUR
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_users'], 1)
        self.assertEqual(response.data['users'][0]['role'], User.Role.VENDEUR)
    
    def test_filter_users_missing_parameters(self):
        """Test avec paramètres manquants"""
        # Test filtrage par entreprise sans ID
        url = reverse('filter-users-by-entreprise')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test filtrage par magasin sans ID
        url = reverse('filter-users-by-magasin')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test filtrage avancé sans paramètres
        url = reverse('filter-users-advanced')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_filter_users_invalid_ids(self):
        """Test avec des IDs invalides"""
        # Test avec ID entreprise invalide
        url = reverse('filter-users-by-entreprise')
        response = self.client.get(url, {'entreprise_id': 'invalid-uuid'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test avec ID magasin invalide
        url = reverse('filter-users-by-magasin')
        response = self.client.get(url, {'magasin_id': 'invalid-uuid'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
