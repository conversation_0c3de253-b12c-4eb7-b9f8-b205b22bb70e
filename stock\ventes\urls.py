from django.urls import path
from .views import (
    VenteListCreateView, VenteDetailView, AddProduitVenteView,
    VentesByUserView, ProduitsStockVendeurView, VenteRapideVendeurView,
    TableauBordVendeurView, InfosPreRemplissageView
)

urlpatterns = [
    path('ventes/', VenteListCreateView.as_view(), name='vente-list-create'),
    path('ventes/<uuid:id>/', VenteDetailView.as_view(), name='vente-detail'),
    path('ventes/<uuid:vente_id>/add-produit/', AddProduitVenteView.as_view(), name='add-produit-vente'),
    path('ventes/user/<uuid:userid>/', VentesByUserView.as_view(), name='ventes-by-user'),
    path('vendeur/produits/', ProduitsStockVendeurView.as_view(), name='vendeur-produits-stock'),
    path('vendeur/vente-rapide/', VenteRapideVendeurView.as_view(), name='vendeur-vente-rapide'),
    path('vendeur/tableau-bord/', TableauBordVendeurView.as_view(), name='vendeur-tableau-bord'),
    path('infos-preremplissage/', InfosPreRemplissageView.as_view(), name='infos-preremplissage'),
]