# base/backends.py
from django.contrib.auth.backends import ModelBackend
from django.db.models import Q
from .models import User

class CustomAuthBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None or password is None:
            return None
            
        try:
            # Recherche par username, email ou téléphone
            user = User.objects.get(
                Q(username=username) | 
                Q(telephone=username) | 
                (Q(email=username) & ~Q(email=''))  # S'assurer que l'email n'est pas vide
            )
            
            # Vérifier le mot de passe
            if user.check_password(password):
                return user
                
        except User.DoesNotExist:
            # Aucun utilisateur trouvé avec ces informations
            return None
        
        return None