from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from .models import ResponsableEntrepot
from base.models import User


@receiver(post_save, sender=ResponsableEntrepot)
def responsable_entrepot_created(sender, instance, created, **kwargs):
    """Signal déclenché lors de la création d'un responsable d'entrepôt"""
    if created:
        # Créer une notification pour l'admin
        try:
            from notifications.services import NotificationService
            NotificationService.create_notification(
                title="Nouveau responsable d'entrepôt",
                message=f"Un nouveau responsable d'entrepôt a été assigné : {instance.user.prenom} {instance.user.nom} pour l'entrepôt {instance.entrepot.nom}",
                notification_type="INFO",
                recipient_role="ADMIN"
            )
        except ImportError:
            # Si le service de notifications n'est pas disponible
            pass

        # Log de l'événement
        import logging
        logger = logging.getLogger('django')
        logger.info(f"Nouveau responsable d'entrepôt créé: {instance.code_responsable} - {instance.user.nom} {instance.user.prenom}")


@receiver(pre_delete, sender=ResponsableEntrepot)
def responsable_entrepot_deleted(sender, instance, **kwargs):
    """Signal déclenché avant la suppression d'un responsable d'entrepôt"""
    # Log de l'événement
    import logging
    logger = logging.getLogger('django')
    logger.warning(f"Responsable d'entrepôt supprimé: {instance.code_responsable} - {instance.user.nom} {instance.user.prenom}")

    # Créer une notification pour l'admin
    try:
        from notifications.services import NotificationService
        NotificationService.create_notification(
            title="Responsable d'entrepôt supprimé",
            message=f"Le responsable d'entrepôt {instance.user.prenom} {instance.user.nom} ({instance.code_responsable}) a été supprimé de l'entrepôt {instance.entrepot.nom}",
            notification_type="WARNING",
            recipient_role="ADMIN"
        )
    except ImportError:
        pass


@receiver(post_save, sender=User)
def create_responsable_entrepot_profile(sender, instance, created, **kwargs):
    """Créer automatiquement un profil de responsable d'entrepôt si nécessaire"""
    if created and instance.role == User.Role.RESPONSABLE_ENTREPOT:
        # Ne pas créer automatiquement le profil, car il faut assigner un entrepôt
        # Cela sera fait manuellement via l'admin ou l'API
        import logging
        logger = logging.getLogger('django')
        logger.info(f"Utilisateur responsable d'entrepôt créé: {instance.nom} {instance.prenom}. Profil à créer manuellement.")
