from rest_framework import serializers
from .models import Inventaire, DetailInventaire
from magasins.serializers import MagasinSerializer
from entrepot.serializers import EntrepotSerializer
from produit.serializers import ProduitSerializer
from base.serializer import UserSerializer
from stock_Pme.models import Stock

class DetailInventaireSerializer(serializers.ModelSerializer):
    produit_details = ProduitSerializer(source='produit', read_only=True)
    compteur_details = UserSerializer(source='compteur', read_only=True)
    ecart = serializers.SerializerMethodField()
    ecart_pourcentage = serializers.SerializerMethodField()
    
    class Meta: 
        model = DetailInventaire
        fields = [
            'id_detail', 'produit', 'produit_details', 'quantite_theorique', 
            'quantite_reelle', 'statut', 'commentaire', 'date_comptage', 
            'compteur', 'compteur_details', 'ecart', 'ecart_pourcentage'
        ]
        read_only_fields = ['quantite_theorique', 'date_comptage']
        
    def get_ecart(self, obj):
        return obj.quantite_reelle - obj.quantite_theorique
    
    def get_ecart_pourcentage(self, obj):
        if obj.quantite_theorique > 0:
            return (obj.quantite_reelle - obj.quantite_theorique) / obj.quantite_theorique * 100
        return 0
        
    def validate_quantite_reelle(self, value):
        if value < 0:
            raise serializers.ValidationError("La quantité réelle ne peut pas être négative.")
        return value
    
class InventaireSerializer(serializers.ModelSerializer):
    magasin_details = MagasinSerializer(source='magasin', read_only=True)
    entrepot_details = EntrepotSerializer(source='entrepot', read_only=True)
    responsable_details = UserSerializer(source='responsable', read_only=True)
    details = DetailInventaireSerializer(many=True, read_only=True)
    ecarts = serializers.SerializerMethodField()
    rapport = serializers.SerializerMethodField()
    stocks_a_inventorier = serializers.SerializerMethodField()
    
    class Meta:
        model = Inventaire
        fields = [
            'id_inventaire', 'numero_inventaire', 'type_inventaire', 'date_planification',
            'date_debut', 'date_fin', 'statut', 'magasin', 'magasin_details',
            'entrepot', 'entrepot_details', 'responsable', 'responsable_details',
            'commentaire', 'tolerance_ecart', 'details', 'ecarts', 'rapport',
            'stocks_a_inventorier'
        ]
        extra_kwargs = {
            'numero_inventaire': {'required': False},
            'date_debut': {'required': False},
            'date_fin': {'required': False},
            'statut': {'required': False},
            'commentaire': {'required': False},
            'tolerance_ecart': {'required': False}
        }
    
    def get_ecarts(self, obj):
        return obj.calculerEcarts()
    
    def get_rapport(self, obj):
        return obj.genererRapportInventaire()
    
    def get_stocks_a_inventorier(self, obj):
        """Récupère les informations des stocks à inventorier"""
        stocks = obj.get_stocks_a_inventorier()
        return [{
            'produit_id': stock.produit.id_produit,
            'produit_nom': stock.produit.nom,
            'reference': stock.produit.reference,
            'quantite_disponible': stock.quantite_disponible,
            'location': stock.location.nom,
            'type_stock': stock.produit.type_stock
        } for stock in stocks]
    
    def validate(self, data):
        # Validation du responsable
        responsable = data.get('responsable')
        if responsable and responsable.role not in ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']:
            raise serializers.ValidationError("Le responsable doit être un ADMIN, RESPONSABLE_MAGASIN ou RESPONSABLE_ENTREPOT.")
        
        # Validation des dates
        date_debut = data.get('date_debut')
        date_fin = data.get('date_fin')
        if date_debut and date_fin and date_fin < date_debut:
            raise serializers.ValidationError("La date de fin ne peut pas être antérieure à la date de début.")
        
        # Validation du magasin et de l'entrepôt
        magasin = data.get('magasin')
        entrepot = data.get('entrepot')
        if magasin and entrepot:
            stocks = Stock.objects.filter(magasin=magasin, entrepot=entrepot)
            if not stocks.exists():
                raise serializers.ValidationError("Aucun stock trouvé pour ce magasin et cet entrepôt.")
        
        return data
    
    def create(self, validated_data):
        instance = super().create(validated_data)
        # Initialiser les détails d'inventaire
        instance.initialiser_details_inventaire()
        return instance
