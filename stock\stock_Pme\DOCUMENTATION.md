# Documentation API - Gestion des Stocks

Cette documentation décrit les principaux endpoints pour la gestion des stocks dans le module `stock_Pme`.

## Sommaire
- [Récupérer tous les stocks](#récupérer-tous-les-stocks)
- [Récupérer les stocks par type de stock](#récupérer-les-stocks-par-type-de-stock)
- [Récupérer les stocks pour un produit particulier](#récupérer-les-stocks-pour-un-produit-particulier)
- [Cas particuliers : produit sans stock](#cas-particuliers-produit-sans-stock)
- [Autres endpoints utiles](#autres-endpoints-utiles)

---

## Récupérer tous les stocks

**GET** `/api/stock_Pme/stocks/`

- Retourne la liste complète de tous les stocks.

**Exemple de réponse :**
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 50,
    "seuil_alerte": 10,
    "produit": "uuid du produit",
    "magasin": "uuid du magasin",
    "entrepot": "uuid de l'entrepot",
    "produit_details": { ... },
    "magasin_details": { ... }
    // ... autres champs
  }
]
```

---

## Récupérer les stocks par type de stock

**GET** `/api/stock_Pme/stocks/by-type/?type_stock=FINIS`

- Filtre les stocks selon le type de stock du produit.
- Types possibles : `FINIS`, `MATIERE`, `EMBALLAGE`, `SEMI_FINI`, `CONSOMMABLE`

**Exemple de requête :**
```
GET /api/stock_Pme/stocks/by-type/?type_stock=FINIS
```

**Exemple de réponse :**
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 20,
    "produit_details": { ... },
    "magasin_details": { ... }
  }
]
```

---

## Récupérer les stocks pour un produit particulier

**GET** `/api/stock_Pme/stocks/by-product/?produit_id=<uuid>`

- Retourne tous les stocks pour un produit donné (par son ID).

**Exemple de requête :**
```
GET /api/stock_Pme/stocks/by-product/?produit_id=123e4567-e89b-12d3-a456-************
```

### Cas 1 : Produit avec stock
```json
[
  {
    "id_stock": "uuid",
    "quantite_disponible": 50,
    "produit_details": { ... },
    "magasin_details": { ... }
  }
]
```

### Cas 2 : Produit sans stock
```json
{
  "produit": {
    "id": "uuid",
    "nom": "Nom du produit",
    "reference": "REF123",
    "type_stock": "FINIS",
    "entreprise": "Nom de l'entreprise"
  },
  "message": "Ce produit n'a pas encore de stock (aucune entrée de stock effectuée)",
  "stocks": []
}
```

### Cas 3 : Produit inexistant
```json
{
  "error": "Produit non trouvé"
}
```

---

## Cas particuliers : produit sans stock
- Un produit peut exister dans la base sans qu'aucune entrée de stock n'ait été faite.
- Dans ce cas, l'API retourne un message explicite et les infos du produit, mais la liste des stocks est vide.

---

## Autres endpoints utiles

- **GET** `/api/stock_Pme/entrepots/<uuid:entrepot_id>/rapport/` : Rapport de stock pour un entrepôt
- **GET** `/api/stock_Pme/alertes/` : Liste des stocks en alerte (quantité sous le seuil)
- **GET** `/api/stock_Pme/summary/` : Résumé global du stock

---

## Notes
- Tous les endpoints sont documentés avec Swagger (drf-yasg).
- Les réponses sont généralement au format JSON.
- Les filtres se font via les paramètres d'URL (`?type_stock=...`, `?produit_id=...`).

---

**Pour toute question ou évolution, voir le code source ou contacter l'équipe technique.** 