"""
Tests de charge pour évaluer les performances du système
"""

import time
import threading
import requests
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Callable, Any
from django.test import TestCase, TransactionTestCase
from django.test.client import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core.management.base import BaseCommand
import json

User = get_user_model()


class LoadTestResult:
    """Résultat d'un test de charge"""
    
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    def add_response(self, response_time: float, success: bool, error: str = None):
        """Ajoute un résultat de réponse"""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
            if error:
                self.errors.append(error)
    
    def get_statistics(self) -> Dict:
        """Calcule les statistiques du test"""
        if not self.response_times:
            return {}
        
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        total_requests = len(self.response_times)
        
        return {
            'total_requests': total_requests,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': (self.success_count / total_requests) * 100 if total_requests > 0 else 0,
            'total_time': total_time,
            'requests_per_second': total_requests / total_time if total_time > 0 else 0,
            'response_times': {
                'min': min(self.response_times),
                'max': max(self.response_times),
                'mean': statistics.mean(self.response_times),
                'median': statistics.median(self.response_times),
                'p95': self._percentile(self.response_times, 95),
                'p99': self._percentile(self.response_times, 99)
            },
            'errors': self.errors[:10]  # Limiter à 10 erreurs pour l'affichage
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calcule un percentile"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]


class LoadTester:
    """Testeur de charge principal"""
    
    def __init__(self, base_url: str = 'http://localhost:8000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
    
    def authenticate(self, username: str, password: str) -> bool:
        """Authentifie l'utilisateur et récupère le token"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/auth/login/",
                json={'username': username, 'password': password}
            )
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access')
                self.session.headers.update({
                    'Authorization': f'Bearer {self.auth_token}'
                })
                return True
        except Exception as e:
            print(f"Erreur authentification: {e}")
        return False
    
    def single_request(self, method: str, endpoint: str, data: Dict = None) -> tuple:
        """Effectue une requête unique et mesure le temps"""
        start_time = time.time()
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = self.session.get(url, params=data)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Méthode HTTP non supportée: {method}")
            
            end_time = time.time()
            response_time = end_time - start_time
            
            success = 200 <= response.status_code < 400
            error = None if success else f"HTTP {response.status_code}: {response.text[:100]}"
            
            return response_time, success, error
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            return response_time, False, str(e)
    
    def load_test(self, 
                  method: str, 
                  endpoint: str, 
                  concurrent_users: int = 10, 
                  requests_per_user: int = 10,
                  data_generator: Callable = None) -> LoadTestResult:
        """Effectue un test de charge"""
        
        result = LoadTestResult()
        result.start_time = time.time()
        
        def user_simulation(user_id: int):
            """Simule un utilisateur"""
            user_results = []
            
            for request_num in range(requests_per_user):
                # Générer des données si nécessaire
                request_data = data_generator(user_id, request_num) if data_generator else None
                
                # Effectuer la requête
                response_time, success, error = self.single_request(method, endpoint, request_data)
                user_results.append((response_time, success, error))
                
                # Petite pause entre les requêtes
                time.sleep(0.1)
            
            return user_results
        
        # Exécuter les tests en parallèle
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [
                executor.submit(user_simulation, user_id) 
                for user_id in range(concurrent_users)
            ]
            
            for future in as_completed(futures):
                try:
                    user_results = future.result()
                    for response_time, success, error in user_results:
                        result.add_response(response_time, success, error)
                except Exception as e:
                    result.add_response(0, False, str(e))
        
        result.end_time = time.time()
        return result


class APILoadTests:
    """Tests de charge spécifiques aux APIs"""
    
    def __init__(self, base_url: str = 'http://localhost:8000'):
        self.tester = LoadTester(base_url)
    
    def setup_authentication(self):
        """Configure l'authentification pour les tests"""
        # Créer un utilisateur de test si nécessaire
        return self.tester.authenticate('admin', 'admin123')
    
    def test_product_list_api(self, concurrent_users: int = 10, requests_per_user: int = 20):
        """Test de charge sur l'API de liste des produits"""
        print("🧪 Test de charge: Liste des produits")
        
        result = self.tester.load_test(
            method='GET',
            endpoint='/api/produits/',
            concurrent_users=concurrent_users,
            requests_per_user=requests_per_user
        )
        
        return result
    
    def test_stock_alerts_api(self, concurrent_users: int = 5, requests_per_user: int = 15):
        """Test de charge sur l'API des alertes de stock"""
        print("🧪 Test de charge: Alertes de stock")
        
        result = self.tester.load_test(
            method='GET',
            endpoint='/api/stocks/alertes/',
            concurrent_users=concurrent_users,
            requests_per_user=requests_per_user
        )
        
        return result
    
    def test_sales_creation_api(self, concurrent_users: int = 3, requests_per_user: int = 5):
        """Test de charge sur la création de ventes"""
        print("🧪 Test de charge: Création de ventes")
        
        def generate_sale_data(user_id: int, request_num: int):
            return {
                'client_id': f'client-{user_id}-{request_num}',
                'produits': [
                    {
                        'produit_id': 'prod-1',
                        'quantite': 1,
                        'prix_unitaire': 10.0
                    }
                ],
                'mode_paiement': 'ESPECES'
            }
        
        result = self.tester.load_test(
            method='POST',
            endpoint='/api/ventes/',
            concurrent_users=concurrent_users,
            requests_per_user=requests_per_user,
            data_generator=generate_sale_data
        )
        
        return result
    
    def test_notifications_api(self, concurrent_users: int = 8, requests_per_user: int = 10):
        """Test de charge sur l'API des notifications"""
        print("🧪 Test de charge: Notifications")
        
        result = self.tester.load_test(
            method='GET',
            endpoint='/api/notifications/notifications/',
            concurrent_users=concurrent_users,
            requests_per_user=requests_per_user
        )
        
        return result


class DatabaseLoadTests(TransactionTestCase):
    """Tests de charge pour la base de données"""
    
    def setUp(self):
        """Configuration des tests"""
        from entreprise.models import Entreprise
        from base.models import User
        
        # Créer une entreprise de test
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )
        
        # Créer un utilisateur de test
        self.user = User.objects.create_user(
            username='testuser',
            telephone='1234567890',
            nom='Test',
            prenom='User',
            adresse='Test Address',
            role='ADMIN'
        )
    
    def test_concurrent_stock_updates(self):
        """Test de mises à jour concurrentes de stock"""
        from stock_Pme.models import Stock
        from produit.models import Produit
        from categorie.models import Categorie
        from magasins.models import Magasin
        from entrepot.models import Entrepot
        
        # Créer les données de test
        categorie = Categorie.objects.create(nom="Test Categorie", entreprise=self.entreprise)
        magasin = Magasin.objects.create(nom="Test Magasin", adresse="Test", entreprise=self.entreprise)
        entrepot = Entrepot.objects.create(nom="Test Entrepot", adresse="Test", magasin=magasin)
        
        produit = Produit.objects.create(
            nom="Test Produit",
            reference="TEST-001",
            prix_achat=10.0,
            prix_vente=15.0,
            categorie=categorie,
            magasin=magasin,
            entreprise=self.entreprise
        )
        
        stock = Stock.objects.create(
            produit=produit,
            quantite_disponible=1000,
            seuil_alerte=10,
            magasin=magasin,
            entrepot=entrepot
        )
        
        def update_stock(thread_id: int):
            """Fonction de mise à jour de stock"""
            results = []
            for i in range(10):
                try:
                    start_time = time.time()
                    
                    # Simuler une vente (diminution du stock)
                    stock.quantite_disponible -= 1
                    stock.save()
                    
                    end_time = time.time()
                    results.append(end_time - start_time)
                    
                except Exception as e:
                    results.append(None)
            
            return results
        
        # Exécuter les mises à jour concurrentes
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(update_stock, i) for i in range(5)]
            all_results = []
            
            for future in as_completed(futures):
                results = future.result()
                all_results.extend([r for r in results if r is not None])
        
        # Analyser les résultats
        if all_results:
            avg_time = statistics.mean(all_results)
            max_time = max(all_results)
            print(f"Mises à jour concurrentes - Temps moyen: {avg_time:.3f}s, Max: {max_time:.3f}s")
        
        # Vérifier l'intégrité des données
        stock.refresh_from_db()
        expected_quantity = 1000 - (5 * 10)  # 5 threads * 10 updates each
        self.assertEqual(stock.quantite_disponible, expected_quantity)


class LoadTestRunner:
    """Exécuteur de tests de charge"""
    
    def __init__(self):
        self.results = {}
    
    def run_all_tests(self, base_url: str = 'http://localhost:8000'):
        """Exécute tous les tests de charge"""
        print("🚀 Démarrage des tests de charge...")
        
        api_tests = APILoadTests(base_url)
        
        # Authentification
        if not api_tests.setup_authentication():
            print("❌ Échec de l'authentification - Tests API ignorés")
            return
        
        # Tests API
        tests = [
            ('product_list', api_tests.test_product_list_api),
            ('stock_alerts', api_tests.test_stock_alerts_api),
            ('sales_creation', api_tests.test_sales_creation_api),
            ('notifications', api_tests.test_notifications_api),
        ]
        
        for test_name, test_func in tests:
            try:
                print(f"\n📊 Exécution: {test_name}")
                result = test_func()
                stats = result.get_statistics()
                self.results[test_name] = stats
                
                print(f"✅ {test_name} terminé:")
                print(f"   - Requêtes: {stats['total_requests']}")
                print(f"   - Taux de succès: {stats['success_rate']:.1f}%")
                print(f"   - Temps moyen: {stats['response_times']['mean']:.3f}s")
                print(f"   - P95: {stats['response_times']['p95']:.3f}s")
                print(f"   - RPS: {stats['requests_per_second']:.1f}")
                
            except Exception as e:
                print(f"❌ Erreur dans {test_name}: {e}")
                self.results[test_name] = {'error': str(e)}
    
    def generate_report(self) -> Dict:
        """Génère un rapport complet"""
        return {
            'timestamp': time.time(),
            'results': self.results,
            'summary': self._generate_summary()
        }
    
    def _generate_summary(self) -> Dict:
        """Génère un résumé des résultats"""
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results.values() if 'error' not in r])
        
        avg_response_times = []
        for result in self.results.values():
            if 'response_times' in result:
                avg_response_times.append(result['response_times']['mean'])
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': (successful_tests / total_tests) * 100 if total_tests > 0 else 0,
            'overall_avg_response_time': statistics.mean(avg_response_times) if avg_response_times else 0
        }
