{"produit": {"create": {"nom": "Smartphone XYZ", "reference": "ELE001", "categorie_nom": "Électronique", "nom_magasin": "Magasin Central", "type_stock": "FINIS", "prix": "299.99", "unite_mesure": "unité", "prix_achat": "250.00", "prix_vente": "349.99", "TVA": 0.2, "entreprise": "123e4567-e89b-12d3-a456-426614174000", "stock_initial": 10, "marque": "TechBrand", "description": "Smartphone dernière génération"}, "create_minimal": {"nom": "Produit Simple", "reference": "SIMPLE001", "categorie_nom": "Divers", "nom_magasin": "Magasin Test", "prix": "19.99", "unite_mesure": "unité", "prix_achat": "15.00", "prix_vente": "24.99", "entreprise": "123e4567-e89b-12d3-a456-426614174000"}}, "vente": {"create_with_client": {"magasin": "123e4567-e89b-12d3-a456-426614174000", "client": "456e7890-e89b-12d3-a456-426614174001", "vendeur": "789e0123-e89b-12d3-a456-426614174002", "mode_paiement": "ESPECES", "produits": [{"produit": "012e3456-e89b-12d3-a456-************", "quantite": 2}, {"produit": "345e6789-e89b-12d3-a456-426614174004", "quantite": 1}]}, "create_anonymous": {"magasin": "123e4567-e89b-12d3-a456-426614174000", "mode_paiement": "CARTE", "client_nom": "Client Anonyme", "client_telephone": "0123456789", "produits": [{"produit": "012e3456-e89b-12d3-a456-************", "quantite": 1}]}, "vente_rapide": {"produits": [{"produit": "012e3456-e89b-12d3-a456-************", "quantite": 3}], "mode_paiement": "ESPECES", "client_nom": "Client Express", "client_telephone": "0987654321"}, "add_product_to_sale": {"produit": "678e9012-e89b-12d3-a456-426614174005", "quantite": 2}}, "client": {"create": {"nom": "<PERSON><PERSON>", "prenom": "<PERSON>", "telephone": "0123456789", "email": "<EMAIL>", "adresse": "123 Rue de la Paix, 75001 Paris"}, "create_minimal": {"nom": "<PERSON>", "prenom": "<PERSON>", "telephone": "0987654321"}}, "vendeur": {"create": {"nom": "<PERSON>", "prenom": "<PERSON>", "email": "<EMAIL>", "telephone": "0987654321", "adresse": "456 Avenue des Champs", "password": "motdepasse123", "magasin": "123e4567-e89b-12d3-a456-426614174000", "date_embauche": "2024-01-15", "taux_commission": 0.05, "notes": "Vendeur expérimenté"}}, "magasin": {"create": {"nom": "Magasin Central", "entreprise": "123e4567-e89b-12d3-a456-426614174000", "localisation": {"adresse": "123 Rue Principale", "ville": "Paris", "code_postal": "75001", "pays": "France", "telephone": "0123456789", "email": "<EMAIL>"}}}, "stock_filters": {"by_type": {"url": "/api/stock_Pme/stocks/by-type/", "params": {"type_stock": "FINIS"}, "valid_types": ["FINIS", "MATIERE", "EMBALLAGE", "SEMI_FINI", "CONSOMMABLE"]}, "by_product": {"url": "/api/stock_Pme/stocks/by-product/", "params": {"produit_id": "012e3456-e89b-12d3-a456-************"}}}, "enums": {"mode_paiement": ["ESPECES", "CARTE", "CHEQUE", "VIREMENT"], "type_stock": ["FINIS", "MATIERE", "EMBALLAGE", "SEMI_FINI", "CONSOMMABLE"], "unite_mesure": ["kg", "g", "L", "ml", "unité"]}, "validation_patterns": {"telephone": "^\\+?[1-9]\\d{1,14}$", "email": "format email standard", "uuid": "format UUID v4 standard", "date": "YYYY-MM-DD", "datetime": "YYYY-MM-DDTHH:MM:SSZ"}}