# Utiliser une image officielle de Python
FROM python:3.11-slim

# Définir le répertoire de travail dans le conteneur
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copier le fichier requirements.txt
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt

# Créer les répertoires nécessaires
RUN mkdir -p /app/media/barcodes

# Copier le code de l'application
COPY ./stock /app

# Rendre le script de migration exécutable
RUN chmod +x /app/migrate.sh

# Exposer le port 8000 pour que l'application soit accessible
EXPOSE 8000

# Démarrer le serveur avec Gunicorn et lancer les migrations
CMD ["sh", "-c", "/app/migrate.sh && gunicorn stock.wsgi:application --bind 0.0.0.0:8000"] 