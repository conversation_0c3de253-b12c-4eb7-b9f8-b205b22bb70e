from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include

# Swagger / drf-yasg
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Suppression des imports directs des vues pour éviter les problèmes avec Swagger
# Les vues seront importées dans leurs URLs respectives


# Swagger configuration
schema_view = get_schema_view(
    openapi.Info(
        title="API Gestion de Stock PME",
        default_version='v1',
        description="Documentation de l'API pour la gestion de stock",
        contact=openapi.Contact(email="<EMAIL>"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include('base.urls')),
    path('api/users/', include('personalisation_user.urls')),
    path('api/produits/', include('produit.urls')),
    path('api/stock/', include('stock_Pme.urls')),
    path('api/entrepots/', include('entrepot.urls')),
    path('api/achats/', include('achat_magasin.urls')),
    path('api/clients/', include('clients.urls')),
    path('api/fournisseurs/', include('fournisseurs.urls')),
    path('api/magasins/', include('magasins.urls')),
    path('api/vendeurs/', include('vendeurs.urls')),
    path('api/responsables-magasin/', include('responsable_magasin.urls')),
    path('api/responsables-entrepot/', include('responsable_entrepot.urls')),
    path('api/ventes/', include('ventes.urls')),
    path('api/entreprises/', include('entreprise.urls')),
    path('api/categories/', include('categorie.urls')),
    path('api/statistiques/', include('statistiques.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/inventaire/', include('inventaire.urls')),

    # Docs Swagger et Redoc
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
