from rest_framework import serializers
from .models import Vente, DetailVente
from produit.serializers import ProduitSerializer
from vendeurs.serializers import VendeurSerializer
from magasins.serializers import MagasinSerializer
from entreprise.serializers import EntrepriseSerializer

class DetailVenteSerializer(serializers.ModelSerializer):
    produit_details = ProduitSerializer(source='produit', read_only=True)

    class Meta:
        model = DetailVente
        fields = [
            'id_detail_vente', 'produit', 'produit_details', 'quantite_vendue',
            'prix_unitaire', 'prix_achat', 'taux_tva', 'montant_ht', 'montant_tva',
            'montant_total', 'created_at', 'updated_at'
        ]

    def validate_quantite_vendue(self, value):
        if value <= 0:
            raise serializers.ValidationError("La quantité vendue doit être supérieure à 0.")
        return value

    def validate_prix_unitaire(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix unitaire doit être supérieur à 0.")
        return value

    def validate(self, data):
        montant_total = data.get('quantite_vendue', 0) * data.get('prix_unitaire', 0)
        if 'montant_total' in data and data['montant_total'] != montant_total:
            raise serializers.ValidationError("Le montant total doit être égal à quantité_vendue * prix_unitaire.")
        data['montant_total'] = montant_total
        return data

class ProduitVenteSerializer(serializers.Serializer):
    produit = serializers.UUIDField()
    quantite = serializers.IntegerField(min_value=1)

    def validate_quantite(self, value):
        if value <= 0:
            raise serializers.ValidationError("La quantité doit être supérieure à 0.")
        return value

class VenteSerializer(serializers.ModelSerializer):
    client_details = serializers.SerializerMethodField()
    vendeur_details = VendeurSerializer(source='vendeur', read_only=True)
    magasin_details = MagasinSerializer(source='magasin', read_only=True)
    entreprise_details = EntrepriseSerializer(source='entreprise', read_only=True)
    details = DetailVenteSerializer(many=True, read_only=True)
    produits = ProduitVenteSerializer(many=True, write_only=True, required=False)
    client_nom = serializers.CharField(write_only=True, required=False, allow_blank=True)
    client_telephone = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = Vente
        fields = [
            'id_vente', 'numero_facture', 'client', 'client_details', 'vendeur', 'vendeur_details',
            'magasin', 'magasin_details', 'entreprise', 'entreprise_details',
            'montant_total', 'montant_ht', 'montant_tva', 'date_vente',
            'mode_paiement', 'created_at', 'updated_at', 'details', 'produits',
            'client_nom', 'client_telephone'
        ]
        read_only_fields = ['numero_facture', 'montant_total', 'montant_ht', 'montant_tva', 'date_vente']

    def get_client_details(self, obj):
        if obj.client:
            from clients.serializers import ClientSerializer
            return ClientSerializer(obj.client).data
        return None

    def validate_montant_total(self, value):
        if value < 0:
            raise serializers.ValidationError("Le montant total ne peut pas être négatif.")
        return value

    def validate(self, data):
        if 'mode_paiement' in data and data['mode_paiement'] not in dict(Vente.MODE_PAIEMENT_CHOICES):
            raise serializers.ValidationError("Mode de paiement invalide.")
        return data

    def create(self, validated_data):
        produits = validated_data.pop('produits', [])
        client_nom = validated_data.pop('client_nom', None)
        client_telephone = validated_data.pop('client_telephone', None)

        # Gérer la création automatique de client si nom fourni
        if client_nom and not validated_data.get('client'):
            from clients.models import Client

            # Essayer de trouver un client existant par nom et téléphone
            client = None
            if client_telephone:
                client = Client.objects.filter(
                    nom__icontains=client_nom,
                    telephone=client_telephone
                ).first()

            # Si pas trouvé, créer un nouveau client
            if not client:
                # Récupérer le magasin pour associer le client
                magasin = validated_data.get('magasin')
                if magasin:
                    client = Client.objects.create(
                        nom=client_nom,
                        prenom="",  # Prenom vide par défaut
                        telephone=client_telephone or "",
                        magasin=magasin
                    )
                    validated_data['client'] = client

        vente = Vente.objects.create(**validated_data)

        # Ajouter les produits à la vente
        for produit_data in produits:
            DetailVente.ajouterProduit(
                vente.id_vente,
                produit_data['produit'],
                produit_data['quantite']
            )

        # Recalculer les montants
        vente.calculer_montant_total()

        return vente