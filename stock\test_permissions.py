#!/usr/bin/env python
"""
Script de test pour vérifier les permissions de toutes les apps
"""
import os
import sys
import django
import requests
import json
from datetime import date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock.settings')
django.setup()

from base.models import User
from entreprise.models import Entreprise
from magasins.models import Magasin, LocalisationMagasin
from entrepot.models import Entrepot
from responsable_magasin.models import Responsable_magasin
from responsable_entrepot.models import ResponsableEntrepot
from vendeurs.models import Vendeur
from personalisation_user.models import UserProfile


class PermissionTester:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.tokens = {}
        self.test_data = {}
    
    def setup_test_data(self):
        """Créer les données de test"""
        print("🔧 Configuration des données de test...")
        
        # Créer une entreprise
        self.test_data['entreprise'], created = Entreprise.objects.get_or_create(
            nom="Test Permissions Entreprise",
            defaults={
                'adresse': "123 Test Street",
                'nif': "1234567890123",
                'date_creation': date.today()
            }
        )
        
        # Créer un entrepôt
        self.test_data['entrepot'], created = Entrepot.objects.get_or_create(
            nom="Test Permissions Entrepôt",
            defaults={
                'adresse': "456 Warehouse Ave",
                'capacite_stockage': 1000,
                'entreprise': self.test_data['entreprise']
            }
        )
        
        # Créer une localisation pour le magasin
        localisation, created = LocalisationMagasin.objects.get_or_create(
            adresse="789 Store Blvd",
            defaults={
                'ville': "Test City",
                'code_postal': "12345"
            }
        )
        
        # Créer un magasin
        self.test_data['magasin'], created = Magasin.objects.get_or_create(
            nom="Test Permissions Magasin",
            defaults={
                'localisation': localisation,
                'entreprise': self.test_data['entreprise'],
                'entrepot': self.test_data['entrepot']
            }
        )
        
        # Créer les utilisateurs de test
        self.create_test_users()
        
        print("✅ Données de test créées")
    
    def create_test_users(self):
        """Créer les utilisateurs de test pour chaque rôle"""
        users_data = [
            {
                'role': 'ADMIN',
                'username': 'test_admin_perm',
                'telephone': '1111111111',
                'password': 'test123'
            },
            {
                'role': 'RESPONSABLE_MAGASIN',
                'username': 'test_resp_mag_perm',
                'telephone': '2222222222',
                'password': 'test123'
            },
            {
                'role': 'VENDEUR',
                'username': 'test_vendeur_perm',
                'telephone': '3333333333',
                'password': 'test123'
            },
            {
                'role': 'RESPONSABLE_ENTREPOT',
                'username': 'test_resp_ent_perm',
                'telephone': '4444444444',
                'password': 'test123'
            }
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'nom': f"Test {user_data['role']}",
                    'prenom': "User",
                    'email': f"{user_data['username']}@test.com",
                    'telephone': user_data['telephone'],
                    'adresse': "Test Address",
                    'role': user_data['role']
                }
            )
            
            if created:
                user.set_password(user_data['password'])
                user.save()
            
            self.test_data[user_data['role'].lower()] = user
            
            # Créer les relations spécifiques
            if user_data['role'] == 'ADMIN':
                UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'entreprise': self.test_data['entreprise'],
                        'role': 'ADMIN'
                    }
                )
            elif user_data['role'] == 'RESPONSABLE_MAGASIN':
                Responsable_magasin.objects.get_or_create(
                    user=user,
                    defaults={
                        'code_responsable_magasin': "RESP-999999",
                        'magasin': self.test_data['magasin'],
                        'date_embauche': date.today()
                    }
                )
            elif user_data['role'] == 'VENDEUR':
                Vendeur.objects.get_or_create(
                    user=user,
                    defaults={
                        'code_vendeur': "VEND-999999",
                        'magasin': self.test_data['magasin'],
                        'date_embauche': date.today()
                    }
                )
            elif user_data['role'] == 'RESPONSABLE_ENTREPOT':
                ResponsableEntrepot.objects.get_or_create(
                    user=user,
                    defaults={
                        'entrepot': self.test_data['entrepot'],
                        'code_responsable': "RESP-ENT-999999",
                        'date_embauche': date.today()
                    }
                )
    
    def authenticate_users(self):
        """Obtenir les tokens d'authentification pour tous les utilisateurs"""
        print("🔐 Authentification des utilisateurs...")
        
        for role in ['admin', 'responsable_magasin', 'vendeur', 'responsable_entrepot']:
            user = self.test_data[role]
            response = requests.post(f"{self.base_url}/auth/login/", {
                'telephone': user.telephone,
                'password': 'test123'
            })
            
            if response.status_code == 200:
                self.tokens[role] = response.json().get('access')
                print(f"✅ {role.upper()} authentifié")
            else:
                print(f"❌ Échec authentification {role.upper()}: {response.text}")
    
    def test_endpoint_permissions(self, endpoint, expected_permissions):
        """Tester les permissions d'un endpoint"""
        print(f"\n🧪 Test de {endpoint}")
        results = {}
        
        for role in ['admin', 'responsable_magasin', 'vendeur', 'responsable_entrepot']:
            if role not in self.tokens:
                continue
                
            headers = {'Authorization': f'Bearer {self.tokens[role]}'}
            response = requests.get(f"{self.base_url}{endpoint}", headers=headers)
            
            should_have_access = role.upper() in expected_permissions
            has_access = response.status_code in [200, 201]
            
            status_icon = "✅" if (should_have_access == has_access) else "❌"
            access_text = "AUTORISÉ" if has_access else "REFUSÉ"
            
            print(f"   {status_icon} {role.upper()}: {access_text} (Code: {response.status_code})")
            results[role] = {
                'expected': should_have_access,
                'actual': has_access,
                'status_code': response.status_code
            }
        
        return results
    
    def run_all_tests(self):
        """Exécuter tous les tests de permissions"""
        print("🚀 Démarrage des tests de permissions")
        print("=" * 60)
        
        # Configuration
        self.setup_test_data()
        self.authenticate_users()
        
        # Tests des endpoints
        test_cases = [
            {
                'endpoint': '/magasins/',
                'expected': ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
            },
            {
                'endpoint': '/ventes/ventes/',
                'expected': ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
            },
            {
                'endpoint': '/entrepots/',
                'expected': ['ADMIN', 'RESPONSABLE_ENTREPOT']
            },
            {
                'endpoint': '/stock/',
                'expected': ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
            },
            {
                'endpoint': '/clients/',
                'expected': ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
            },
            {
                'endpoint': '/fournisseurs/',
                'expected': ['ADMIN', 'RESPONSABLE_ENTREPOT']
            },
            {
                'endpoint': '/produits/',
                'expected': ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
            }
        ]
        
        all_results = {}
        for test_case in test_cases:
            results = self.test_endpoint_permissions(
                test_case['endpoint'], 
                test_case['expected']
            )
            all_results[test_case['endpoint']] = results
        
        # Résumé
        self.print_summary(all_results)
    
    def print_summary(self, results):
        """Afficher le résumé des tests"""
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS DE PERMISSIONS")
        print("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        
        for endpoint, endpoint_results in results.items():
            print(f"\n🔗 {endpoint}")
            for role, result in endpoint_results.items():
                total_tests += 1
                if result['expected'] == result['actual']:
                    passed_tests += 1
                    status = "✅ PASS"
                else:
                    status = "❌ FAIL"
                
                print(f"   {status} {role.upper()}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n🎯 RÉSULTAT GLOBAL: {passed_tests}/{total_tests} tests réussis ({success_rate:.1f}%)")
        
        if success_rate == 100:
            print("🎉 Tous les tests de permissions sont réussis !")
        else:
            print("⚠️  Certains tests ont échoué. Vérifiez la configuration des permissions.")


def main():
    """Fonction principale"""
    try:
        tester = PermissionTester()
        tester.run_all_tests()
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
