from django.core.management.base import BaseCommand
from notifications.models import NotificationType


class Command(BaseCommand):
    help = 'Crée les types de notifications par défaut'

    def handle(self, *args, **options):
        """Crée tous les types de notifications nécessaires au système"""
        
        notification_types = [
            # Notifications de stock
            {
                'nom': 'ALERTE_STOCK_BAS',
                'description': 'Alerte quand le stock est en dessous du seuil',
                'categorie': 'STOCK',
                'priorite_defaut': 'HIGH',
                'template_email': '''
                <h2>Alerte Stock Bas</h2>
                <p>Le produit <strong>{{ produit_nom }}</strong> ({{ produit_reference }}) est en dessous du seuil d'alerte.</p>
                <ul>
                    <li>Quantité actuelle: {{ quantite_actuelle }}</li>
                    <li>Seuil d'alerte: {{ seuil_alerte }}</li>
                    <li>Magasin: {{ magasin_nom }}</li>
                    <li>Entrepôt: {{ entrepot_nom }}</li>
                </ul>
                <p>Veuillez procéder au réapprovisionnement.</p>
                ''',
                'template_sms': 'Alerte: Stock bas pour {{ produit_nom }} ({{ quantite_actuelle }}/{{ seuil_alerte }})',
                'template_push': 'Stock bas: {{ produit_nom }}',
                'template_in_app': 'Le stock de {{ produit_nom }} est en dessous du seuil d\'alerte'
            },
            {
                'nom': 'STOCK_EPUISE',
                'description': 'Notification quand un produit est en rupture de stock',
                'categorie': 'STOCK',
                'priorite_defaut': 'CRITICAL',
                'template_email': '''
                <h2>Rupture de Stock</h2>
                <p>Le produit <strong>{{ produit_nom }}</strong> est en rupture de stock.</p>
                <p>Magasin: {{ magasin_nom }}</p>
                <p>Action immédiate requise!</p>
                ''',
                'template_sms': 'URGENT: Rupture stock {{ produit_nom }}',
                'template_push': 'Rupture: {{ produit_nom }}',
                'template_in_app': 'Rupture de stock pour {{ produit_nom }}'
            },
            
            # Notifications de ventes
            {
                'nom': 'NOUVELLE_VENTE',
                'description': 'Notification lors d\'une nouvelle vente',
                'categorie': 'VENTE',
                'priorite_defaut': 'MEDIUM',
                'template_email': '''
                <h2>Nouvelle Vente</h2>
                <p>Une nouvelle vente de <strong>{{ montant_total }}€</strong> a été réalisée.</p>
                <ul>
                    <li>Vendeur: {{ vendeur_nom }}</li>
                    <li>Client: {{ client_nom }}</li>
                    <li>Magasin: {{ magasin_nom }}</li>
                    <li>Mode de paiement: {{ mode_paiement }}</li>
                </ul>
                ''',
                'template_sms': 'Nouvelle vente: {{ montant_total }}€ par {{ vendeur_nom }}',
                'template_push': 'Vente: {{ montant_total }}€',
                'template_in_app': 'Nouvelle vente de {{ montant_total }}€ réalisée'
            },
            {
                'nom': 'OBJECTIF_VENTE_ATTEINT',
                'description': 'Notification quand un objectif de vente est atteint',
                'categorie': 'VENTE',
                'priorite_defaut': 'HIGH',
                'template_email': '''
                <h2>Objectif Atteint!</h2>
                <p>Félicitations! L'objectif de vente a été atteint.</p>
                <p>Objectif: {{ objectif }}€</p>
                <p>Réalisé: {{ realise }}€</p>
                ''',
                'template_sms': 'Objectif atteint! {{ realise }}€/{{ objectif }}€',
                'template_push': 'Objectif atteint!',
                'template_in_app': 'Objectif de vente atteint: {{ realise }}€'
            },
            
            # Notifications d'achats
            {
                'nom': 'NOUVEL_ACHAT',
                'description': 'Notification lors d\'un nouvel achat',
                'categorie': 'ACHAT',
                'priorite_defaut': 'MEDIUM',
                'template_email': '''
                <h2>Nouvelle Commande Fournisseur</h2>
                <p>Une nouvelle commande de <strong>{{ montant_total }}€</strong> a été passée.</p>
                <ul>
                    <li>Fournisseur: {{ fournisseur_nom }}</li>
                    <li>Date de commande: {{ date_commande }}</li>
                    <li>Réception prévue: {{ date_reception_prevue }}</li>
                </ul>
                ''',
                'template_sms': 'Commande {{ montant_total }}€ chez {{ fournisseur_nom }}',
                'template_push': 'Commande: {{ montant_total }}€',
                'template_in_app': 'Nouvelle commande de {{ montant_total }}€'
            },
            {
                'nom': 'COMMANDE_LIVREE',
                'description': 'Notification quand une commande est livrée',
                'categorie': 'ACHAT',
                'priorite_defaut': 'MEDIUM',
                'template_email': '''
                <h2>Commande Livrée</h2>
                <p>La commande de <strong>{{ montant_total }}€</strong> de {{ fournisseur_nom }} a été livrée.</p>
                <p>Date de livraison: {{ date_livraison }}</p>
                ''',
                'template_sms': 'Livraison reçue: {{ fournisseur_nom }}',
                'template_push': 'Livraison reçue',
                'template_in_app': 'Commande de {{ fournisseur_nom }} livrée'
            },
            
            # Notifications d'inventaire
            {
                'nom': 'INVENTAIRE_PLANIFIE',
                'description': 'Notification quand un inventaire est planifié',
                'categorie': 'INVENTAIRE',
                'priorite_defaut': 'MEDIUM',
                'template_email': '''
                <h2>Inventaire Planifié</h2>
                <p>Un inventaire {{ type_inventaire }} a été planifié.</p>
                <ul>
                    <li>Numéro: {{ numero_inventaire }}</li>
                    <li>Magasin: {{ magasin_nom }}</li>
                    <li>Date: {{ date_planification }}</li>
                </ul>
                ''',
                'template_sms': 'Inventaire planifié: {{ numero_inventaire }}',
                'template_push': 'Inventaire planifié',
                'template_in_app': 'Inventaire {{ numero_inventaire }} planifié'
            },
            {
                'nom': 'INVENTAIRE_TERMINE',
                'description': 'Notification quand un inventaire est terminé',
                'categorie': 'INVENTAIRE',
                'priorite_defaut': 'HIGH',
                'template_email': '''
                <h2>Inventaire Terminé</h2>
                <p>L'inventaire {{ numero_inventaire }} est terminé.</p>
                <p>{{ total_ecarts }} écart(s) significatif(s) détecté(s).</p>
                <p>Magasin: {{ magasin_nom }}</p>
                ''',
                'template_sms': 'Inventaire terminé: {{ total_ecarts }} écarts',
                'template_push': 'Inventaire terminé',
                'template_in_app': 'Inventaire {{ numero_inventaire }} terminé avec {{ total_ecarts }} écarts'
            },
            
            # Notifications système
            {
                'nom': 'BIENVENUE',
                'description': 'Message de bienvenue pour les nouveaux utilisateurs',
                'categorie': 'USER',
                'priorite_defaut': 'LOW',
                'template_email': '''
                <h2>Bienvenue {{ nom_utilisateur }}!</h2>
                <p>Bienvenue dans notre système de gestion de stock.</p>
                <p>Votre rôle: {{ role }}</p>
                <p>Vous pouvez configurer vos préférences de notification dans votre profil.</p>
                ''',
                'template_sms': 'Bienvenue {{ nom_utilisateur }}!',
                'template_push': 'Bienvenue!',
                'template_in_app': 'Bienvenue dans le système de gestion de stock'
            },
            {
                'nom': 'MAINTENANCE_PROGRAMMEE',
                'description': 'Notification de maintenance programmée',
                'categorie': 'SYSTEM',
                'priorite_defaut': 'HIGH',
                'template_email': '''
                <h2>Maintenance Programmée</h2>
                <p>Une maintenance est programmée le {{ date_maintenance }}.</p>
                <p>Durée estimée: {{ duree }}</p>
                <p>Le système sera temporairement indisponible.</p>
                ''',
                'template_sms': 'Maintenance {{ date_maintenance }}',
                'template_push': 'Maintenance programmée',
                'template_in_app': 'Maintenance programmée le {{ date_maintenance }}'
            },
            
            # Notifications de sécurité
            {
                'nom': 'CONNEXION_SUSPECTE',
                'description': 'Notification de connexion suspecte',
                'categorie': 'SECURITY',
                'priorite_defaut': 'CRITICAL',
                'template_email': '''
                <h2>Connexion Suspecte Détectée</h2>
                <p>Une connexion suspecte a été détectée sur votre compte.</p>
                <ul>
                    <li>IP: {{ ip_address }}</li>
                    <li>Date: {{ date_connexion }}</li>
                    <li>Navigateur: {{ user_agent }}</li>
                </ul>
                <p>Si ce n'est pas vous, changez immédiatement votre mot de passe.</p>
                ''',
                'template_sms': 'Connexion suspecte détectée',
                'template_push': 'Connexion suspecte',
                'template_in_app': 'Connexion suspecte détectée sur votre compte'
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for type_data in notification_types:
            notification_type, created = NotificationType.objects.get_or_create(
                nom=type_data['nom'],
                defaults=type_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Créé: {type_data["nom"]}')
                )
            else:
                # Mettre à jour les champs si nécessaire
                updated = False
                for field, value in type_data.items():
                    if getattr(notification_type, field) != value:
                        setattr(notification_type, field, value)
                        updated = True
                
                if updated:
                    notification_type.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'↻ Mis à jour: {type_data["nom"]}')
                    )
                else:
                    self.stdout.write(
                        self.style.HTTP_INFO(f'- Existe déjà: {type_data["nom"]}')
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Terminé! {created_count} créés, {updated_count} mis à jour'
            )
        )
