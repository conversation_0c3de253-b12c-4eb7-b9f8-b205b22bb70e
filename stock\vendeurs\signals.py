from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Vendeur
from base.models import User
from responsable_magasin.models import Responsable_magasin
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Vendeur)
def validate_vendeur(sender, instance, **kwargs):
    """
    Valide le vendeur avant la sauvegarde
    """
    # Vérifier que le taux de commission est entre 0 et 1
    if not 0 <= instance.taux_commission <= 1:
        raise ValueError("Le taux de commission doit être entre 0 et 1")
    
   

@receiver(post_save, sender=Vendeur)
def handle_vendeur_save(sender, instance, created, **kwargs):
    """
    Gère les actions après la sauvegarde d'un vendeur
    """
    try:
        if created:
            logger.info(f"Nouveau vendeur créé: {instance}")
        else:
            logger.info(f"Vendeur mis à jour: {instance}")
            
        # Mettre à jour le statut actif du vendeur
        if not instance.actif:
            # Désactiver les ventes en cours si nécessaire
            from ventes.models import Vente
            ventes_actives = Vente.objects.filter(
                vendeur=instance,
                statut='EN_COURS'
            )
            if ventes_actives.exists():
                ventes_actives.update(statut='ANNULEE')
                logger.info(f"Ventes annulées pour le vendeur {instance}")
                
    except Exception as e:
        logger.error(f"Erreur lors de la sauvegarde du vendeur: {str(e)}")

@receiver(post_delete, sender=Vendeur)
def handle_vendeur_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'un vendeur
    """
    try:
        # Archiver les ventes associées au vendeur
        from ventes.models import Vente
        ventes = Vente.objects.filter(vendeur=instance)
        if ventes.exists():
            ventes.update(statut='ARCHIVEE')
            logger.info(f"Ventes archivées pour le vendeur {instance}")
            
        logger.info(f"Vendeur supprimé: {instance}")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression du vendeur: {str(e)}")

@receiver(pre_save, sender=Responsable_magasin)
def validate_responsable(sender, instance, **kwargs):
    if not instance.user:
        raise ValueError("Un utilisateur doit être associé au responsable")
    if not instance.magasin:
        raise ValueError("Un magasin doit être associé au responsable")
    
    if instance.user.role != User.Role.RESPONSABLE_MAGASIN:
        raise ValueError("L'utilisateur doit avoir le rôle de responsable de magasin") 