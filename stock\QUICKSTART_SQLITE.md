# 🚀 Démarrage Rapide - SQLite Optimisé

## ⚡ Démarrage en 2 minutes

### Option 1 : Script automatique (Recommandé)

#### Linux/Mac
```bash
chmod +x start_sqlite.sh
./start_sqlite.sh
```

#### Windows
```cmd
start_sqlite.bat
```

### Option 2 : Démarrage manuel

```bash
# 1. Environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate.bat  # Windows

# 2. Dépendances
pip install -r requirements.txt
pip install django-redis psutil

# 3. Base de données
python manage.py migrate
python manage.py create_notification_types
python manage.py optimize_database --all

# 4. Superutilisateur
python manage.py createsuperuser

# 5. Démarrage
python manage.py runserver
```

## 🌐 URLs importantes

- **Application** : http://localhost:8000
- **Administration** : http://localhost:8000/admin
- **API Documentation** : http://localhost:8000/swagger/
- **Métriques Performance** : http://localhost:8000/api/stocks/performance/

## 👤 Connexion par défaut

- **Username** : admin
- **Password** : admin123

## 🛠️ Commandes utiles

### Monitoring en temps réel
```bash
python manage.py performance_monitor
```

### Tests de charge
```bash
python manage.py run_load_tests --concurrent-users 5
```

### Optimisation SQLite
```bash
python manage.py optimize_database --all
```

### Nettoyage des notifications
```bash
python manage.py cleanup_notifications --days 30
```

## 📊 APIs de performance disponibles

### Alertes de stock avec cache
```bash
GET /api/stocks/alertes/
GET /api/stocks/alertes/?magasin_id=123
```

### Résumé de stock optimisé
```bash
GET /api/stocks/summary/
GET /api/stocks/summary/?magasin_id=123
```

### Métriques de performance
```bash
GET /api/stocks/performance/
```

### Notifications optimisées
```bash
GET /api/notifications/notifications/
GET /api/notifications/notifications/statistiques/
POST /api/notifications/notifications/marquer-toutes-lues/
```

## 🔧 Configuration avancée

### Variables d'environnement

Copiez `.env.sqlite` vers `.env` et ajustez :

```bash
cp .env.sqlite .env
```

### Redis (optionnel mais recommandé)

#### Installation Redis

**Ubuntu/Debian :**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

**macOS :**
```bash
brew install redis
brew services start redis
```

**Windows :**
```bash
# Utiliser Docker
docker run -d -p 6379:6379 redis:7-alpine
```

#### Test Redis
```bash
redis-cli ping
# Réponse attendue: PONG
```

### Docker (avec Redis)

```bash
# Démarrer Redis seulement
docker-compose up -d redis

# Ou tout démarrer
docker-compose up -d
```

## 📈 Performance avec SQLite

### Capacités typiques
- **👥 Utilisateurs concurrents** : 5-15
- **📊 Requêtes/seconde** : 100-500
- **💾 Taille recommandée** : < 100MB
- **🚀 Temps de réponse** : < 100ms

### Optimisations automatiques
- ✅ **WAL Mode** : Lectures concurrentes
- ✅ **Cache optimisé** : 1000 pages en mémoire
- ✅ **Index intelligents** : Créés automatiquement
- ✅ **Compression** : Headers optimisés
- ✅ **Monitoring** : Métriques temps réel

## 🚨 Monitoring et alertes

### Tableau de bord temps réel

```bash
# Monitoring continu
python manage.py performance_monitor --interval 30

# Alertes seulement
python manage.py performance_monitor --alerts-only

# Sauvegarde des métriques
python manage.py performance_monitor --output-file metrics.json
```

### Métriques disponibles

- **Système** : CPU, RAM, Disque
- **Base de données** : Taille, intégrité, performance
- **Cache** : Hit ratio, statistiques Redis
- **Application** : Utilisateurs actifs, alertes stock

### Alertes automatiques

- Requêtes lentes (>1s)
- Utilisation CPU élevée (>80%)
- Mémoire critique (>85%)
- Cache inefficace (<70%)
- Taille SQLite importante (>50MB)

## 🔍 Debugging

### Logs de performance

```bash
# Voir les logs
tail -f logs/performance.log

# Logs Django
tail -f logs/django.log
```

### Analyse des requêtes

```python
# Dans le shell Django
python manage.py shell

from performance.query_optimizer import QueryProfiler

with QueryProfiler() as profiler:
    # Votre code ici
    from stock_Pme.models import Stock
    Stock.objects.filter(quantite_disponible__lt=10).count()

print(profiler.get_report())
```

### Vérification SQLite

```python
# Dans le shell Django
from django.db import connection

with connection.cursor() as cursor:
    cursor.execute("PRAGMA integrity_check;")
    print("Intégrité:", cursor.fetchone()[0])
    
    cursor.execute("PRAGMA journal_mode;")
    print("Mode journal:", cursor.fetchone()[0])
```

## 📋 Checklist de déploiement

### Avant le déploiement
- [ ] Tests de charge exécutés
- [ ] Base de données optimisée
- [ ] Redis configuré (optionnel)
- [ ] Variables d'environnement définies
- [ ] Logs configurés

### Après le déploiement
- [ ] Vérifier les métriques
- [ ] Tester les APIs
- [ ] Configurer les alertes
- [ ] Planifier la maintenance

## 🔮 Migration future

### Quand migrer vers PostgreSQL ?

- **👥 > 15 utilisateurs concurrents**
- **💾 Base de données > 100MB**
- **🔄 Écritures intensives**
- **🌐 Déploiement multi-serveurs**

### Préparation migration

Le code est déjà compatible PostgreSQL :

```bash
# Quand vous serez prêt
pip install psycopg2-binary
# Changer DATABASE_URL dans .env
python manage.py migrate
```

## 🆘 Support

### Problèmes courants

**"Database is locked"**
```bash
# Vérifier les processus
ps aux | grep python
# Redémarrer l'application
```

**Performances dégradées**
```bash
# Optimiser la base
python manage.py optimize_database --vacuum --analyze-tables
```

**Cache inefficace**
```bash
# Vérifier Redis
redis-cli ping
# Vider le cache
python manage.py shell -c "from django.core.cache import cache; cache.clear()"
```

### Ressources

- **Documentation** : `performance/README.md`
- **Guide SQLite** : `performance/SQLITE_GUIDE.md`
- **Exemples** : `notifications/examples.py`

## 🎉 Prêt !

Votre application de gestion de stock est maintenant optimisée avec :

- ⚡ **SQLite haute performance**
- 🚀 **Cache Redis intelligent**
- 📊 **Monitoring temps réel**
- 🔍 **Alertes automatiques**
- 📈 **APIs optimisées**

**Temps de démarrage** : < 2 minutes
**Performance** : Production-ready
**Scalabilité** : 5-15 utilisateurs concurrents

Bon développement ! 🚀
