#!/usr/bin/env python
"""
Script de test pour le nouveau workflow de création magasin -> responsable
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from base.models import User
from magasins.models import Magasin
from entreprise.models import Entreprise
from responsable_magasin.models import Responsable_magasin

User = get_user_model()

def test_magasin_creation_workflow():
    """Test du workflow complet : entreprise -> magasin -> responsable"""
    print("🧪 Test: Workflow création magasin -> responsable")

    # Créer un admin pour les tests
    admin = User.objects.create_user(
        username='admin_workflow',
        telephone='9999999999',
        nom='Admin',
        prenom='Workflow',
        password='testpass123',
        role='ADMIN'
    )

    client = APIClient()
    client.force_authenticate(user=admin)

    try:
        # 1. Créer une entreprise
        print("\n📋 Étape 1: Création de l'entreprise")
        entreprise = Entreprise.objects.create(
            nom="Test Entreprise Workflow",
            adresse="123 Workflow Street",
            nif="9876543210123"
        )
        print(f"✅ Entreprise créée: {entreprise.nom}")

        # 2. Créer un magasin SANS responsable
        print("\n🏪 Étape 2: Création du magasin (sans responsable)")
        magasin_data = {
            "nom": "Magasin Test Workflow",
            "entreprise": str(entreprise.id)
        }

        response = client.post('/api/magasins/', magasin_data)
        print(f"Status création magasin: {response.status_code}")

        if response.status_code == 201:
            magasin_id = response.data['id']
            magasin = Magasin.objects.get(id=magasin_id)
            print(f"✅ Magasin créé: {magasin.nom}")
            print(f"   Responsable actuel: {magasin.responsable_magasin}")

            # 3. Créer un responsable et l'assigner au magasin
            print("\n👤 Étape 3: Création du responsable magasin")
            responsable_data = {
                "nom": "Responsable",
                "prenom": "Workflow",
                "telephone": "8888888888",
                "password": "testpass123",
                "magasin": magasin_id,
                "date_embauche": "2024-01-15",
                "adresse": "456 Responsable Street"
            }

            response = client.post('/api/responsables-magasin/', responsable_data)
            print(f"Status création responsable: {response.status_code}")

            if response.status_code == 201:
                print("✅ Responsable créé avec succès")

                # Vérifier l'assignation automatique
                magasin.refresh_from_db()
                print(f"   Responsable assigné au magasin: {magasin.responsable_magasin}")

                if magasin.responsable_magasin:
                    print("✅ Assignation automatique réussie!")
                    print(f"   Code responsable: {magasin.responsable_magasin.code_responsable_magasin}")
                else:
                    print("❌ Assignation automatique échouée")
            else:
                print(f"❌ Erreur création responsable: {response.data}")
        else:
            print(f"❌ Erreur création magasin: {response.data}")

    except Exception as e:
        print(f"❌ Erreur: {e}")

    finally:
        # Nettoyage
        print("\n🧹 Nettoyage...")
        User.objects.filter(telephone__in=['9999999999', '8888888888']).delete()
        Magasin.objects.filter(nom="Magasin Test Workflow").delete()
        Entreprise.objects.filter(nom="Test Entreprise Workflow").delete()
        print("✅ Nettoyage terminé")

def test_magasin_without_responsable():
    """Test qu'un magasin peut être créé sans responsable"""
    print("\n🧪 Test: Création magasin sans responsable")

    admin = User.objects.create_user(
        username='admin_test2',
        telephone='7777777777',
        nom='Admin',
        prenom='Test2',
        password='testpass123',
        role='ADMIN'
    )

    try:
        entreprise = Entreprise.objects.create(
            nom="Test Entreprise 2",
            adresse="789 Test Street",
            nif="1111111111111"
        )

        # Créer directement via le modèle
        magasin = Magasin.objects.create(
            nom="Magasin Sans Responsable",
            entreprise=entreprise
        )

        print(f"✅ Magasin créé sans responsable: {magasin.nom}")
        print(f"   Responsable: {magasin.responsable_magasin}")

        # Vérifier que la validation ne bloque pas
        magasin.full_clean()  # Cela devrait passer maintenant
        print("✅ Validation du modèle réussie")

    except Exception as e:
        print(f"❌ Erreur: {e}")

    finally:
        # Nettoyage
        User.objects.filter(telephone='7777777777').delete()
        Magasin.objects.filter(nom="Magasin Sans Responsable").delete()
        Entreprise.objects.filter(nom="Test Entreprise 2").delete()

if __name__ == "__main__":
    print("🚀 Test du nouveau workflow magasin -> responsable\n")

    test_magasin_creation_workflow()
    test_magasin_without_responsable()

    print("\n✅ Tests terminés")
