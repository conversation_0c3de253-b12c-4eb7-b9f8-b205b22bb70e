from rest_framework import serializers
from .models import AchatMagasin, DetailAchatMagasin
from produit.serializers import ProduitSerializer
from fournisseurs.serializers import FournisseurSerializer
from magasins.serializers import MagasinSerializer
from base.serializer import UserSerializer
from django.utils import timezone

class DetailAchatMagasinSerializer(serializers.ModelSerializer):
    produit_details = ProduitSerializer(source='produit', read_only=True)
    montant_total = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = DetailAchatMagasin
        fields = ['id', 'produit', 'produit_details', 'quantite_achetee', 
                 'prix_unitaire', 'montant_total', 'created_at', 'updated_at']
        read_only_fields = ['montant_total', 'created_at', 'updated_at']

    def validate_quantite_achetee(self, value):
        if value <= 0:
            raise serializers.ValidationError("La quantité achetée doit être positive.")
        return value

    def validate_prix_unitaire(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix unitaire doit être positif.")
        return value

    def validate(self, data):
        if 'quantite_achetee' in data and 'prix_unitaire' in data:
            montant = data['quantite_achetee'] * data['prix_unitaire']
            if montant <= 0:
                raise serializers.ValidationError("Le montant total doit être positif.")
        return data

class AchatMagasinSerializer(serializers.ModelSerializer):
    details = DetailAchatMagasinSerializer(many=True, read_only=True)
    fournisseur_details = FournisseurSerializer(source='fournisseur', read_only=True)
    magasin_details = MagasinSerializer(source='magasin', read_only=True)
    responsable_details = UserSerializer(source='responsable', read_only=True)
    statut_display = serializers.CharField(source='get_statut_display', read_only=True)
    jours_restants = serializers.SerializerMethodField()
    fournisseur = serializers.UUIDField()
    magasin = serializers.UUIDField()
    responsable = serializers.UUIDField(required=False, allow_null=True)

    class Meta:
        model = AchatMagasin
        fields = ['id', 'fournisseur', 'fournisseur_details', 'magasin', 'magasin_details',
                 'responsable', 'responsable_details', 'date_commande', 
                 'date_reception_prevue', 'statut', 'statut_display', 'montant_total',
                 'date_creation', 'date_modification', 'details', 'jours_restants']
        read_only_fields = ['montant_total', 'date_creation', 'date_modification']

    def get_jours_restants(self, obj):
        if obj.statut in ['EN_ATTENTE', 'CONFIRME']:
            delta = obj.date_reception_prevue - timezone.now().date()
            return delta.days
        return None

    def validate(self, data):
        if 'date_reception_prevue' in data and 'date_commande' in data:
            if data['date_reception_prevue'] < data['date_commande']:
                raise serializers.ValidationError(
                    "La date de réception prévue doit être postérieure à la date de commande."
                )
        return data

    def create(self, validated_data):
        from fournisseurs.models import Fournisseur
        from magasins.models import Magasin
        from base.models import User

        # Convertir les UUIDs en objets
        validated_data['fournisseur'] = Fournisseur.objects.get(id=validated_data['fournisseur'])
        validated_data['magasin'] = Magasin.objects.get(id=validated_data['magasin'])
        if 'responsable' in validated_data and validated_data['responsable']:
            validated_data['responsable'] = User.objects.get(id=validated_data['responsable'])

        achat = AchatMagasin.objects.create(**validated_data)
        return achat

    def update(self, instance, validated_data):
        from fournisseurs.models import Fournisseur
        from magasins.models import Magasin
        from base.models import User

        # Convertir les UUIDs en objets si présents
        if 'fournisseur' in validated_data:
            validated_data['fournisseur'] = Fournisseur.objects.get(id=validated_data['fournisseur'])
        if 'magasin' in validated_data:
            validated_data['magasin'] = Magasin.objects.get(id=validated_data['magasin'])
        if 'responsable' in validated_data:
            if validated_data['responsable']:
                validated_data['responsable'] = User.objects.get(id=validated_data['responsable'])
            else:
                validated_data['responsable'] = None

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.full_clean()
        instance.save()
        return instance