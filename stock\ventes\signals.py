from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Vente, DetailVente
from stock_Pme.models import Stock
from magasins.models import Magasin
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=DetailVente)
def update_stock_after_detail_vente(sender, instance, created, **kwargs):
    """
    Met à jour le stock après la création ou la modification d'un détail de vente
    """
    try:
        with transaction.atomic():
            magasin_principal = Magasin.objects.filter(actif=True).first()
            if not magasin_principal:
                logger.error("Aucun magasin actif trouvé")
                return

            stock = Stock.objects.get(
                entrepot__type_stock=instance.produit.type_stock,
                produit=instance.produit,
                magasin=magasin_principal
            )

            if created:
                # Nouveau détail de vente
                stock.retirer_produit(instance.quantite_vendue)
            else:
                # Modification d'un détail existant
                old_instance = DetailVente.objects.get(pk=instance.pk)
                difference = old_instance.quantite_vendue - instance.quantite_vendue
                if difference > 0:
                    stock.ajouter_produit(difference)
                elif difference < 0:
                    stock.retirer_produit(abs(difference))

    except Stock.DoesNotExist:
        logger.error(f"Stock non trouvé pour le produit {instance.produit.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}")

@receiver(post_delete, sender=DetailVente)
def restore_stock_after_detail_vente_delete(sender, instance, **kwargs):
    """
    Restaure le stock après la suppression d'un détail de vente
    """
    try:
        with transaction.atomic():
            magasin_principal = Magasin.objects.filter(actif=True).first()
            if not magasin_principal:
                logger.error("Aucun magasin actif trouvé")
                return

            stock = Stock.objects.get(
                entrepot__type_stock=instance.produit.type_stock,
                produit=instance.produit,
                magasin=magasin_principal
            )
            stock.ajouter_produit(instance.quantite_vendue)

    except Stock.DoesNotExist:
        logger.error(f"Stock non trouvé pour le produit {instance.produit.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la restauration du stock: {str(e)}")

@receiver(pre_save, sender=Vente)
def validate_vente(sender, instance, **kwargs):
    """
    Valide la vente avant la sauvegarde
    """
    if instance.montant_total < 0:
        raise ValueError("Le montant total ne peut pas être négatif")
    
    if instance.vendeur and instance.vendeur.magasin != instance.magasin:
        raise ValueError("Le vendeur doit appartenir au magasin de la vente") 