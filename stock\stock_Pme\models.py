import uuid
from django.db import models
from produit.models import Produit
from entrepot.models import Location, Entrepot
from magasins.models import Magasin

class MouvementStock(models.Model):
    TYPES_MOUVEMENT = [
        ('ENTREE', 'Entrée en stock'),
        ('SORTIE', 'Sortie de stock'),
        ('TRANSFERT', 'Transfert'),
        ('AJUSTEMENT', 'Ajustement')
    ]
    
    id_mouvement = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    produit = models.ForeignKey(Produit, on_delete=models.PROTECT)
    location = models.ForeignKey(Location, on_delete=models.PROTECT)
    quantite = models.PositiveIntegerField()
    type_mouvement = models.CharField(max_length=20, choices=TYPES_MOUVEMENT)
    date_mouvement = models.DateTimeField(auto_now_add=True)
    utilisateur = models.CharField(max_length=100)
    notes = models.TextField(blank=True)

    class Meta:
        verbose_name = "Mouvement de stock"
        verbose_name_plural = "Mouvements de stock"

    def __str__(self):
        return f"{self.get_type_mouvement_display()} - {self.produit.nom} ({self.quantite})"

class Stock(models.Model):
    id_stock = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quantite_disponible = models.IntegerField(default=0)
    seuil_alerte = models.IntegerField(default=10)
    updatedAt = models.DateField(auto_now=True)
    produit = models.ForeignKey(Produit, on_delete=models.PROTECT)
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    magasin = models.ForeignKey(Magasin, on_delete=models.CASCADE, related_name='stocks')
    entrepot = models.ForeignKey(Entrepot, on_delete=models.CASCADE, related_name='stocks')

    class Meta:
        unique_together = ('produit', 'magasin', 'entrepot')
        verbose_name = "Stock"
        verbose_name_plural = "Stocks"
        indexes = [
            models.Index(fields=['produit', 'magasin']),
            models.Index(fields=['entrepot']),
        ]

    def __str__(self):
        return f"{self.produit} - {self.magasin.nom} ({self.quantite_disponible})"

    def ajouterStock(self, quantity):
        """Ajoute de la quantité au stock"""
        self.quantite_disponible += quantity
        self.save()
        return self

    def retirerStock(self, quantity):
        """Retire de la quantité du stock si disponible"""
        if self.quantite_disponible >= quantity:
            self.quantite_disponible -= quantity
            self.save()
            return {
                'success': True,
                'nouvelle_quantite': self.quantite_disponible,
                'quantite_retiree': quantity
            }
        return {
            'success': False,
            'message': 'Quantité insuffisante en stock',
            'quantite_disponible': self.quantite_disponible,
            'quantite_demandee': quantity
        }

    def vérifierSeuilAlerte(self):
        """Vérifie si le stock est en dessous du seuil d'alerte"""
        return self.quantite_disponible < self.seuil_alerte
    
  

    @classmethod
    def getStockByProduct(cls, productId):
        """Récupère tous les stocks pour un produit donné"""
        return cls.objects.filter(produit_id=productId)

def get_absolute_url(self):
    from django.urls import reverse
    return reverse('rapport-stock', kwargs={'entrepot_id': self.id_entrepot})