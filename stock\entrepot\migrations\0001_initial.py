# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entreprise', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Entrepot',
            fields=[
                ('id_entrepot', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('adresse', models.TextField()),
                ('capacite_stockage', models.IntegerField()),
                ('statut', models.BooleanField(default=True)),
                ('entreprise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='entrepots', to='entreprise.entreprise')),
            ],
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('type_stock', models.CharField(choices=[('FINIS', 'Produits finis'), ('MATIERE', 'Matières premières'), ('EMBALLAGE', 'Emballage et packaging'), ('SEMI_FINI', 'Produits semi-finis'), ('MARCHANDISE', 'Marchandises'), ('CONSOMMABLE', 'Consommables')], max_length=20)),
                ('entrepot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='entrepot.entrepot')),
            ],
        ),
    ]
