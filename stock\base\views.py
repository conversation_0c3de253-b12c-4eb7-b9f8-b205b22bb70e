from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status, permissions
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
from .models import Note, User
from .serializer import NoteSerializer, UserSerializer, LoginSerializer, AdminRegistrationSerializer
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class RegisterView(APIView):
    @swagger_auto_schema(
        operation_description="Inscription pour créer uniquement des comptes administrateur et une entreprise associée",
        request_body=AdminRegistrationSerializer,
        responses={
            201: openapi.Response(
                description="Inscription réussie - Compte administrateur et entreprise créés",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        """Inscription pour créer uniquement des comptes administrateur et une entreprise associée"""
        serializer = AdminRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            # Récupérer l'entreprise créée via le serializer
            entreprise = getattr(serializer, '_entreprise', None)
            # Génération des tokens JWT
            refresh = RefreshToken.for_user(user)
            entreprise_data = None
            if entreprise:
                entreprise_data = {
                    'id': str(entreprise.id),
                    'nom': entreprise.nom,
                    'adresse': entreprise.adresse,
                    'nif': entreprise.nif,
                    'date_creation': entreprise.date_creation,
                    'statut': entreprise.statut,
                    'administrateur': str(entreprise.administrateur.id_utilisateur) if entreprise.administrateur else None
                }
            return Response({
                'user': {
                    'id_utilisateur': str(user.id_utilisateur),
                    'username': user.username,
                    'nom': user.nom,
                    'prenom': user.prenom,
                    'email': user.email,
                    'telephone': user.telephone,
                    'role': user.role,
                    'statut': user.statut
                },
                'entreprise': entreprise_data,
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'message': 'Inscription réussie - Compte administrateur et entreprise créés'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'errors': serializer.errors,
            'message': "Erreur lors de l'inscription"
        }, status=status.HTTP_400_BAD_REQUEST)

class LoginView(APIView):
    @swagger_auto_schema(
        operation_description="Connexion utilisateur (username/email/téléphone + mot de passe)",
        request_body=LoginSerializer,
        responses={
            200: openapi.Response(
                description="Connexion réussie",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            403: "Forbidden"
        }
    )
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            identifiant = serializer.validated_data['identifiant']  # Peut être username, email ou téléphone
            password = serializer.validated_data['password']

            # Le backend d'authentification personnalisé déterminera le type d'identifiant
            user = authenticate(request, username=identifiant, password=password)

            if user:
                if not user.statut:
                    return Response(
                        {'error': 'Ce compte est désactivé'},
                        status=status.HTTP_403_FORBIDDEN
                    )

                # Génération des tokens JWT
                refresh = RefreshToken.for_user(user)

                return Response({
                    'user': UserSerializer(user).data,
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                    'message': 'Connexion réussie'
                })

            return Response({
                'error': 'Identifiant ou mot de passe incorrect',
                'message': 'Échec de la connexion'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# vue pour la déconnexion
class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Déconnexion utilisateur (blacklist du refresh token)",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['refresh'],
            properties={
                'refresh': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(description="Déconnexion réussie"),
            400: "Bad Request"
        }
    )
    def post(self, request):
        try:
            # Obtenir le token de rafraîchissement
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                return Response({
                    'error': 'Le token de rafraîchissement est requis',
                    'message': 'Échec de la déconnexion'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Mettre le token en liste noire
            token = RefreshToken(refresh_token)
            token.blacklist()

            return Response({
                'message': 'Déconnexion réussie'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e),
                'message': 'Échec de la déconnexion'
            }, status=status.HTTP_400_BAD_REQUEST)

# permission pour chaque utilisateur
class IsAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.role == 'ADMIN'

class IsEntreprise(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.role == 'ENTREPRISE'

class IsMagasin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.role == 'MAGASIN'

class isVendeur(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.role == 'VENDEUR'

class isResponsable_Magasin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.role == 'RESPONSABLE_MAGASIN'


class DeleteUserView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Supprimer un utilisateur par ID (admin uniquement)",
        responses={
            204: openapi.Response(description="Utilisateur supprimé avec succès"),
            403: "Forbidden",
            404: "Not Found"
        }
    )
    def delete(self, request, user_id):
        try:
            user_to_delete = User.objects.get(id_utilisateur=user_id)
            if user_to_delete == request.user:
                return Response({
                    'error': 'Vous ne pouvez pas supprimer votre propre compte',
                    'message': 'Suppression échouée'
                }, status=status.HTTP_403_FORBIDDEN)

            user_to_delete.delete()
            return Response({
                'message': 'Utilisateur supprimé avec succès'
            }, status=status.HTTP_204_NO_CONTENT)
        except User.DoesNotExist:
            return Response({
                'error': 'Utilisateur non trouvé',
            }, status=status.HTTP_404_NOT_FOUND)

@swagger_auto_schema(
    method='get',
    operation_description="Récupérer les notes de l'utilisateur connecté",
    responses={
        200: openapi.Response(
            description="Liste des notes",
            schema=openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT))
        )
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notes(request):
    user = request.user
    notes = Note.objects.filter(owner=user)
    serializer = NoteSerializer(notes, many=True)
    return Response(serializer.data)

class RefreshTokenView(APIView):
    @swagger_auto_schema(
        operation_description="Rafraîchir le token d'accès à partir d'un refresh token",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['refresh'],
            properties={
                'refresh': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(
                description="Token rafraîchi avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                return Response({
                    'error': 'Refresh token requis',
                    'message': 'Le refresh token est nécessaire'
                }, status=status.HTTP_400_BAD_REQUEST)

            token = RefreshToken(refresh_token)
            access_token = str(token.access_token)

            return Response({
                'access': access_token,
                'message': 'Token rafraîchi avec succès'
            })

        except Exception as e:
            return Response({
                'error': str(e),
                'message': 'Échec du rafraîchissement du token'
            }, status=status.HTTP_400_BAD_REQUEST)