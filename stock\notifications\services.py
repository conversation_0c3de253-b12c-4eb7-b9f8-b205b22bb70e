import logging
import json
# import requests  # Décommenté quand requests sera installé
from typing import List, Dict, Any, Optional
from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template import Template, Context
from django.utils import timezone
from django.db import transaction
from .models import Notification, NotificationType, NotificationPreference, NotificationBatch

logger = logging.getLogger(__name__)


class NotificationService:
    """Service principal pour la gestion des notifications"""

    @staticmethod
    def creer_notification(
        type_notification: str,
        destinataire,
        titre: str,
        message: str,
        donnees_contexte: Dict[str, Any] = None,
        canal: str = 'IN_APP',
        priorite: str = None,
        objet_lie=None,
        date_expiration=None
    ) -> Notification:
        """Crée une nouvelle notification"""

        try:
            # Récupérer le type de notification
            notification_type = NotificationType.objects.get(nom=type_notification, actif=True)
        except NotificationType.DoesNotExist:
            logger.error(f"Type de notification '{type_notification}' non trouvé")
            raise ValueError(f"Type de notification '{type_notification}' non trouvé")

        # Utiliser la priorité par défaut si non spécifiée
        if not priorite:
            priorite = notification_type.priorite_defaut

        # Vérifier les préférences de l'utilisateur
        preferences, _ = NotificationPreference.objects.get_or_create(user=destinataire)

        if not preferences.is_notification_allowed(notification_type):
            logger.info(f"Notification bloquée par les préférences de {destinataire.username}")
            return None

        # Créer la notification
        notification = Notification.objects.create(
            type_notification=notification_type,
            destinataire=destinataire,
            titre=titre,
            message=message,
            donnees_contexte=donnees_contexte or {},
            canal=canal,
            priorite=priorite,
            date_expiration=date_expiration
        )

        # Ajouter la référence à l'objet lié si fourni
        if objet_lie:
            from django.contrib.contenttypes.models import ContentType
            notification.content_type = ContentType.objects.get_for_model(objet_lie)
            notification.object_id = str(objet_lie.pk)
            notification.save()

        logger.info(f"Notification créée: {notification.id} pour {destinataire.username}")
        return notification

    @staticmethod
    def envoyer_notification(notification: Notification) -> bool:
        """Envoie une notification selon son canal"""

        if notification.statut != 'PENDING':
            logger.warning(f"Tentative d'envoi d'une notification non en attente: {notification.id}")
            return False

        if notification.est_expiree:
            notification.statut = 'CANCELLED'
            notification.save()
            logger.info(f"Notification expirée annulée: {notification.id}")
            return False

        try:
            success = False

            if notification.canal == 'EMAIL':
                success = EmailNotificationService.envoyer_email(notification)
            elif notification.canal == 'SMS':
                success = SMSNotificationService.envoyer_sms(notification)
            elif notification.canal == 'PUSH':
                success = PushNotificationService.envoyer_push(notification)
            elif notification.canal == 'IN_APP':
                success = InAppNotificationService.envoyer_in_app(notification)

            if success:
                notification.marquer_comme_envoyee()
                logger.info(f"Notification envoyée avec succès: {notification.id}")
            else:
                notification.marquer_comme_echouee("Échec de l'envoi")
                logger.error(f"Échec de l'envoi de la notification: {notification.id}")

            return success

        except Exception as e:
            notification.marquer_comme_echouee(str(e))
            logger.error(f"Erreur lors de l'envoi de la notification {notification.id}: {str(e)}")
            return False

    @staticmethod
    def envoyer_notifications_en_lot(notifications: List[Notification]) -> Dict[str, int]:
        """Envoie plusieurs notifications en lot"""

        resultats = {'succes': 0, 'echecs': 0}

        for notification in notifications:
            if NotificationService.envoyer_notification(notification):
                resultats['succes'] += 1
            else:
                resultats['echecs'] += 1

        return resultats

    @staticmethod
    def creer_et_envoyer_notification(
        type_notification: str,
        destinataires: List,
        titre: str,
        message: str,
        donnees_contexte: Dict[str, Any] = None,
        canal: str = 'IN_APP',
        priorite: str = None,
        objet_lie=None,
        envoyer_immediatement: bool = True
    ) -> List[Notification]:
        """Crée et envoie des notifications à plusieurs destinataires"""

        notifications = []

        for destinataire in destinataires:
            notification = NotificationService.creer_notification(
                type_notification=type_notification,
                destinataire=destinataire,
                titre=titre,
                message=message,
                donnees_contexte=donnees_contexte,
                canal=canal,
                priorite=priorite,
                objet_lie=objet_lie
            )

            if notification:
                notifications.append(notification)

                if envoyer_immediatement:
                    NotificationService.envoyer_notification(notification)

        return notifications


class EmailNotificationService:
    """Service pour l'envoi d'emails"""

    @staticmethod
    def envoyer_email(notification: Notification) -> bool:
        """Envoie une notification par email"""

        if not settings.NOTIFICATIONS_SETTINGS.get('EMAIL_ENABLED', True):
            logger.info("Envoi d'emails désactivé dans la configuration")
            return False

        try:
            # Vérifier les préférences utilisateur
            preferences = getattr(notification.destinataire, 'notification_preferences', None)
            if preferences and not preferences.email_enabled:
                logger.info(f"Email désactivé pour {notification.destinataire.username}")
                return False

            # Préparer le contenu
            template = notification.type_notification.template_email or notification.message
            contenu_html = EmailNotificationService._render_template(template, notification.donnees_contexte)

            # Envoyer l'email
            email = EmailMultiAlternatives(
                subject=notification.titre,
                body=notification.message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[notification.destinataire.email]
            )

            if contenu_html != notification.message:
                email.attach_alternative(contenu_html, "text/html")

            email.send()

            logger.info(f"Email envoyé à {notification.destinataire.email}")
            return True

        except Exception as e:
            logger.error(f"Erreur envoi email: {str(e)}")
            return False

    @staticmethod
    def _render_template(template_str: str, context_data: Dict[str, Any]) -> str:
        """Rend un template avec les données de contexte"""
        try:
            template = Template(template_str)
            context = Context(context_data)
            return template.render(context)
        except Exception as e:
            logger.error(f"Erreur rendu template: {str(e)}")
            return template_str


class SMSNotificationService:
    """Service pour l'envoi de SMS via Twilio"""

    @staticmethod
    def envoyer_sms(notification: Notification) -> bool:
        """Envoie une notification par SMS"""

        if not settings.NOTIFICATIONS_SETTINGS.get('SMS_ENABLED', False):
            logger.info("Envoi de SMS désactivé dans la configuration")
            return False

        try:
            # Vérifier les préférences utilisateur
            preferences = getattr(notification.destinataire, 'notification_preferences', None)
            if preferences and not preferences.sms_enabled:
                logger.info(f"SMS désactivé pour {notification.destinataire.username}")
                return False

            # Vérifier la configuration Twilio
            if not all([settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN, settings.TWILIO_PHONE_NUMBER]):
                logger.error("Configuration Twilio incomplète")
                return False

            # Préparer le message
            template = notification.type_notification.template_sms or notification.message
            message_sms = SMSNotificationService._render_template(template, notification.donnees_contexte)

            # Limiter à 160 caractères
            if len(message_sms) > 160:
                message_sms = message_sms[:157] + "..."

            # Envoyer via Twilio (simulation - remplacer par vraie intégration)
            # from twilio.rest import Client
            # client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            # message = client.messages.create(
            #     body=message_sms,
            #     from_=settings.TWILIO_PHONE_NUMBER,
            #     to=notification.destinataire.telephone
            # )

            logger.info(f"SMS envoyé à {notification.destinataire.telephone}")
            return True

        except Exception as e:
            logger.error(f"Erreur envoi SMS: {str(e)}")
            return False

    @staticmethod
    def _render_template(template_str: str, context_data: Dict[str, Any]) -> str:
        """Rend un template SMS avec les données de contexte"""
        try:
            template = Template(template_str)
            context = Context(context_data)
            return template.render(context)
        except Exception as e:
            logger.error(f"Erreur rendu template SMS: {str(e)}")
            return template_str


class PushNotificationService:
    """Service pour l'envoi de notifications push via Firebase"""

    @staticmethod
    def envoyer_push(notification: Notification) -> bool:
        """Envoie une notification push"""

        if not settings.NOTIFICATIONS_SETTINGS.get('PUSH_ENABLED', True):
            logger.info("Envoi de push désactivé dans la configuration")
            return False

        try:
            # Vérifier les préférences utilisateur
            preferences = getattr(notification.destinataire, 'notification_preferences', None)
            if preferences and not preferences.push_enabled:
                logger.info(f"Push désactivé pour {notification.destinataire.username}")
                return False

            # Simulation d'envoi Firebase (remplacer par vraie intégration)
            logger.info(f"Push notification envoyée à {notification.destinataire.username}")
            return True

        except Exception as e:
            logger.error(f"Erreur envoi push: {str(e)}")
            return False


class InAppNotificationService:
    """Service pour les notifications in-app"""

    @staticmethod
    def envoyer_in_app(notification: Notification) -> bool:
        """Marque une notification in-app comme envoyée"""

        try:
            # Vérifier les préférences utilisateur
            preferences = getattr(notification.destinataire, 'notification_preferences', None)
            if preferences and not preferences.in_app_enabled:
                logger.info(f"Notifications in-app désactivées pour {notification.destinataire.username}")
                return False

            # Pour les notifications in-app, on les marque simplement comme envoyées
            # Elles seront récupérées via l'API
            logger.info(f"Notification in-app créée pour {notification.destinataire.username}")
            return True

        except Exception as e:
            logger.error(f"Erreur notification in-app: {str(e)}")
            return False
