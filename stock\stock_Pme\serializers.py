from rest_framework import serializers
from .models import Stock
from produit.serializers import ProduitSerializer
from magasins.serializers import MagasinSerializer

class StockSerializer(serializers.ModelSerializer):
    produit_details = ProduitSerializer(source='produit', read_only=True)
    magasin_details = MagasinSerializer(source='magasin', read_only=True)
    utilisateur_details = serializers.CharField(source='utilisateur.get_full_name', read_only=True)
    class Meta:
        model = Stock
        fields = '__all__'        
        read_only_fields = ('date', 'utilisateur')        

