# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clients', '0001_initial'),
        ('entreprise', '0001_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
        ('vendeurs', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Vente',
            fields=[
                ('id_vente', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('montant_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('montant_ht', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('montant_tva', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('date_vente', models.DateTimeField(auto_now_add=True)),
                ('mode_paiement', models.CharField(choices=[('ESPECES', 'Espèces'), ('CARTE', 'Carte bancaire'), ('CHEQUE', 'Chèque'), ('VIREMENT', 'Virement')], default='ESPECES', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='clients.client')),
                ('entreprise', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ventes', to='entreprise.entreprise')),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ventes', to='magasins.magasin')),
                ('vendeur', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='vendeurs.vendeur')),
            ],
            options={
                'verbose_name': 'Vente',
                'verbose_name_plural': 'Ventes',
            },
        ),
        migrations.CreateModel(
            name='DetailVente',
            fields=[
                ('id_detail_vente', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite_vendue', models.PositiveIntegerField()),
                ('prix_unitaire', models.DecimalField(decimal_places=2, max_digits=10)),
                ('prix_achat', models.DecimalField(decimal_places=2, max_digits=10)),
                ('taux_tva', models.DecimalField(decimal_places=2, default=20.0, max_digits=5)),
                ('montant_ht', models.DecimalField(decimal_places=2, max_digits=10)),
                ('montant_tva', models.DecimalField(decimal_places=2, max_digits=10)),
                ('montant_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('produit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit')),
                ('vente', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='ventes.vente')),
            ],
            options={
                'verbose_name': 'Détail Vente',
                'verbose_name_plural': 'Détails Ventes',
            },
        ),
        migrations.AddIndex(
            model_name='vente',
            index=models.Index(fields=['date_vente'], name='ventes_vent_date_ve_1d28bd_idx'),
        ),
        migrations.AddIndex(
            model_name='vente',
            index=models.Index(fields=['client'], name='ventes_vent_client__a61544_idx'),
        ),
        migrations.AddIndex(
            model_name='vente',
            index=models.Index(fields=['vendeur'], name='ventes_vent_vendeur_e271e7_idx'),
        ),
        migrations.AddIndex(
            model_name='vente',
            index=models.Index(fields=['magasin'], name='ventes_vent_magasin_5c5cf7_idx'),
        ),
        migrations.AddIndex(
            model_name='detailvente',
            index=models.Index(fields=['vente'], name='ventes_deta_vente_i_94d7ab_idx'),
        ),
        migrations.AddIndex(
            model_name='detailvente',
            index=models.Index(fields=['produit'], name='ventes_deta_produit_0ea7f7_idx'),
        ),
    ]
