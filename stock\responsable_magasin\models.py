from django.db import models 
from base.models import User
from django.core.validators import RegexValidator
from inventaire.models import Inventaire
from achat_magasin.models import AchatMagasin
from stock_Pme.models import Stock
from django.utils import timezone

class Responsable_magasin(models.Model):
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'role': User.Role.RESPONSABLE_MAGASIN}
    )
    code_responsable_magasin = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^RESP-[0-9]{6}$', 'Le code doit être au format RESP-XXXXXX')]
    )
    magasin = models.ForeignKey('magasins.Magasin', on_delete=models.CASCADE, related_name='responsables')
    date_embauche = models.DateField()
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.user.prenom} {self.user.nom} ({self.code_responsable_magasin})"

    def planifier_inventaire(self, date_planifiee=None):
        """Planifie un nouvel inventaire pour le magasin"""
        inventaire = Inventaire.objects.create(
            magasin=self.magasin,
            entrepot=self.magasin.entrepot,
            responsable=self.user,
            date_inventaire=date_planifiee
        )
        return inventaire

    def verifier_stock_critique(self):
        """Vérifie les produits dont le stock est en dessous du seuil critique"""
        stocks_critiques = Stock.objects.filter(
            magasin=self.magasin,
            quantite__lte=models.F('seuil_alerte')
        ).select_related('produit')
        return stocks_critiques

    def creer_commande_fournisseur(self, fournisseur, produits_data):
        """Crée une nouvelle commande pour un fournisseur"""
        achat = AchatMagasin.objects.create(
            fournisseur=fournisseur,
            magasin=self.magasin,
            responsable=self.user
        )
        
        for produit_data in produits_data:
            achat.creer_details_achat(
                produit=produit_data['produit'],
                data={
                    'quantite': produit_data['quantite'],
                    'prix_unitaire': produit_data.get('prix_unitaire')
                }
            )
        return achat

    def suivre_commandes_en_cours(self):
        """Récupère toutes les commandes en cours pour le magasin"""
        return AchatMagasin.objects.filter(
            magasin=self.magasin,
            statut__in=['EN_ATTENTE', 'CONFIRME']
        ).select_related('fournisseur')

    def suivre_livraisons_prevues(self):
        """Récupère les livraisons prévues pour aujourd'hui"""
        return AchatMagasin.objects.filter(
            magasin=self.magasin,
            date_reception_prevue=timezone.now().date(),
            statut__in=['CONFIRME', 'LIVRE']
        ).select_related('fournisseur')

    def generer_rapport_stock(self):
        """Génère un rapport détaillé de l'état des stocks"""
        stocks = Stock.objects.filter(magasin=self.magasin).select_related('produit')
        rapport = {
            'magasin': self.magasin.nom,
            'date': timezone.now().date(),
            'stocks': [],
            'total_valeur': 0
        }
        
        for stock in stocks:
            valeur_stock = stock.quantite * stock.produit.prix_achat
            rapport['stocks'].append({
                'produit': stock.produit.nom,
                'quantite': stock.quantite,
                'seuil_alerte': stock.seuil_alerte,
                'valeur_stock': valeur_stock
            })
            rapport['total_valeur'] += valeur_stock
            
        return rapport

    def suivre_mouvements_stock(self, date_debut=None, date_fin=None):
        """Suivi des mouvements de stock sur une période donnée"""
        if not date_debut:
            date_debut = timezone.now().date()
        if not date_fin:
            date_fin = date_debut

        mouvements = Stock.objects.filter(
            magasin=self.magasin,
            date_modification__date__range=[date_debut, date_fin]
        ).select_related('produit')
        
        return mouvements

    class Meta:
        verbose_name = "Responsable de magasin"
        verbose_name_plural = "Responsables de magasin"
        indexes = [
            models.Index(fields=['code_responsable_magasin']),
            models.Index(fields=['magasin']),
        ]
    
