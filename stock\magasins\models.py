import uuid
from django.db import models
from entrepot.models import Entrepot
from entreprise.models import Entreprise
from django.core.validators import RegexValidator

class LocalisationMagasin(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    adresse = models.TextField()
    ville = models.CharField(max_length=100)
    code_postal = models.CharField(max_length=20)
    pays = models.CharField(max_length=100)
    telephone = models.CharField(
        max_length=13,
        unique=True,
        validators=[RegexValidator(r'^\+261\d{7,9}$', 'Le numéro doit être au format +261xxxxxxx')]
    )
    email = models.EmailField(blank=True, null=True)
    actif = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.ville} - {self.adresse}"

class Magasin(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100)
    localisation = models.OneToOneField(LocalisationMagasin, on_delete=models.CASCADE, related_name='magasin')
    responsable_magasin = models.ForeignKey('responsable_magasin.Responsable_magasin', on_delete=models.SET_NULL, null=True, related_name='magasins_geres')
    entreprise = models.ForeignKey(Entreprise, on_delete=models.CASCADE, related_name='magasins')
    entrepot = models.ForeignKey(Entrepot, on_delete=models.CASCADE, related_name='magasins', null=True, blank=True)
    actif = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Magasin"
        verbose_name_plural = "Magasins"
        indexes = [
            models.Index(fields=['nom']),
            models.Index(fields=['responsable_magasin']),
            models.Index(fields=['entrepot']),
        ]

    def __str__(self):
        return f"{self.nom}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if not hasattr(self, 'localisation'):
            raise ValidationError("Un magasin doit avoir une localisation.")
        # Note: Un responsable peut être assigné plus tard après la création du magasin

    @classmethod
    def creer_magasin(cls, data):
        return cls.objects.create(**data)

    def mettre_a_jour_magasin(self, data):
        for field, value in data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
        return self

    def generer_rapport_ventes(self):
        from ventes.models import Vente, DetailVente
        from django.db.models import Sum, Count
        ventes = DetailVente.objects.filter(
            produit__magasin=self
        ).aggregate(
            total_ventes=Sum('montant_total'),
            nombre_ventes=Count('id_detail_vente')
        )
        return {
            'magasin': self.nom,
            'total_ventes': ventes['total_ventes'] or 0,
            'nombre_ventes': ventes['nombre_ventes'] or 0,
            'responsable': self.responsable_magasin.user.get_full_name() if self.responsable_magasin else None,
            'nombre_vendeurs': self.vendeurs.filter(actif=True).count()
        }