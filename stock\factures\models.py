import uuid
from django.db import models
from ventes.models import Vente
from clients.models import Client
from entreprise.models import Entreprise

class Facture(models.Model):
    id_facture = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    vente = models.OneToOneField(Vente, on_delete=models.CASCADE, related_name='facture')
    numero_facture = models.CharField(max_length=50, unique=True)
    date_emission = models.DateTimeField(auto_now_add=True)
    date_echeance = models.DateTimeField(null=True, blank=True)
    statut = models.CharField(max_length=20, choices=[
        ('EN_ATTENTE', 'En attente'),
        ('PAYEE', 'Payée'),
        ('ANNULEE', 'Annulée')
    ], default='EN_ATTENTE')
    montant_ht = models.DecimalField(max_digits=10, decimal_places=2)
    montant_tva = models.DecimalField(max_digits=10, decimal_places=2)
    montant_total = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Facture"
        verbose_name_plural = "Factures"
        indexes = [
            models.Index(fields=['numero_facture']),
            models.Index(fields=['date_emission']),
            models.Index(fields=['statut']),
        ]

    def __str__(self):
        return f"Facture {self.numero_facture} - {self.montant_total}"

    @classmethod
    def generer_facture(cls, vente):
        """Génère une facture à partir d'une vente"""
        # Générer un numéro de facture unique
        dernier_numero = cls.objects.order_by('-numero_facture').first()
        if dernier_numero:
            numero = int(dernier_numero.numero_facture.split('-')[-1]) + 1
        else:
            numero = 1
        numero_facture = f"FACT-{numero:06d}"

        # Créer la facture
        facture = cls.objects.create(
            vente=vente,
            numero_facture=numero_facture,
            montant_ht=vente.montant_ht,
            montant_tva=vente.montant_tva,
            montant_total=vente.montant_total
        )
        return facture 