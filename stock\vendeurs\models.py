from django.db import models
from base.models import User
from django.core.validators import RegexValidator
from magasins.models import Magasin

class Vendeur(models.Model):
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'role': User.Role.VENDEUR}
    )
    code_vendeur = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^VEND-[0-9]{6}$', 'Le code doit être au format VEND-XXXXXX')]
    )
    magasin = models.ForeignKey(Magasin, on_delete=models.CASCADE, related_name='vendeurs')
    date_embauche = models.DateField()
    taux_commission = models.FloatField(default=0.1)
    notes = models.TextField(blank=True)
    actif = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Vendeur"
        verbose_name_plural = "Vendeurs"
        unique_together = ['user', 'magasin']

    def __str__(self):
        return f"{self.user.prenom} {self.user.nom} ({self.code_vendeur}) - {self.magasin.nom}"

    def get_performance(self):
        from ventes.models import Vente
        from django.db.models import Sum
        from django.utils import timezone
        ventes = Vente.objects.filter(
            vendeur=self,
            date_vente__month=timezone.now().month
        ).aggregate(
            count=models.Count('id_vente'),
            total=Sum('montant_total')
        )
        return {
            'ventes_mois': ventes['count'],
            'chiffre_affaires': ventes['total'] or 0
        }