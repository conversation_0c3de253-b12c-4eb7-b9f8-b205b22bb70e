from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Vente, DetailVente
from vendeurs.models import Vendeur
from magasins.models import Magasin, LocalisationMagasin
from produit.models import Produit
from clients.models import Client
from entreprise.models import Entreprise
from categorie.models import Categorie
from entrepot.models import Entrepot
from stock_Pme.models import Stock, Location
from responsable_magasin.models import Responsable_magasin
import uuid
from decimal import Decimal
from datetime import datetime

User = get_user_model()


class VenteTestCase(APITestCase):
    """Tests pour les fonctionnalités de vente"""

    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer une entreprise
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            telephone="+************",
            email="<EMAIL>"
        )

        # Créer une localisation pour le magasin
        self.localisation = LocalisationMagasin.objects.create(
            adresse="456 Magasin Street",
            ville="Test City",
            code_postal="12345",
            pays="Madagascar",
            telephone="+************"
        )

        # Créer un entrepôt
        self.entrepot = Entrepot.objects.create(
            nom="Entrepôt Test",
            type_stock="FINIS",
            entreprise=self.entreprise
        )

        # Créer un magasin
        self.magasin = Magasin.objects.create(
            nom="Magasin Test",
            localisation=self.localisation,
            entreprise=self.entreprise,
            entrepot=self.entrepot
        )

        # Créer des utilisateurs
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="testpass123",
            nom="Admin",
            prenom="Test",
            role=User.Role.ADMIN
        )

        self.vendeur_user = User.objects.create_user(
            username="vendeur",
            email="<EMAIL>",
            password="testpass123",
            nom="Vendeur",
            prenom="Test",
            role=User.Role.VENDEUR
        )

        self.responsable_user = User.objects.create_user(
            username="responsable",
            email="<EMAIL>",
            password="testpass123",
            nom="Responsable",
            prenom="Test",
            role=User.Role.RESPONSABLE_MAGASIN
        )

        # Créer un vendeur
        self.vendeur = Vendeur.objects.create(
            user=self.vendeur_user,
            code_vendeur="VEND-123456",
            magasin=self.magasin,
            date_embauche=datetime.now().date(),
            taux_commission=0.1
        )

        # Créer un responsable de magasin
        self.responsable = Responsable_magasin.objects.create(
            user=self.responsable_user,
            code_responsable="RESP-123456",
            date_embauche=datetime.now().date()
        )
        self.responsable.magasins_geres.add(self.magasin)

        # Créer une catégorie
        self.categorie = Categorie.objects.create(
            nom="Catégorie Test",
            description="Description test"
        )

        # Créer un produit
        self.produit = Produit.objects.create(
            nom="Produit Test",
            reference="PROD-001",
            categorie=self.categorie,
            magasin=self.magasin,
            prix=100.00,
            prix_achat=80.00,
            prix_vente=120.00,
            unite_mesure="PIECE",
            entreprise=self.entreprise
        )

        # Créer une location
        self.location = Location.objects.create(
            nom="Location Test",
            type_stock="FINIS",
            entrepot=self.entrepot
        )

        # Créer un stock
        self.stock = Stock.objects.create(
            quantite_disponible=100,
            seuil_alerte=10,
            produit=self.produit,
            location=self.location,
            magasin=self.magasin,
            entrepot=self.entrepot
        )

        # Créer un client
        self.client_obj = Client.objects.create(
            nom="Client",
            prenom="Test",
            telephone="+261111111111",
            adresse="Client Address",
            magasin=self.magasin
        )

        # Client API
        self.client = APIClient()


class NumeroFactureTestCase(VenteTestCase):
    """Tests pour la génération de numéro de facture"""

    def test_generation_numero_facture(self):
        """Test de génération automatique du numéro de facture"""
        vente = Vente.objects.create(
            vendeur=self.vendeur,
            magasin=self.magasin,
            mode_paiement='ESPECES',
            entreprise=self.entreprise
        )

        # Vérifier que le numéro de facture a été généré
        self.assertIsNotNone(vente.numero_facture)
        self.assertTrue(vente.numero_facture.startswith('MAG'))
        self.assertIn('2024', vente.numero_facture)  # Année actuelle

    def test_unicite_numero_facture(self):
        """Test de l'unicité du numéro de facture"""
        vente1 = Vente.objects.create(
            vendeur=self.vendeur,
            magasin=self.magasin,
            mode_paiement='ESPECES',
            entreprise=self.entreprise
        )

        vente2 = Vente.objects.create(
            vendeur=self.vendeur,
            magasin=self.magasin,
            mode_paiement='CARTE',
            entreprise=self.entreprise
        )

        # Vérifier que les numéros sont différents
        self.assertNotEqual(vente1.numero_facture, vente2.numero_facture)


class VentesByUserTestCase(VenteTestCase):
    """Tests pour l'endpoint de filtrage des ventes par user ID"""

    def test_vendeur_peut_voir_ses_ventes(self):
        """Test qu'un vendeur peut voir ses propres ventes"""
        # Créer une vente pour le vendeur
        vente = Vente.objects.create(
            vendeur=self.vendeur,
            magasin=self.magasin,
            mode_paiement='ESPECES',
            entreprise=self.entreprise,
            montant_total=100.00
        )

        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Faire la requête
        url = f'/api/ventes/user/{self.vendeur_user.id}/'
        response = self.client.get(url)

        # Vérifications
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['total_ventes'], 1)
        self.assertEqual(len(response.data['ventes']), 1)

    def test_vendeur_ne_peut_pas_voir_ventes_autres(self):
        """Test qu'un vendeur ne peut pas voir les ventes d'autres vendeurs"""
        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Essayer de voir les ventes de l'admin
        url = f'/api/ventes/user/{self.admin_user.id}/'
        response = self.client.get(url)

        # Doit être refusé
        self.assertEqual(response.status_code, 403)


class TableauBordVendeurTestCase(VenteTestCase):
    """Tests pour le tableau de bord vendeur"""

    def test_acces_tableau_bord_vendeur(self):
        """Test d'accès au tableau de bord pour un vendeur"""
        # Créer quelques ventes
        for i in range(3):
            Vente.objects.create(
                vendeur=self.vendeur,
                magasin=self.magasin,
                mode_paiement='ESPECES',
                entreprise=self.entreprise,
                montant_total=100.00 + i * 50
            )

        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Faire la requête
        url = '/api/ventes/vendeur/tableau-bord/'
        response = self.client.get(url)

        # Vérifications
        self.assertEqual(response.status_code, 200)
        self.assertIn('vendeur', response.data)
        self.assertIn('statistiques_periode', response.data)
        self.assertIn('actions_rapides', response.data)

    def test_acces_refuse_non_vendeur(self):
        """Test que seuls les vendeurs peuvent accéder au tableau de bord"""
        # Se connecter en tant qu'admin
        self.client.force_authenticate(user=self.admin_user)

        # Faire la requête
        url = '/api/ventes/vendeur/tableau-bord/'
        response = self.client.get(url)

        # Doit être refusé
        self.assertEqual(response.status_code, 403)


class VenteRapideTestCase(VenteTestCase):
    """Tests pour la création de vente rapide"""

    def test_creation_vente_rapide_vendeur(self):
        """Test de création d'une vente rapide par un vendeur"""
        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Données de la vente
        data = {
            'mode_paiement': 'ESPECES',
            'produits': [
                {
                    'produit': str(self.produit.id_produit),
                    'quantite': 2
                }
            ]
        }

        # Faire la requête
        url = '/api/ventes/vendeur/vente-rapide/'
        response = self.client.post(url, data, format='json')

        # Vérifications
        self.assertEqual(response.status_code, 201)
        self.assertIn('vente', response.data)

        # Vérifier que la vente a été créée avec les bonnes informations
        vente_id = response.data['vente']['id_vente']
        vente = Vente.objects.get(id_vente=vente_id)
        self.assertEqual(vente.vendeur, self.vendeur)
        self.assertEqual(vente.magasin, self.magasin)

    def test_acces_refuse_non_vendeur_vente_rapide(self):
        """Test que seuls les vendeurs peuvent créer des ventes rapides"""
        # Se connecter en tant qu'admin
        self.client.force_authenticate(user=self.admin_user)

        # Données de la vente
        data = {
            'mode_paiement': 'ESPECES',
            'produits': [
                {
                    'produit': str(self.produit.id_produit),
                    'quantite': 2
                }
            ]
        }

        # Faire la requête
        url = '/api/ventes/vendeur/vente-rapide/'
        response = self.client.post(url, data, format='json')

        # Doit être refusé
        self.assertEqual(response.status_code, 403)


class ProduitsStockVendeurTestCase(VenteTestCase):
    """Tests pour la consultation des produits et stock par les vendeurs"""

    def test_consultation_produits_vendeur(self):
        """Test qu'un vendeur peut consulter les produits de son magasin"""
        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Faire la requête
        url = '/api/ventes/vendeur/produits/'
        response = self.client.get(url)

        # Vérifications
        self.assertEqual(response.status_code, 200)
        self.assertIn('produits', response.data)
        self.assertIn('magasin', response.data)
        self.assertEqual(len(response.data['produits']), 1)

        # Vérifier les informations du produit
        produit_data = response.data['produits'][0]
        self.assertEqual(produit_data['nom'], self.produit.nom)
        self.assertIn('stock', produit_data)

    def test_acces_refuse_non_vendeur_produits(self):
        """Test que seuls les vendeurs peuvent consulter cette ressource"""
        # Se connecter en tant qu'admin
        self.client.force_authenticate(user=self.admin_user)

        # Faire la requête
        url = '/api/ventes/vendeur/produits/'
        response = self.client.get(url)

        # Doit être refusé
        self.assertEqual(response.status_code, 403)


class InfosPreRemplissageTestCase(VenteTestCase):
    """Tests pour l'endpoint d'informations de pré-remplissage"""

    def test_infos_preremplissage_vendeur(self):
        """Test des informations de pré-remplissage pour un vendeur"""
        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Faire la requête
        url = '/api/ventes/infos-preremplissage/'
        response = self.client.get(url)

        # Vérifications
        self.assertEqual(response.status_code, 200)
        self.assertIn('auto_remplissage', response.data)
        self.assertIn('options_disponibles', response.data)

        # Vérifier l'auto-remplissage
        auto_remplissage = response.data['auto_remplissage']
        self.assertEqual(auto_remplissage['vendeur_id'], self.vendeur.id)
        self.assertEqual(auto_remplissage['magasin_id'], self.magasin.id)

    def test_infos_preremplissage_admin(self):
        """Test des informations de pré-remplissage pour un admin"""
        # Se connecter en tant qu'admin
        self.client.force_authenticate(user=self.admin_user)

        # Faire la requête
        url = '/api/ventes/infos-preremplissage/'
        response = self.client.get(url)

        # Vérifications
        self.assertEqual(response.status_code, 200)
        self.assertIn('options_disponibles', response.data)

        # Admin doit avoir accès à tous les magasins
        options = response.data['options_disponibles']
        self.assertIn('magasins', options)


class AutoRemplissageVenteTestCase(VenteTestCase):
    """Tests pour l'auto-remplissage dans la création de vente"""

    def test_auto_remplissage_vendeur(self):
        """Test de l'auto-remplissage pour un vendeur"""
        # Se connecter en tant que vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Données sans spécifier vendeur ni magasin
        data = {
            'mode_paiement': 'ESPECES',
            'produits': [
                {
                    'produit': str(self.produit.id_produit),
                    'quantite': 1
                }
            ]
        }

        # Faire la requête
        url = '/api/ventes/ventes/'
        response = self.client.post(url, data, format='json')

        # Vérifications
        self.assertEqual(response.status_code, 201)

        # Vérifier que le vendeur et le magasin ont été auto-remplis
        vente_id = response.data['id_vente']
        vente = Vente.objects.get(id_vente=vente_id)
        self.assertEqual(vente.vendeur, self.vendeur)
        self.assertEqual(vente.magasin, self.magasin)

    def test_vendeur_ne_peut_pas_modifier_vendeur(self):
        """Test qu'un vendeur ne peut pas spécifier un autre vendeur"""
        # Créer un autre vendeur
        autre_vendeur_user = User.objects.create_user(
            username="autre_vendeur",
            email="<EMAIL>",
            password="testpass123",
            nom="Autre",
            prenom="Vendeur",
            role=User.Role.VENDEUR
        )
        autre_vendeur = Vendeur.objects.create(
            user=autre_vendeur_user,
            code_vendeur="VEND-789012",
            magasin=self.magasin,
            date_embauche=datetime.now().date()
        )

        # Se connecter en tant que premier vendeur
        self.client.force_authenticate(user=self.vendeur_user)

        # Essayer de créer une vente pour l'autre vendeur
        data = {
            'vendeur': autre_vendeur.id,
            'mode_paiement': 'ESPECES',
            'produits': [
                {
                    'produit': str(self.produit.id_produit),
                    'quantite': 1
                }
            ]
        }

        # Faire la requête
        url = '/api/ventes/ventes/'
        response = self.client.post(url, data, format='json')

        # Doit être refusé
        self.assertEqual(response.status_code, 403)
