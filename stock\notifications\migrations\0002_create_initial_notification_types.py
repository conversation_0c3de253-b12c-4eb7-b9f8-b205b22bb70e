from django.db import migrations

def create_notification_types(apps, schema_editor):
    NotificationType = apps.get_model('notifications', 'NotificationType')
    NotificationType.objects.get_or_create(nom='BIENVENUE', actif=True)
    NotificationType.objects.get_or_create(nom='ALERTE_STOCK_BAS', actif=True)
    NotificationType.objects.get_or_create(nom='STOCK_EPUISE', actif=True)
    NotificationType.objects.get_or_create(nom='NOUVELLE_VENTE', actif=True)
    # Ajoute ici d'autres types de notifications nécessaires à ton projet

class Migration(migrations.Migration):

    dependencies = [
        ('notifications', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_notification_types),
    ] 