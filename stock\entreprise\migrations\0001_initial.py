# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Entreprise',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.Char<PERSON>ield(max_length=100)),
                ('adresse', models.TextField()),
                ('nif', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^[0-9]{13}$', 'Le NIF doit contenir 13 chiffres')])),
                ('date_creation', models.DateField()),
                ('statut', models.Bo<PERSON>an<PERSON>ield(default=True)),
                ('administrateur', models.ForeignKey(blank=True, limit_choices_to={'role': 'ADMIN'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='entreprises_administrees', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
