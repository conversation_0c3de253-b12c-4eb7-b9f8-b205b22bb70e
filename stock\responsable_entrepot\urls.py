from django.urls import path
from .views import (
    ResponsableEntrepotListCreateView,
    ResponsableEntrepotDetailView,
    ResponsableEntrepotPerformanceView,
    ResponsableEntrepotRapportView,
    ResponsableEntrepotByUserView,
    ResponsableEntrepotStatsView
)

urlpatterns = [
    # CRUD de base
    path('', ResponsableEntrepotListCreateView.as_view(), name='responsable-entrepot-list-create'),
    path('<int:id>/', ResponsableEntrepotDetailView.as_view(), name='responsable-entrepot-detail'),

    # Fonctionnalités spécialisées
    path('<int:id>/performance/', ResponsableEntrepotPerformanceView.as_view(), name='responsable-entrepot-performance'),
    path('<int:id>/rapport/', ResponsableEntrepotRapportView.as_view(), name='responsable-entrepot-rapport'),

    # Vues utilitaires
    path('mon-entrepot/', ResponsableEntrepotByUserView.as_view(), name='mon-entrepot'),
    path('stats/', ResponsableEntrepotStatsView.as_view(), name='responsables-entrepot-stats'),
]
