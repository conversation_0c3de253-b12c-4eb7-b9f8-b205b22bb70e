from django.urls import path
from .views import (
    InventaireListCreateView, InventaireDetailView, InventaireTerminerView,
    DetailInventaireCreateView, DetailInventaireUpdateView, InventaireRapportView
)

urlpatterns = [
    path('inventaires/', InventaireListCreateView.as_view(), name='inventaire-list-create'),
    path('inventaires/<uuid:id_inventaire>/', InventaireDetailView.as_view(), name='inventaire-detail'),
    path('inventaires/<uuid:id_inventaire>/terminer/', InventaireTerminerView.as_view(), name='inventaire-terminer'),
    path('inventaires/<uuid:id_inventaire>/details/', DetailInventaireCreateView.as_view(), name='detail-inventaire-create'),
    path('details/<uuid:id_detail>/', DetailInventaireUpdateView.as_view(), name='detail-inventaire-update'),
    path('inventaires/<uuid:id_inventaire>/rapport/', InventaireRapportView.as_view(), name='inventaire-rapport'),
]