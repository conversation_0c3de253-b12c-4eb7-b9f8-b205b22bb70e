# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('magasins', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Responsable_magasin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_responsable_magasin', models.CharField(max_length=20, unique=True, validators=[django.core.validators.RegexValidator('^RESP-[0-9]{6}$', 'Le code doit être au format RESP-XXXXXX')])),
                ('date_embauche', models.DateField()),
                ('notes', models.TextField(blank=True)),
                ('magasin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responsables', to='magasins.magasin')),
                ('user', models.OneToOneField(limit_choices_to={'role': 'RESPONSABLE_MAGASIN'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Responsable de magasin',
                'verbose_name_plural': 'Responsables de magasin',
                'indexes': [models.Index(fields=['code_responsable_magasin'], name='responsable_code_re_7607b4_idx'), models.Index(fields=['magasin'], name='responsable_magasin_e1d2b5_idx')],
            },
        ),
    ]
