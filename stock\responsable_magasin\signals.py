from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Responsable_magasin
from magasins.models import Magasin
from personalisation_user.models import UserProfile
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Responsable_magasin)
def validate_responsable(sender, instance, **kwargs):
    """
    Valide le responsable de magasin avant la sauvegarde
    """
    if not instance.user:
        raise ValueError("Un utilisateur doit être associé au responsable")
    if not instance.magasin:
        raise ValueError("Un magasin doit être associé au responsable")
    
    # Vérifier que l'utilisateur a le bon rôle
    if instance.user.role != 'RESPONSABLE_MAGASIN':
        raise ValueError("L'utilisateur doit avoir le rôle de responsable de magasin")

@receiver(post_save, sender=Responsable_magasin)
def update_user_profile(sender, instance, created, **kwargs):
    """
    Met à jour le profil utilisateur lors de la création/modification d'un responsable
    """
    try:
        with transaction.atomic():
            # Mettre à jour ou créer le profil utilisateur
            profile, created = UserProfile.objects.get_or_create(
                user=instance.user,
                defaults={
                    'entreprise': instance.magasin.entreprise,
                    'role': 'RESPONSABLE_MAGASIN',
                    'telephone': instance.user.telephone,
                    'adresse': instance.user.adresse
                }
            )
            
            if not created:
                profile.entreprise = instance.magasin.entreprise
                profile.role = 'RESPONSABLE_MAGASIN'
                profile.telephone = instance.user.telephone
                profile.adresse = instance.user.adresse
                profile.save()
            
            logger.info(f"Profil utilisateur mis à jour pour le responsable {instance.user.get_full_name()}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du profil utilisateur: {str(e)}")

@receiver(post_delete, sender=Responsable_magasin)
def handle_responsable_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'un responsable de magasin
    """
    try:
        with transaction.atomic():
            # Mettre à jour les magasins gérés par ce responsable
            magasins = Magasin.objects.filter(responsable_magasin=instance)
            for magasin in magasins:
                magasin.responsable_magasin = None
                magasin.save(update_fields=['responsable_magasin'])
                logger.info(f"Responsable retiré du magasin {magasin.nom}")
            
            # Mettre à jour le profil utilisateur
            try:
                profile = UserProfile.objects.get(user=instance.user)
                profile.role = 'USER'  # Rétrograder le rôle
                profile.save()
                logger.info(f"Rôle utilisateur mis à jour pour {instance.user.get_full_name()}")
            except UserProfile.DoesNotExist:
                pass
            
            logger.info(f"Responsable {instance.user.get_full_name()} supprimé avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression du responsable: {str(e)}")

@receiver(post_save, sender=Responsable_magasin)
def update_magasin_responsable(sender, instance, **kwargs):
    """
    Met à jour les magasins associés au responsable
    """
    try:
        with transaction.atomic():
            # Mettre à jour les magasins existants
            magasins = Magasin.objects.filter(responsable_magasin=instance)
            for magasin in magasins:
                if magasin.id != instance.magasin.id:
                    magasin.responsable_magasin = None
                    magasin.save(update_fields=['responsable_magasin'])
                    logger.warning(f"Responsable retiré du magasin {magasin.nom}")
            
            logger.info(f"Magasins mis à jour pour le responsable {instance.user.get_full_name()}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des magasins: {str(e)}") 