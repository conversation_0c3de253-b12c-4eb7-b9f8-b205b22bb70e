from django.core.management.base import BaseCommand
from django.conf import settings
import json
import time
from performance.load_testing import Load<PERSON>est<PERSON><PERSON><PERSON>, APILoadTests


class Command(BaseCommand):
    help = 'Exécute des tests de charge sur l\'application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--base-url',
            type=str,
            default='http://localhost:8000',
            help='URL de base pour les tests (défaut: http://localhost:8000)'
        )
        parser.add_argument(
            '--concurrent-users',
            type=int,
            default=10,
            help='Nombre d\'utilisateurs concurrent (défaut: 10)'
        )
        parser.add_argument(
            '--requests-per-user',
            type=int,
            default=10,
            help='Nombre de requêtes par utilisateur (défaut: 10)'
        )
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['all', 'api', 'database'],
            default='all',
            help='Type de tests à exécuter'
        )
        parser.add_argument(
            '--output-file',
            type=str,
            help='Fichier de sortie pour les résultats JSON'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Affichage détaillé'
        )

    def handle(self, *args, **options):
        base_url = options['base_url']
        concurrent_users = options['concurrent_users']
        requests_per_user = options['requests_per_user']
        test_type = options['test_type']
        output_file = options['output_file']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS('🚀 Démarrage des tests de charge')
        )
        self.stdout.write(f'🌐 URL: {base_url}')
        self.stdout.write(f'👥 Utilisateurs concurrents: {concurrent_users}')
        self.stdout.write(f'📊 Requêtes par utilisateur: {requests_per_user}')
        self.stdout.write(f'🎯 Type de test: {test_type}')
        
        start_time = time.time()
        results = {}
        
        try:
            if test_type in ['all', 'api']:
                self.stdout.write(self.style.HTTP_INFO('\n📡 Tests API'))
                api_results = self._run_api_tests(
                    base_url, concurrent_users, requests_per_user, verbose
                )
                results['api'] = api_results
            
            if test_type in ['all', 'database']:
                self.stdout.write(self.style.HTTP_INFO('\n🗄️  Tests Base de Données'))
                db_results = self._run_database_tests(verbose)
                results['database'] = db_results
            
            # Générer le rapport final
            end_time = time.time()
            total_duration = end_time - start_time
            
            final_report = {
                'timestamp': time.time(),
                'duration': total_duration,
                'configuration': {
                    'base_url': base_url,
                    'concurrent_users': concurrent_users,
                    'requests_per_user': requests_per_user,
                    'test_type': test_type
                },
                'results': results
            }
            
            # Afficher le résumé
            self._display_summary(final_report)
            
            # Sauvegarder si demandé
            if output_file:
                self._save_results(final_report, output_file)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erreur lors des tests: {e}')
            )
    
    def _run_api_tests(self, base_url, concurrent_users, requests_per_user, verbose):
        """Exécute les tests API"""
        api_tests = APILoadTests(base_url)
        results = {}
        
        # Authentification
        self.stdout.write('🔐 Authentification...')
        if not api_tests.setup_authentication():
            self.stdout.write(
                self.style.WARNING('⚠️  Échec authentification - Tests API limités')
            )
            return {'error': 'Authentication failed'}
        
        # Tests individuels
        tests = [
            ('product_list', 'Liste des produits', api_tests.test_product_list_api),
            ('stock_alerts', 'Alertes de stock', api_tests.test_stock_alerts_api),
            ('notifications', 'Notifications', api_tests.test_notifications_api),
        ]
        
        for test_key, test_name, test_func in tests:
            self.stdout.write(f'🧪 Test: {test_name}')
            
            try:
                result = test_func(concurrent_users, requests_per_user)
                stats = result.get_statistics()
                results[test_key] = stats
                
                # Affichage des résultats
                if verbose:
                    self._display_test_result(test_name, stats)
                else:
                    success_rate = stats.get('success_rate', 0)
                    avg_time = stats.get('response_times', {}).get('mean', 0)
                    rps = stats.get('requests_per_second', 0)
                    
                    status_style = self.style.SUCCESS if success_rate > 95 else self.style.WARNING
                    self.stdout.write(
                        status_style(f'   ✓ Succès: {success_rate:.1f}% | Temps: {avg_time:.3f}s | RPS: {rps:.1f}')
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ Erreur: {e}')
                )
                results[test_key] = {'error': str(e)}
        
        return results
    
    def _run_database_tests(self, verbose):
        """Exécute les tests de base de données"""
        from django.test.utils import setup_test_environment, teardown_test_environment
        from django.test.runner import DiscoverRunner
        
        self.stdout.write('🗄️  Tests de base de données...')
        
        try:
            # Configuration de l'environnement de test
            setup_test_environment()
            
            # Simuler des tests de charge DB
            results = {
                'concurrent_operations': self._test_concurrent_operations(),
                'query_performance': self._test_query_performance(),
            }
            
            teardown_test_environment()
            return results
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Erreur tests DB: {e}')
            )
            return {'error': str(e)}
    
    def _test_concurrent_operations(self):
        """Test des opérations concurrentes"""
        from django.db import transaction
        import threading
        import time
        
        self.stdout.write('   🔄 Test opérations concurrentes...')
        
        results = []
        
        def db_operation():
            start_time = time.time()
            try:
                with transaction.atomic():
                    # Simuler une opération DB
                    from base.models import User
                    User.objects.count()
                    time.sleep(0.01)  # Simuler du travail
                
                end_time = time.time()
                results.append(end_time - start_time)
            except Exception as e:
                results.append(None)
        
        # Lancer 10 opérations concurrentes
        threads = []
        for i in range(10):
            thread = threading.Thread(target=db_operation)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        valid_results = [r for r in results if r is not None]
        
        if valid_results:
            return {
                'total_operations': len(results),
                'successful_operations': len(valid_results),
                'average_time': sum(valid_results) / len(valid_results),
                'max_time': max(valid_results),
                'min_time': min(valid_results)
            }
        else:
            return {'error': 'No successful operations'}
    
    def _test_query_performance(self):
        """Test des performances de requêtes"""
        from django.db import connection
        import time
        
        self.stdout.write('   📊 Test performance requêtes...')
        
        queries = [
            "SELECT COUNT(*) FROM base_user;",
            "SELECT COUNT(*) FROM produit_produit;",
            "SELECT COUNT(*) FROM stock_pme_stock;",
        ]
        
        results = []
        
        for query in queries:
            start_time = time.time()
            try:
                with connection.cursor() as cursor:
                    cursor.execute(query)
                    cursor.fetchall()
                
                end_time = time.time()
                results.append({
                    'query': query,
                    'execution_time': end_time - start_time,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'query': query,
                    'error': str(e),
                    'success': False
                })
        
        return {
            'queries_tested': len(queries),
            'results': results,
            'average_time': sum(r.get('execution_time', 0) for r in results if r.get('success')) / len([r for r in results if r.get('success')])
        }
    
    def _display_test_result(self, test_name, stats):
        """Affiche les résultats détaillés d'un test"""
        self.stdout.write(f'   📊 {test_name}:')
        self.stdout.write(f'      • Requêtes totales: {stats.get("total_requests", 0)}')
        self.stdout.write(f'      • Taux de succès: {stats.get("success_rate", 0):.1f}%')
        
        response_times = stats.get('response_times', {})
        self.stdout.write(f'      • Temps de réponse:')
        self.stdout.write(f'        - Moyen: {response_times.get("mean", 0):.3f}s')
        self.stdout.write(f'        - Médian: {response_times.get("median", 0):.3f}s')
        self.stdout.write(f'        - P95: {response_times.get("p95", 0):.3f}s')
        self.stdout.write(f'        - Max: {response_times.get("max", 0):.3f}s')
        
        self.stdout.write(f'      • Requêtes/seconde: {stats.get("requests_per_second", 0):.1f}')
        
        if stats.get('errors'):
            self.stdout.write(f'      • Erreurs: {len(stats["errors"])}')
    
    def _display_summary(self, report):
        """Affiche le résumé final"""
        self.stdout.write(self.style.SUCCESS('\n🎉 Tests terminés!'))
        self.stdout.write(f'⏱️  Durée totale: {report["duration"]:.1f}s')
        
        # Résumé API
        if 'api' in report['results']:
            api_results = report['results']['api']
            if 'error' not in api_results:
                total_requests = sum(
                    test.get('total_requests', 0) 
                    for test in api_results.values() 
                    if isinstance(test, dict)
                )
                avg_success_rate = sum(
                    test.get('success_rate', 0) 
                    for test in api_results.values() 
                    if isinstance(test, dict) and 'success_rate' in test
                ) / len([t for t in api_results.values() if isinstance(t, dict) and 'success_rate' in t])
                
                self.stdout.write(f'📡 API: {total_requests} requêtes, {avg_success_rate:.1f}% succès')
        
        # Résumé DB
        if 'database' in report['results']:
            db_results = report['results']['database']
            if 'error' not in db_results:
                self.stdout.write('🗄️  Base de données: Tests réussis')
    
    def _save_results(self, report, filename):
        """Sauvegarde les résultats"""
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2)
            self.stdout.write(
                self.style.SUCCESS(f'💾 Résultats sauvés dans: {filename}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erreur sauvegarde: {e}')
            )
