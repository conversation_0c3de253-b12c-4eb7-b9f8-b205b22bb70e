import uuid
from django.db import models
from django.core.validators import RegexValidator
from base.models import User
from entrepot.models import Entrepot
from achat_magasin.models import AchatMagasin


class ResponsableEntrepot(models.Model):
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'role': User.Role.RESPONSABLE_ENTREPOT}
    )
    entrepot = models.ForeignKey(Entrepot, on_delete=models.CASCADE, related_name='responsables')
    code_responsable = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^RESP-ENT-[0-9]{6}$', 'Le code doit être au format RESP-ENT-XXXXXX')]
    )
    date_embauche = models.DateField()
    notes = models.TextField(blank=True)
    actif = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Responsable Entrepôt"
        verbose_name_plural = "Responsables Entrepôt"
        unique_together = ['user', 'entrepot']

    def __str__(self):
        return f"{self.user.prenom} {self.user.nom} - {self.entrepot.nom} ({self.code_responsable})"

    def get_performance(self):
        """Calcule les performances du responsable d'entrepôt"""
        from stock_Pme.models import Stock
        from achats.models import Achat
        from django.db.models import Sum, Count
        from datetime import datetime, timedelta

        # Statistiques des stocks gérés
        stocks_geres = Stock.objects.filter(entrepot=self.entrepot)
        total_produits = stocks_geres.count()
        
        # Stocks en alerte
        stocks_alerte = stocks_geres.filter(quantite__lte=models.F('seuil_alerte')).count()
        
        # Mouvements de stock du mois dernier
        date_debut = datetime.now() - timedelta(days=30)
        achats_recents = Achat.objects.filter(
            entrepot=self.entrepot,
            date_achat__gte=date_debut
        ).aggregate(
            total_achats=Count('id'),
            valeur_totale=Sum('montant_total')
        )

        return {
            'total_produits_geres': total_produits,
            'stocks_en_alerte': stocks_alerte,
            'taux_alerte': round((stocks_alerte / total_produits * 100) if total_produits > 0 else 0, 2),
            'achats_mois': achats_recents['total_achats'] or 0,
            'valeur_achats_mois': float(achats_recents['valeur_totale'] or 0),
            'capacite_entrepot': self.entrepot.capacite_stockage,
            'entrepot_actif': self.entrepot.statut
        }

    def generer_rapport_entrepot(self):
        """Génère un rapport détaillé de l'entrepôt géré"""
        return self.entrepot.generer_rapport_stock()

    def save(self, *args, **kwargs):
        # Générer automatiquement le code responsable si pas fourni
        if not self.code_responsable:
            import random
            self.code_responsable = f"RESP-ENT-{random.randint(100000, 999999)}"
        super().save(*args, **kwargs)
