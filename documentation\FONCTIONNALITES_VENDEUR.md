# 🛍️ Fonctionnalités Vendeur - Documentation API

## 📋 Vue d'ensemble

Ce document présente toutes les nouvelles fonctionnalités implémentées pour les vendeurs dans le système de gestion des ventes.

## 🎯 Fonctionnalités Implémentées

### ✅ 1. Numéro de facture automatique par magasin
- **Format** : `{3_lettres_magasin}-{numéro_séquentiel:04d}-{année}`
- **Exemple** : `MAG-0001-2024`, `STO-0002-2024`
- **Génération** : Automatique lors de la création de vente
- **Unicité** : Par magasin et par année

### ✅ 2. Filtrage des ventes par utilisateur
- **Endpoint** : `GET /api/ventes/user/{userid}/`
- **Sécurité** : Contrôles d'accès par rôle
- **Réponse** : Ventes + statistiques utilisateur

### ✅ 3. Gestion des ventes pour vendeurs
- **Annulation** : `DELETE /api/ventes/ventes/{id}/` (avec restauration stock)
- **Produits & Stock** : `GET /api/ventes/vendeur/produits/`
- **Vente rapide** : `POST /api/ventes/vendeur/vente-rapide/`

### ✅ 4. Tableau de bord vendeur
- **Endpoint** : `GET /api/ventes/vendeur/tableau-bord/`
- **Statistiques** : CA, nombre de ventes, panier moyen
- **Périodes** : jour, semaine, mois, année
- **Actions rapides** : Liens vers fonctionnalités principales

### ✅ 5. Interface optimisée avec auto-remplissage
- **Auto-détection** : Rôle utilisateur automatiquement détecté
- **Pré-remplissage** : `GET /api/ventes/infos-preremplissage/`
- **Validation** : Permissions strictes par rôle

---

## 🔗 Endpoints API

### 📊 Tableau de bord vendeur
```http
GET /api/ventes/vendeur/tableau-bord/?periode=mois
Authorization: Bearer {token}
```

**Réponse :**
```json
{
  "vendeur": {
    "nom": "Jean Dupont",
    "code_vendeur": "VEND-123456",
    "magasin": "Magasin Central"
  },
  "statistiques_periode": {
    "total_ventes": 15,
    "chiffre_affaires": 12500.00,
    "panier_moyen": 833.33
  },
  "actions_rapides": [
    {
      "nom": "Nouvelle vente",
      "endpoint": "/api/ventes/vendeur/vente-rapide/",
      "methode": "POST"
    }
  ]
}
```

### 🛒 Vente rapide
```http
POST /api/ventes/vendeur/vente-rapide/
Authorization: Bearer {token}
Content-Type: application/json

{
  "mode_paiement": "ESPECES",
  "client": "uuid-client-optionnel",
  "produits": [
    {
      "produit": "uuid-produit",
      "quantite": 2
    }
  ]
}
```

### 📦 Consultation produits & stock
```http
GET /api/ventes/vendeur/produits/?search=produit&categorie=electronique
Authorization: Bearer {token}
```

**Réponse :**
```json
{
  "magasin": "Magasin Central",
  "total_produits": 25,
  "produits": [
    {
      "id_produit": "uuid",
      "nom": "Smartphone XYZ",
      "prix_vente": 299.99,
      "stock": {
        "quantite_disponible": 15,
        "seuil_alerte": 5,
        "en_alerte": false
      }
    }
  ]
}
```

### 👤 Ventes par utilisateur
```http
GET /api/ventes/user/{userid}/
Authorization: Bearer {token}
```

### ℹ️ Informations de pré-remplissage
```http
GET /api/ventes/infos-preremplissage/
Authorization: Bearer {token}
```

**Réponse pour vendeur :**
```json
{
  "auto_remplissage": {
    "vendeur_id": 123,
    "magasin_id": 456,
    "vendeur_nom": "Jean Dupont",
    "magasin_nom": "Magasin Central"
  },
  "options_disponibles": {
    "produits": [...],
    "clients": [...],
    "modes_paiement": [...]
  }
}
```

---

## 🔒 Sécurité et Permissions

### Vendeurs
- ✅ Peuvent voir uniquement leurs propres ventes
- ✅ Peuvent créer des ventes uniquement pour leur magasin
- ✅ Auto-remplissage automatique de leur ID et magasin
- ✅ Ne peuvent pas modifier les ventes d'autres vendeurs

### Responsables de magasin
- ✅ Peuvent voir les ventes de leurs magasins
- ✅ Peuvent annuler les ventes de leurs magasins
- ✅ Accès aux vendeurs de leurs magasins

### Administrateurs
- ✅ Accès complet à toutes les fonctionnalités
- ✅ Peuvent voir toutes les ventes
- ✅ Peuvent gérer tous les magasins

---

## 🧪 Tests

Les tests couvrent :
- ✅ Génération et unicité des numéros de facture
- ✅ Contrôles d'accès par rôle
- ✅ Auto-remplissage intelligent
- ✅ Fonctionnalités du tableau de bord
- ✅ Création de ventes rapides
- ✅ Consultation des produits et stock

**Exécuter les tests :**
```bash
python manage.py test ventes.tests
```

---

## 📈 Améliorations Futures

### Suggestions d'évolution :
1. **Notifications en temps réel** pour les alertes de stock
2. **Objectifs personnalisés** par vendeur
3. **Rapports détaillés** avec graphiques
4. **Mode hors-ligne** pour les ventes
5. **Intégration scanner** de codes-barres
6. **Historique des modifications** de ventes

---

## 🚀 Utilisation Recommandée

### Workflow vendeur optimal :
1. **Connexion** → Auto-détection du rôle
2. **Tableau de bord** → Vue d'ensemble des performances
3. **Vente rapide** → Interface simplifiée pour ventes fréquentes
4. **Consultation stock** → Vérification disponibilité avant vente
5. **Suivi ventes** → Historique personnel via filtrage

### Bonnes pratiques :
- Utiliser la vente rapide pour les transactions courantes
- Consulter le stock avant chaque vente importante
- Vérifier le tableau de bord régulièrement
- Utiliser les filtres de recherche pour les produits
