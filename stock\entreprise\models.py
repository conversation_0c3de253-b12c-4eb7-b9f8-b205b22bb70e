import uuid
from django.core.validators import RegexValidator
from django.db import models
from base.models import User

class Entreprise(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100)
    adresse = models.TextField()
    nif = models.CharField(
        max_length=20,
        unique=True,
        validators=[RegexValidator(r'^[0-9]{13}$', 'Le NIF doit contenir 13 chiffres')]
    )
    date_creation = models.DateField()
    statut = models.BooleanField(default=True)
    administrateur = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='entreprises_administrees',
        limit_choices_to={'role': User.Role.ADMIN}
    )
    
    def __str__(self):
        return f"{self.nom} (NIF: {self.nif})" 
    
    def verifier_validite_nif(self):
         """Vérifie la validité du NIF selon l'algorithme standard"""
         # Implémentez ici la logique de validation réelle
         return len(self.nif) == 13 and self.nif.isdigit()
    
    @classmethod
    def generer_rapport_entreprises(cls):
        from django.db.models import Count
        return {
            'total' : cls.objects.count(),
            'actives' : cls.objects.filter(statut=True).count(),
            'par_annee':cls.objects.values('date_creation__year').annotate(total=Count('id'))
        }    
         
        


