from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Inventaire, DetailInventaire
from stock_Pme.models import Stock, MouvementStock
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Inventaire)
def update_stock_after_inventaire(sender, instance, created, **kwargs):
    """
    Met à jour le stock après la finalisation d'un inventaire
    """
    try:
        with transaction.atomic():
            if not created and instance.statut:  # Si l'inventaire vient d'être terminé
                for detail in instance.details.all():
                    stock = Stock.objects.get(
                        produit=detail.produit,
                        entrepot=instance.entrepot,
                        magasin=instance.magasin
                    )
                    
                    # Calculer la différence
                    difference = detail.quantite_reelle - detail.quantite_theorique
                    
                    if difference != 0:
                        # Mettre à jour le stock
                        stock.quantite_disponible = detail.quantite_reelle
                        stock.save()
                        
                        # Créer un mouvement de stock pour l'ajustement
                        MouvementStock.objects.create(
                            produit=detail.produit,
                            location=stock.location,
                            quantite=abs(difference),
                            type_mouvement='AJUSTEMENT',
                            utilisateur=instance.responsable.username if instance.responsable else 'SYSTEM',
                            notes=f'Ajustement suite à l\'inventaire {instance.id_inventaire}'
                        )

    except Stock.DoesNotExist:
        logger.error(f"Stock non trouvé pour le produit {detail.produit.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}")

@receiver(pre_save, sender=Inventaire)
def validate_inventaire(sender, instance, **kwargs):
    """
    Valide l'inventaire avant la sauvegarde
    """
    if instance.entrepot != instance.magasin.entrepot:
        raise ValueError("L'entrepôt doit correspondre à celui du magasin")
    
    if instance.responsable and instance.responsable.role not in ['ADMIN', 'RESPONSABLE']:
        raise ValueError("Le responsable doit être un ADMIN ou RESPONSABLE")

@receiver(post_save, sender=DetailInventaire)
def validate_detail_inventaire(sender, instance, created, **kwargs):
    """
    Valide le détail d'inventaire après la sauvegarde
    """
    try:
        if instance.inventaire.statut:
            raise ValueError("Impossible de modifier un inventaire terminé")
        
        if instance.quantite_reelle < 0:
            raise ValueError("La quantité réelle ne peut pas être négative")
            
    except Exception as e:
        logger.error(f"Erreur lors de la validation du détail d'inventaire: {str(e)}")
        raise 