from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from entreprise.models import Entreprise
from personalisation_user.models import UserProfile

class Command(BaseCommand):
    help = 'Crée le super admin et l\'entreprise par défaut'

    def handle(self, *args, **kwargs):
        # Créer l'entreprise par défaut
        entreprise, created = Entreprise.objects.get_or_create(
            nom="Entreprise Principale",
            defaults={
                'adresse': "Adresse par défaut",
                'telephone': "0000000000",
                'email': "<EMAIL>",
                'site_web': "www.entreprise.com",
                'description': "Entreprise principale du système"
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS('Entreprise créée avec succès'))
        else:
            self.stdout.write(self.style.SUCCESS('Entreprise déjà existante'))

        # <PERSON><PERSON><PERSON> le super admin
        super_admin, created = User.objects.get_or_create(
            username='superadmin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
                'is_active': True
            }
        )

        if created:
            super_admin.set_password('superadmin123')  # Changer ce mot de passe en production
            super_admin.save()
            self.stdout.write(self.style.SUCCESS('Super admin créé avec succès'))
        else:
            self.stdout.write(self.style.SUCCESS('Super admin déjà existant'))

        # Donner tous les droits au super admin
        all_permissions = Permission.objects.all()
        super_admin.user_permissions.set(all_permissions)
        self.stdout.write(self.style.SUCCESS('Tous les droits ont été attribués au super admin'))

        # Créer le profil du super admin
        profile, created = UserProfile.objects.get_or_create(
            user=super_admin,
            defaults={
                'entreprise': entreprise,
                'role': 'SUPER_ADMIN',
                'telephone': '0000000000',
                'adresse': 'Adresse du super admin'
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS('Profil super admin créé avec succès'))
        else:
            self.stdout.write(self.style.SUCCESS('Profil super admin déjà existant')) 