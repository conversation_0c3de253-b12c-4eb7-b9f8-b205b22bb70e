# Documentation de l’app `statistiques`

## Introduction

L’application `statistiques` centralise la collecte, l’agrégation et l’analyse des données de ventes, de stock, de clients, de facturation et de performance pour l’entreprise, les magasins et les vendeurs. Elle expose des endpoints d’API pour la consultation, la génération et l’analyse des statistiques, et automatise la mise à jour des indicateurs via des signaux.

---

## Modèles principaux

### 1. `Statistique`
- **But** : Stocke les indicateurs consolidés (ventes, stock, clients, facturation, performance) pour une période donnée (jour, semaine, mois, année), par entreprise, magasin ou entrepôt.
- **Champs clés** :
  - Ventes : `total_ventes`, `chiffre_affaire`, `montant_ht`, `montant_tva`, `produits_populaires`
  - Financier : `marge_brute`, `taux_marge`, `cout_achat`, `benefice_net`
  - Stock : `total_produits`, `valeur_stock`, `taux_rotation`, `produits_en_alerte`
  - Clients : `nouveaux_clients`, `clients_actifs`, `panier_moyen`, `taux_fidelite`
  - Facturation : `factures_emises`, `factures_payees`, `montant_impaye`, `delai_paiement_moyen`
  - Performance : `temps_traitement_moyen`, `taux_retour`, `satisfaction_client`
  - Métadonnées : `date_statistique`, `periode`, `created_at`
- **Méthode clé** :  
  - `calculer_statistiques_completes` : calcule et crée une statistique consolidée pour une période, un magasin ou un entrepôt.

### 2. `StatistiqueVendeur`
- **But** : Statistiques individuelles pour chaque vendeur (nombre de ventes, montant total, moyenne).
- **Champs** : `vendeur`, `nombre_ventes`, `montant_total`, `moyenne_vente`, `created_at`, `updated_at`

### 3. `StatistiqueMagasin`
- **But** : Statistiques individuelles pour chaque magasin (nombre de ventes, montant total, moyenne).
- **Champs** : `magasin`, `nombre_ventes`, `montant_total`, `moyenne_vente`, `created_at`, `updated_at`

---

## Serializers

- **StatistiqueSerializer** : Sérialise tous les champs du modèle `Statistique` + détails magasin/entrepôt.
- **GenerateStatistiqueSerializer** : Pour la génération de statistiques (période, magasin_id, entrepot_id).

---

## Endpoints API

Tous les endpoints sont accessibles via `/statistiques/` (router DRF).

| Méthode | URL | Description |
|---------|-----|-------------|
| GET     | `/statistiques/` | Liste toutes les statistiques (filtrage par date, magasin, entrepôt, période possible) |
| POST    | `/statistiques/` | Crée une statistique (voir GenerateStatistiqueSerializer) |
| GET     | `/statistiques/{id}/` | Détail d’une statistique |
| PUT     | `/statistiques/{id}/` | Met à jour une statistique |
| DELETE  | `/statistiques/{id}/` | Supprime une statistique |
| POST    | `/statistiques/generer/` | Génère et calcule une statistique consolidée (admin uniquement) |
| GET     | `/statistiques/rapport_consolide/` | Statistiques globales, par jour, par magasin |
| GET     | `/statistiques/produits_populaires/` | Top produits vendus (quantité, marge, magasins) |
| GET     | `/statistiques/alertes_stock/` | Produits en alerte de stock |
| GET     | `/statistiques/performance/` | Indicateurs de performance (temps, retours, satisfaction) |
| GET     | `/statistiques/tendances/` | Tendances (variation ventes, CA, clients sur 30/60 jours) |
| GET     | `/statistiques/magasin_detail/?magasin_id=...` | Statistiques détaillées d’un magasin (admin entreprise) |

---

## Vues et logique métier

- **StatistiqueViewSet** (DRF ModelViewSet) :
  - Filtrage dynamique selon l’utilisateur (entreprise, magasin), période, date, etc.
  - Actions personnalisées :
    - `generer` : Génère une statistique consolidée pour une période donnée.
    - `rapport_consolide` : Statistiques globales, par jour, par magasin.
    - `produits_populaires` : Top produits vendus.
    - `alertes_stock` : Produits en alerte de stock.
    - `performance` : Indicateurs de performance globaux et par magasin.
    - `tendances` : Variations sur 30/60 jours.
    - `magasin_detail` : Statistiques détaillées d’un magasin (CA, ventes, clients, produits, alertes, performance, tendances).

---

## Signaux

- **Avant sauvegarde Statistique** : Validation (magasin obligatoire, montants/ventes non négatifs).
- **Après vente** : Mise à jour automatique des statistiques magasin/vendeur.
- **Après suppression Statistique** : Réinitialisation des stats du magasin.
- **Après création magasin/vendeur** : Création automatique des stats initiales.

---

## Sécurité & Permissions

- Toutes les vues nécessitent l’authentification (`IsAuthenticated`).
- Certaines actions (génération, détail magasin) nécessitent d’être admin ou entreprise.
- Validation stricte des entrées et cohérence des données via signaux.

---

## Bonnes pratiques

- Utiliser les endpoints `/rapport_consolide/`, `/performance/`, `/tendances/` pour les dashboards.
- Générer régulièrement les statistiques via `/generer/` (cron, tâche planifiée, ou action admin).
- Utiliser les filtres de période, magasin, entrepôt pour des analyses ciblées.

---

## Extension & Personnalisation

- Ajouter de nouveaux indicateurs dans le modèle `Statistique` si besoin.
- Étendre les actions du ViewSet pour des analyses spécifiques.
- Intégrer avec d’autres modules (ventes, stock, clients) pour enrichir les analyses.

---

## Références croisées

- **Ventes** : `ventes.models.Vente`, `ventes.models.DetailVente`
- **Clients** : `clients.models.Client`
- **Factures** : `factures.models.Facture`
- **Stock** : `stock_Pme.models.Stock`
- **Magasin** : `magasins.models.Magasin`
- **Vendeur** : `vendeurs.models.Vendeur`
- **Entrepôt** : `entrepot.models.Entrepot`

---

Pour toute extension, voir le code source ou contacter l’équipe technique. 