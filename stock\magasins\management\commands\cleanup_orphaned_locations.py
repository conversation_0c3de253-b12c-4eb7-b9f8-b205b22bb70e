from django.core.management.base import BaseCommand
from magasins.models import LocalisationMagasin
from django.db import transaction
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Nettoie les localisations orphelines (non liées à un magasin)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Affiche les localisations qui seraient supprimées sans les supprimer',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Trouver les localisations orphelines
        orphaned_locations = LocalisationMagasin.objects.filter(magasin__isnull=True)
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: {orphaned_locations.count()} localisations orphelines trouvées'
                )
            )
            for location in orphaned_locations:
                self.stdout.write(
                    f'  - {location.ville} - {location.adresse} (ID: {location.id})'
                )
        else:
            count = orphaned_locations.count()
            if count > 0:
                with transaction.atomic():
                    orphaned_locations.delete()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'{count} localisations orphelines supprimées avec succès'
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('Aucune localisation orpheline trouvée')
                ) 