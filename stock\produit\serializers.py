from rest_framework import serializers
from django.db import models
from .models import Produit
from categorie.serializers import CategorieSerializer
from categorie.models import Categorie
from magasins.models import Magasin

class ProduitSerializer(serializers.ModelSerializer):
    categorie = CategorieSerializer(read_only=True)
    categorie_nom = serializers.CharField(write_only=True, required=True)
    stock_total = serializers.SerializerMethodField()
    stock_initial = serializers.IntegerField(write_only=True, required=False, default=0)
    nom_magasin = serializers.CharField(write_only=True, required=True)
    entreprise_magasin = serializers.SerializerMethodField()
    entreprise = serializers.SerializerMethodField()
    magasin_id = serializers.SerializerMethodField()
    magasin_nom = serializers.SerializerMethodField()

    class Meta:
        model = Produit
        fields = [
            'id_produit', 'nom', 'reference', 'categorie', 'categorie_nom', 'type_stock',
            'prix', 'date_peremption', 'code_barre', 'marque', 'description', 'unite_mesure',
            'prix_achat', 'prix_vente', 'TVA', 'created_at', 'updated_at', 'stock_total',
            'stock_initial', 'nom_magasin', 'entreprise_magasin', 'entreprise', 'magasin_id', 'magasin_nom'
        ]

    def get_stock_total(self, obj):
       from stock_Pme.models import Stock
       stock_total = Stock.objects.filter(produit=obj).aggregate(total=models.Sum('quantite_disponible'))['total'] or 0
       return stock_total

    def get_entreprise_magasin(self, obj):
        """Retourne l'entreprise du magasin qui contient le produit"""
        if obj.magasin and obj.magasin.entreprise:
            return {
                'id': obj.magasin.entreprise.id,
                'nom': obj.magasin.entreprise.nom,
                # Retirer 'description' car le modèle Entreprise n'a pas ce champ
            }
        return None

    def get_entreprise(self, obj):
        """Retourne l'entreprise du produit"""
        if obj.entreprise:
            return {
                'id': obj.entreprise.id,
                'nom': obj.entreprise.nom,
                # Retirer 'description' car le modèle Entreprise n'a pas ce champ
            }
        return None

    def get_magasin_id(self, obj):
        """Retourne l'ID du magasin"""
        return str(obj.magasin.id) if obj.magasin else None

    def get_magasin_nom(self, obj):
        """Retourne le nom du magasin"""
        return obj.magasin.nom if obj.magasin else None

    def validate_reference(self, value):
        if Produit.objects.filter(reference=value).exclude(id_produit=self.instance.id_produit if self.instance else None).exists():
            raise serializers.ValidationError("Cette référence est déjà utilisée.")
        return value

    def validate_code_barre(self, value):
        if value and Produit.objects.filter(code_barre=value).exclude(id_produit=self.instance.id_produit if self.instance else None).exists():
            raise serializers.ValidationError("Ce code-barre est déjà utilisé.")
        return value

    def validate_prix(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix doit être supérieur à 0.")
        return value

    def validate_prix_achat(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix d'achat doit être supérieur à 0.")
        return value

    def validate_prix_vente(self, value):
        if value <= 0:
            raise serializers.ValidationError("Le prix de vente doit être supérieur à 0.")
        return value

    def validate_TVA(self, value):
        if value < 0:
            raise serializers.ValidationError("La TVA ne peut pas être négative.")
        return value

    def validate(self, data):
        if not data.get('categorie_nom'):
            raise serializers.ValidationError("Vous devez fournir un nom de catégorie.")
        return data

    def create(self, validated_data):
        # Retirer categorie_nom, nom_magasin et stock_initial des données validées
        categorie_nom = validated_data.pop('categorie_nom')
        nom_magasin = validated_data.pop('nom_magasin')
        stock_initial = validated_data.pop('stock_initial', 0)

        # Trouver le magasin par son nom
        try:
            magasin = Magasin.objects.get(nom=nom_magasin)
            validated_data['magasin'] = magasin
            # Récupérer l'entreprise associée au magasin
            validated_data['entreprise'] = magasin.entreprise
        except Magasin.DoesNotExist:
            raise serializers.ValidationError(f"Aucun magasin trouvé avec le nom : {nom_magasin}")

        # Créer ou récupérer la catégorie
        try:
            # Essayer de trouver une catégorie existante
            categorie = Categorie.objects.get(nom=categorie_nom)
        except Categorie.DoesNotExist:
            # Créer une nouvelle catégorie si elle n'existe pas
            categorie = Categorie.objects.create(nom=categorie_nom)
        validated_data['categorie'] = categorie

        # Créer le produit
        produit = Produit.objects.create(**validated_data)

        # Créer le stock initial si spécifié
        if stock_initial > 0:
            from stock_Pme.models import Stock, Location
            from entrepot.models import Entrepot

            # Trouver l'entrepôt associé au magasin
            entrepot = magasin.entrepot
            if entrepot:
                # Trouver une location appropriée pour le type de stock
                location = Location.objects.filter(
                    entrepot=entrepot,
                    type_stock=produit.type_stock
                ).first()

                if location:
                    Stock.objects.create(
                        quantite_disponible=stock_initial,
                        seuil_alerte=max(1, stock_initial // 10),  # 10% du stock initial
                        produit=produit,
                        location=location,
                        magasin=magasin,
                        entrepot=entrepot
                    )

        return produit