from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase
from rest_framework import status
from decimal import Decimal
from .models import Produit
from categorie.models import Categorie
from entreprise.models import Entreprise
from magasins.models import Magasin
import uuid

class ProduitModelTest(TestCase):
    def setUp(self):
        # Création des données de test
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street"
        )
        
        self.categorie = Categorie.objects.create(
            nom="Test Catégorie",
            description="Description test",
            entreprise=self.entreprise
        )
        
        self.magasin = Magasin.objects.create(
            nom="Test Magasin",
            adresse="456 Test Avenue",
            entreprise=self.entreprise
        )
        
        self.produit_data = {
            'nom': 'Test Produit',
            'reference': 'REF001',
            'categorie': self.categorie,
            'magasin': self.magasin,
            'type_stock': 'FINIS',
            'prix': Decimal('10.00'),
            'unite_mesure': 'unité',
            'prix_achat': Decimal('8.00'),
            'prix_vente': Decimal('12.00'),
            'TVA': 0.2,
            'entreprise': self.entreprise
        }

    def test_creation_produit(self):
        """Test la création d'un produit"""
        produit = Produit.objects.create(**self.produit_data)
        self.assertEqual(produit.nom, 'Test Produit')
        self.assertEqual(produit.reference, 'REF001')
        self.assertTrue(produit.code_barre is not None)  # Vérifie que le code-barres est généré

    def test_validation_prix_positif(self):
        """Test la validation des prix positifs"""
        self.produit_data['prix'] = Decimal('-10.00')
        with self.assertRaises(ValidationError):
            produit = Produit.objects.create(**self.produit_data)
            produit.full_clean()

    def test_validation_tva_positive(self):
        """Test la validation de la TVA positive"""
        self.produit_data['TVA'] = -0.2
        with self.assertRaises(ValidationError):
            produit = Produit.objects.create(**self.produit_data)
            produit.full_clean()

    def test_validation_magasin_entreprise(self):
        """Test la validation du magasin appartenant à la même entreprise"""
        autre_entreprise = Entreprise.objects.create(
            nom="Autre Entreprise",
            adresse="789 Test Road"
        )
        autre_magasin = Magasin.objects.create(
            nom="Autre Magasin",
            adresse="101 Test Street",
            entreprise=autre_entreprise
        )
        self.produit_data['magasin'] = autre_magasin
        with self.assertRaises(ValidationError):
            produit = Produit.objects.create(**self.produit_data)
            produit.full_clean()

class ProduitAPITest(APITestCase):
    def setUp(self):
        # Configuration similaire à ProduitModelTest
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street"
        )
        
        self.categorie = Categorie.objects.create(
            nom="Test Catégorie",
            description="Description test",
            entreprise=self.entreprise
        )
        
        self.magasin = Magasin.objects.create(
            nom="Test Magasin",
            adresse="456 Test Avenue",
            entreprise=self.entreprise
        )
        
        self.produit = Produit.objects.create(
            nom='Test Produit',
            reference='REF001',
            categorie=self.categorie,
            magasin=self.magasin,
            type_stock='FINIS',
            prix=Decimal('10.00'),
            unite_mesure='unité',
            prix_achat=Decimal('8.00'),
            prix_vente=Decimal('12.00'),
            TVA=0.2,
            entreprise=self.entreprise
        )

    def test_liste_produits(self):
        """Test la récupération de la liste des produits"""
        response = self.client.get('/api/produits/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_detail_produit(self):
        """Test la récupération des détails d'un produit"""
        response = self.client.get(f'/api/produits/{self.produit.id_produit}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['nom'], 'Test Produit')

    def test_creation_produit_api(self):
        """Test la création d'un produit via l'API"""
        nouveau_produit = {
            'nom': 'Nouveau Produit',
            'reference': 'REF002',
            'categorie': self.categorie.id,
            'magasin': self.magasin.id,
            'type_stock': 'FINIS',
            'prix': '15.00',
            'unite_mesure': 'unité',
            'prix_achat': '12.00',
            'prix_vente': '18.00',
            'TVA': 0.2,
            'entreprise': self.entreprise.id
        }
        response = self.client.post('/api/produits/', nouveau_produit, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue('code_barre' in response.data)  # Vérifie que le code-barres est généré

    def test_modification_produit(self):
        """Test la modification d'un produit"""
        modification = {
            'prix': '20.00',
            'prix_vente': '25.00'
        }
        response = self.client.patch(
            f'/api/produits/{self.produit.id_produit}/',
            modification,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Decimal(response.data['prix']), Decimal('20.00'))

    def test_suppression_produit(self):
        """Test la suppression d'un produit"""
        response = self.client.delete(f'/api/produits/{self.produit.id_produit}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Produit.objects.filter(id_produit=self.produit.id_produit).exists())
