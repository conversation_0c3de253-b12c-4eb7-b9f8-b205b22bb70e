from rest_framework import serializers
from django.utils import timezone
from .models import NotificationType, NotificationPreference, Notification, NotificationBatch
from base.serializer import UserSerializer


class NotificationTypeSerializer(serializers.ModelSerializer):
    """Serializer pour les types de notifications"""

    class Meta:
        model = NotificationType
        fields = [
            'id', 'nom', 'description', 'categorie', 'priorite_defaut',
            'template_email', 'template_sms', 'template_push', 'template_in_app',
            'actif', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_template_sms(self, value):
        """Valide que le template SMS ne dépasse pas 160 caractères"""
        if value and len(value) > 160:
            raise serializers.ValidationError("Le template SMS ne peut pas dépasser 160 caractères.")
        return value


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    """Serializer pour les préférences de notifications"""

    user_details = UserSerializer(source='user', read_only=True)

    class Meta:
        model = NotificationPreference
        fields = [
            'user', 'user_details', 'email_enabled', 'sms_enabled',
            'push_enabled', 'in_app_enabled', 'stock_notifications',
            'vente_notifications', 'achat_notifications', 'inventaire_notifications',
            'finance_notifications', 'system_notifications', 'security_notifications',
            'quiet_hours_start', 'quiet_hours_end', 'weekend_notifications',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']

    def validate(self, data):
        """Valide les heures silencieuses"""
        quiet_start = data.get('quiet_hours_start')
        quiet_end = data.get('quiet_hours_end')

        if quiet_start and quiet_end and quiet_start == quiet_end:
            raise serializers.ValidationError(
                "L'heure de début et de fin des heures silencieuses ne peuvent pas être identiques."
            )

        return data


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer pour les notifications"""

    type_notification_details = NotificationTypeSerializer(source='type_notification', read_only=True)
    destinataire_details = UserSerializer(source='destinataire', read_only=True)
    canal_display = serializers.CharField(source='get_canal_display', read_only=True)
    statut_display = serializers.CharField(source='get_statut_display', read_only=True)
    priorite_display = serializers.CharField(source='get_priorite_display', read_only=True)
    est_lue = serializers.BooleanField(read_only=True)
    est_expiree = serializers.BooleanField(read_only=True)

    class Meta:
        model = Notification
        fields = [
            'id', 'type_notification', 'type_notification_details', 'destinataire',
            'destinataire_details', 'titre', 'message', 'donnees_contexte',
            'content_type', 'object_id', 'canal', 'canal_display', 'statut',
            'statut_display', 'priorite', 'priorite_display', 'date_creation',
            'date_envoi', 'date_lecture', 'date_expiration', 'tentatives_envoi',
            'erreur_envoi', 'identifiant_externe', 'est_lue', 'est_expiree',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'date_creation', 'date_envoi', 'date_lecture', 'tentatives_envoi',
            'erreur_envoi', 'identifiant_externe', 'created_at', 'updated_at'
        ]

    def validate_date_expiration(self, value):
        """Valide que la date d'expiration est dans le futur"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("La date d'expiration doit être dans le futur.")
        return value


class NotificationCreateSerializer(serializers.ModelSerializer):
    """Serializer pour créer une notification"""

    destinataires = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        help_text="Liste des IDs des destinataires"
    )

    class Meta:
        model = Notification
        fields = [
            'type_notification', 'destinataires', 'titre', 'message',
            'donnees_contexte', 'canal', 'priorite', 'date_expiration'
        ]

    def create(self, validated_data):
        """Crée plusieurs notifications pour les destinataires spécifiés"""
        destinataires_ids = validated_data.pop('destinataires')
        notifications = []

        for destinataire_id in destinataires_ids:
            try:
                from base.models import User
                destinataire = User.objects.get(id_utilisateur=destinataire_id)
                notification = Notification.objects.create(
                    destinataire=destinataire,
                    **validated_data
                )
                notifications.append(notification)
            except User.DoesNotExist:
                continue

        return notifications


class NotificationBatchSerializer(serializers.ModelSerializer):
    """Serializer pour les lots de notifications"""

    type_notification_details = NotificationTypeSerializer(source='type_notification', read_only=True)
    createur_details = UserSerializer(source='createur', read_only=True)
    statut_display = serializers.CharField(source='get_statut_display', read_only=True)
    taux_succes = serializers.FloatField(read_only=True)
    taux_echec = serializers.FloatField(read_only=True)

    class Meta:
        model = NotificationBatch
        fields = [
            'id', 'nom', 'description', 'type_notification', 'type_notification_details',
            'criteres_selection', 'statut', 'statut_display', 'total_destinataires',
            'notifications_envoyees', 'notifications_echouees', 'taux_succes',
            'taux_echec', 'date_planification', 'date_debut_traitement',
            'date_fin_traitement', 'createur', 'createur_details',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_destinataires', 'notifications_envoyees',
            'notifications_echouees', 'date_debut_traitement', 'date_fin_traitement',
            'createur', 'created_at', 'updated_at'
        ]


class NotificationStatsSerializer(serializers.Serializer):
    """Serializer pour les statistiques de notifications"""

    total_notifications = serializers.IntegerField()
    notifications_non_lues = serializers.IntegerField()
    notifications_par_canal = serializers.DictField()
    notifications_par_priorite = serializers.DictField()
    notifications_par_statut = serializers.DictField()
    notifications_recentes = NotificationSerializer(many=True)


class NotificationMarkAsReadSerializer(serializers.Serializer):
    """Serializer pour marquer des notifications comme lues"""

    notification_ids = serializers.ListField(
        child=serializers.UUIDField(),
        help_text="Liste des IDs des notifications à marquer comme lues"
    )

    def validate_notification_ids(self, value):
        """Valide que toutes les notifications existent et appartiennent à l'utilisateur"""
        user = self.context['request'].user
        existing_notifications = Notification.objects.filter(
            id__in=value,
            destinataire=user
        ).values_list('id', flat=True)

        if len(existing_notifications) != len(value):
            raise serializers.ValidationError(
                "Certaines notifications n'existent pas ou ne vous appartiennent pas."
            )

        return value
