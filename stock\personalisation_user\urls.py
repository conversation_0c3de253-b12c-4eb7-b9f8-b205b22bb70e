from django.urls import path
from .views import (
    List_User_View,
    GetUserView,
    Filter_Users_By_Role,
    GetUserByIdView,
    FilterUsersByEntrepriseView,
    FilterUsersByMagasinView,
    FilterUsersAdvancedView
)

urlpatterns = [
    path('list-users/', List_User_View.as_view(), name='list-users'),
    path('user/', GetUserView.as_view(), name='user-detail'),  # détails de l'utilisateur
    path('filter-users/', Filter_Users_By_Role.as_view(), name='filter-users'),  # filtrer par rôle
    path('user/<uuid:user_id>/', GetUserByIdView.as_view(), name='user-by-id'),
    path('filter-by-entreprise/', FilterUsersByEntrepriseView.as_view(), name='filter-users-by-entreprise'),
    path('filter-by-magasin/', FilterUsersByMagasinView.as_view(), name='filter-users-by-magasin'),
    path('filter-advanced/', FilterUsersAdvancedView.as_view(), name='filter-users-advanced'),
]
