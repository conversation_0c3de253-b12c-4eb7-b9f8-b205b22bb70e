from django.urls import path
from .views import (
    CapaciteEntrepotAPIView,
    RapportStockAPIView,
    MouvementStockAPIView,
    AlerteStockAPIView,
    StockSummaryAPIView,
    StockPerformanceAPIView,
    StockListAPIView,
    StockByTypeAPIView,
    StockByProductAPIView
)

urlpatterns = [
    path('entrepots/<uuid:entrepot_id>/capacite/', CapaciteEntrepotAPIView.as_view(), name='capacite-entrepot'),
    path('entrepots/<uuid:entrepot_id>/rapport/', RapportStockAPIView.as_view(), name='rapport-stock'),
    path('mouvements/', MouvementStockAPIView.as_view(), name='mouvement-stock'),
    path('alertes/', AlerteStockAPIView.as_view(), name='alertes-stock'),
    path('summary/', StockSummaryAPIView.as_view(), name='stock-summary'),
    path('performance/', StockPerformanceAPIView.as_view(), name='stock-performance'),
    path('stocks/', StockListAPIView.as_view(), name='stock-list'),
    path('stocks/by-type/', StockByTypeAPIView.as_view(), name='stock-by-type'),
    path('stocks/by-product/', StockByProductAPIView.as_view(), name='stock-by-product'),
]