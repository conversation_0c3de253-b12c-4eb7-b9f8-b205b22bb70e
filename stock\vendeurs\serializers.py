from rest_framework import serializers
from .models import <PERSON>endeur
from base.serializer import UserSerializer
from base.models import User
from base.utils import generate_username
from magasins.models import Magasin
import random

class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['nom', 'prenom', 'email', 'telephone', 'adresse']

class VendeurSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    performance = serializers.SerializerMethodField()
    user = UserUpdateSerializer()

    class Meta:
        model = Vendeur
        fields = ['id', 'user', 'user_details', 'date_embauche', 'taux_commission', 'notes', 'performance']

    def get_performance(self, obj):
        return obj.get_performance()

    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', {})
        if user_data:
            user = instance.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class VendeurCreateSerializer(serializers.Serializer):
    """Serializer pour créer un vendeur avec son utilisateur"""

    # Données utilisateur
    nom = serializers.CharField(max_length=150)
    prenom = serializers.CharField(max_length=150)
    email = serializers.EmailField(required=False, allow_blank=True, allow_null=True)
    telephone = serializers.CharField(max_length=20)
    adresse = serializers.CharField(required=False, allow_blank=True)
    password = serializers.CharField(write_only=True)

    # Données vendeur
    magasin = serializers.PrimaryKeyRelatedField(queryset=Magasin.objects.all())
    date_embauche = serializers.DateField()
    taux_commission = serializers.FloatField(default=0.1)
    notes = serializers.CharField(required=False, allow_blank=True)

    def validate_telephone(self, value):
        """Vérifier que le téléphone n'existe pas déjà"""
        if User.objects.filter(telephone=value).exists():
            raise serializers.ValidationError("Ce numéro de téléphone est déjà utilisé")
        return value

    def validate_email(self, value):
        """Vérifier que l'email n'existe pas déjà (si fourni)"""
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Cette adresse email est déjà utilisée")
        return value

    def create(self, validated_data):
        # Extraire les données du vendeur
        magasin = validated_data.pop('magasin')  # Ceci est déjà un objet Magasin grâce à PrimaryKeyRelatedField
        date_embauche = validated_data.pop('date_embauche')
        taux_commission = validated_data.pop('taux_commission', 0.1)
        notes = validated_data.pop('notes', '')

        # Générer un username unique
        nom = validated_data.get('nom', '')
        prenom = validated_data.get('prenom', '')
        username = generate_username(nom, prenom)
        attempt = 0

        while User.objects.filter(username=username).exists() and attempt < 10:
            username = generate_username(nom, prenom)
            attempt += 1

        if attempt >= 10:
            import time
            username = f"{username}{int(time.time())}"

        # Créer l'utilisateur avec le rôle VENDEUR
        user_data = {
            'username': username,
            'nom': validated_data['nom'],
            'prenom': validated_data['prenom'],
            'email': validated_data.get('email', ''),
            'telephone': validated_data['telephone'],
            'adresse': validated_data.get('adresse', ''),
            'password': validated_data['password'],
            'role': User.Role.VENDEUR
        }

        user_serializer = UserSerializer(data=user_data)
        user_serializer.is_valid(raise_exception=True)
        user = user_serializer.save()

        # Générer un code vendeur unique
        code_vendeur = self._generate_code_vendeur()

        # Créer le vendeur
        vendeur = Vendeur.objects.create(
            user=user,
            magasin=magasin,
            code_vendeur=code_vendeur,
            date_embauche=date_embauche,
            taux_commission=taux_commission,
            notes=notes
        )

        # Inclure l'entreprise dans l'instance pour la réponse
        vendeur.entreprise_id = magasin.entreprise.id if magasin.entreprise else None
        return vendeur

    def _generate_code_vendeur(self):
        """Générer un code vendeur unique"""
        while True:
            code = f"VEND-{random.randint(100000, 999999)}"
            if not Vendeur.objects.filter(code_vendeur=code).exists():
                return code

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['entreprise'] = instance.magasin.entreprise.id if instance.magasin and instance.magasin.entreprise else None
        data['magasin'] = str(instance.magasin.id) if instance.magasin else None
        return data

    
