# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepot', '0001_initial'),
        ('entreprise', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LocalisationMagasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('adresse', models.TextField()),
                ('ville', models.CharField(max_length=100)),
                ('code_postal', models.CharField(max_length=20)),
                ('pays', models.CharField(max_length=100)),
                ('telephone', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator('^\\+?[1-9]\\d{1,14}$', 'Format de téléphone invalide')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Magasin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100)),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('entrepot', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='magasins', to='entrepot.entrepot')),
                ('entreprise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='magasins', to='entreprise.entreprise')),
                ('localisation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='magasin', to='magasins.localisationmagasin')),
            ],
            options={
                'verbose_name': 'Magasin',
                'verbose_name_plural': 'Magasins',
            },
        ),
    ]
