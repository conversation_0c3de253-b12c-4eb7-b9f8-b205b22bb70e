from rest_framework import serializers
from .models import Entreprise
from base.serializer import UserSerializer

class EntrepriseSerializer(serializers.ModelSerializer):
    administrateur = UserSerializer(read_only=True)

    class Meta:
        model = Entreprise
        fields = ['id', 'nom', 'adresse', 'nif', 'date_creation', 'statut', 'administrateur']

    def create(self, validated_data):
        if 'administrateur' in validated_data and validated_data['administrateur']:
            from base.models import User
            validated_data['administrateur'] = User.objects.get(id=validated_data['administrateur'])
        return super().create(validated_data)

    def update(self, instance, validated_data):
        if 'administrateur' in validated_data:
            from base.models import User
            if validated_data['administrateur']:
                validated_data['administrateur'] = User.objects.get(id=validated_data['administrateur'])
            else:
                validated_data['administrateur'] = None
        return super().update(instance, validated_data)

    def validate_nif(self, value):
        if not value.isdigit() or len(value) != 13:
            raise serializers.ValidationError("Le NIF doit contenir exactement 13 chiffres")
        return value

class RapportEntrepriseSerializer(serializers.Serializer):
    total = serializers.IntegerField()
    actives = serializers.IntegerField()
    par_annee = serializers.ListField(child=serializers.DictField())
            