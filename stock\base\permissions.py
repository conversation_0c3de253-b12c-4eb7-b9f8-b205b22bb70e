"""
Système de permissions centralisé pour l'application de gestion de stock
"""
from rest_framework.permissions import BasePermission
from django.core.exceptions import ObjectDoesNotExist


class IsAdmin(BasePermission):
    """Permission pour les administrateurs"""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'ADMIN'


class IsVendeur(BasePermission):
    """Permission pour les vendeurs"""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'VENDEUR'


class IsResponsableMagasin(BasePermission):
    """Permission pour les responsables magasin"""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'RESPONSABLE_MAGASIN'


class IsResponsableEntrepot(BasePermission):
    """Permission pour les responsables entrepôt"""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'RESPONSABLE_ENTREPOT'


class IsFournisseur(BasePermission):
    """Permission pour les fournisseurs"""
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.role == 'FOURNISSEUR'


# Permissions combinées pour différents contextes métier

class MagasinPermissions(BasePermission):
    """
    Permissions pour la gestion des magasins
    Autorisé: Admin, Responsable Magasin, Vendeur
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
        return request.user.role in allowed_roles


class VentePermissions(BasePermission):
    """
    Permissions pour la gestion des ventes
    Autorisé: Admin, Responsable Magasin, Vendeur
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
        return request.user.role in allowed_roles


class EntrepotPermissions(BasePermission):
    """
    Permissions pour la gestion des entrepôts
    Autorisé: Admin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class StockPermissions(BasePermission):
    """
    Permissions pour la gestion du stock
    Autorisé: Admin, Responsable Magasin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class AchatPermissions(BasePermission):
    """
    Permissions pour la gestion des achats
    Autorisé: Admin, Responsable Magasin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class ClientPermissions(BasePermission):
    """
    Permissions pour la gestion des clients
    Autorisé: Admin, Responsable Magasin, Vendeur
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'VENDEUR']
        return request.user.role in allowed_roles


class FournisseurPermissions(BasePermission):
    """
    Permissions pour la gestion des fournisseurs
    Autorisé: Admin, Responsable Entrepôt, Fournisseur
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_ENTREPOT', 'FOURNISSEUR']
        return request.user.role in allowed_roles


class ProduitPermissions(BasePermission):
    """
    Permissions pour la gestion des produits
    Autorisé: Admin, Responsable Magasin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPÔT']
        return request.user.role in allowed_roles


class InventairePermissions(BasePermission):
    """
    Permissions pour la gestion de l'inventaire
    Autorisé: Admin, Responsable Magasin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class StatistiquePermissions(BasePermission):
    """
    Permissions pour la consultation des statistiques
    Autorisé: Admin, Responsable Magasin, Responsable Entrepôt
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class NotificationPermissions(BasePermission):
    """
    Permissions pour la gestion des notifications
    Autorisé: Tous les utilisateurs authentifiés
    """
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated


# Permissions avec logique métier avancée

class MagasinOwnerPermission(BasePermission):
    """
    Permission pour vérifier que l'utilisateur a accès au magasin spécifique
    """
    def has_object_permission(self, request, view, obj):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Admin a accès à tout
        if request.user.role == 'ADMIN':
            return True
        
        # Responsable magasin : accès à son magasin
        if request.user.role == 'RESPONSABLE_MAGASIN':
            try:
                return obj == request.user.responsable_magasin.magasin
            except ObjectDoesNotExist:
                return False
        
        # Vendeur : accès à son magasin
        if request.user.role == 'VENDEUR':
            try:
                return obj == request.user.vendeur.magasin
            except ObjectDoesNotExist:
                return False
        
        return False


class EntrepotOwnerPermission(BasePermission):
    """
    Permission pour vérifier que l'utilisateur a accès à l'entrepôt spécifique
    """
    def has_object_permission(self, request, view, obj):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Admin a accès à tout
        if request.user.role == 'ADMIN':
            return True
        
        # Responsable entrepôt : accès à son entrepôt
        if request.user.role == 'RESPONSABLE_ENTREPOT':
            try:
                return obj == request.user.responsableentrepot.entrepot
            except ObjectDoesNotExist:
                return False
        
        return False


class VenteOwnerPermission(BasePermission):
    """
    Permission pour vérifier que l'utilisateur a accès à la vente spécifique
    """
    def has_object_permission(self, request, view, obj):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Admin a accès à tout
        if request.user.role == 'ADMIN':
            return True
        
        # Vendeur : accès à ses ventes uniquement
        if request.user.role == 'VENDEUR':
            try:
                return obj.vendeur.user == request.user
            except ObjectDoesNotExist:
                return False
        
        # Responsable magasin : accès aux ventes de son magasin
        if request.user.role == 'RESPONSABLE_MAGASIN':
            try:
                return obj.magasin == request.user.responsable_magasin.magasin
            except ObjectDoesNotExist:
                return False
        
        return False


# Permissions pour les opérations de lecture/écriture

class ReadOnlyForVendeur(BasePermission):
    """
    Permission lecture seule pour les vendeurs, lecture/écriture pour les autres
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Vendeur : lecture seule
        if request.user.role == 'VENDEUR':
            return request.method in ['GET', 'HEAD', 'OPTIONS']
        
        # Autres rôles : accès complet
        allowed_roles = ['ADMIN', 'RESPONSABLE_MAGASIN', 'RESPONSABLE_ENTREPOT']
        return request.user.role in allowed_roles


class AdminOrReadOnly(BasePermission):
    """
    Permission lecture pour tous, écriture pour admin seulement
    """
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # Lecture pour tous les utilisateurs authentifiés
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Écriture pour admin seulement
        return request.user.role == 'ADMIN'
