"""
Système de monitoring des performances
"""

import time
import psutil
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from .cache_service import CacheService, CacheMonitor

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Moniteur de performance système"""

    @staticmethod
    def get_system_metrics() -> Dict[str, Any]:
        """Récupère les métriques système"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()

            # Mémoire
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB

            # Disque
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            disk_total = disk.total / (1024**3)  # GB

            # Processus
            process_count = len(psutil.pids())

            return {
                'timestamp': timezone.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                    'load_avg': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                'memory': {
                    'percent': memory_percent,
                    'available_gb': round(memory_available, 2),
                    'total_gb': round(memory_total, 2),
                    'used_gb': round(memory_total - memory_available, 2)
                },
                'disk': {
                    'percent': disk_percent,
                    'free_gb': round(disk_free, 2),
                    'total_gb': round(disk_total, 2),
                    'used_gb': round(disk_total - disk_free, 2)
                },
                'processes': process_count
            }
        except Exception as e:
            logger.error(f"Erreur récupération métriques système: {str(e)}")
            return {}

    @staticmethod
    def get_database_metrics() -> Dict[str, Any]:
        """Récupère les métriques de base de données SQLite"""
        try:
            import os
            from django.conf import settings

            # Chemin de la base SQLite
            db_path = settings.DATABASES['default']['NAME']

            # Taille du fichier SQLite
            db_size_bytes = os.path.getsize(db_path) if os.path.exists(db_path) else 0
            db_size_mb = db_size_bytes / (1024 * 1024)

            with connection.cursor() as cursor:
                # Informations SQLite
                cursor.execute("PRAGMA database_list;")
                db_info = cursor.fetchall()

                # Statistiques des tables principales
                cursor.execute("""
                    SELECT name, sql FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name;
                """)
                tables = cursor.fetchall()

                # Compter les enregistrements par table
                table_stats = []
                for table_name, _ in tables[:10]:  # Limiter à 10 tables
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                        count = cursor.fetchone()[0]
                        table_stats.append({
                            'table': table_name,
                            'record_count': count
                        })
                    except:
                        continue

                # Informations sur les index
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='index' AND name NOT LIKE 'sqlite_%';
                """)
                indexes = [row[0] for row in cursor.fetchall()]

                return {
                    'timestamp': timezone.now().isoformat(),
                    'database_type': 'SQLite',
                    'database_size_mb': round(db_size_mb, 2),
                    'database_size_bytes': db_size_bytes,
                    'database_path': db_path,
                    'table_count': len(tables),
                    'index_count': len(indexes),
                    'table_statistics': table_stats,
                    'indexes': indexes[:10]  # Limiter l'affichage
                }
        except Exception as e:
            logger.error(f"Erreur récupération métriques DB SQLite: {str(e)}")
            return {}

    @staticmethod
    def get_application_metrics() -> Dict[str, Any]:
        """Récupère les métriques de l'application Django"""
        try:
            # Statistiques du cache
            cache_stats = CacheMonitor.get_cache_stats()
            cache_hit_ratio = CacheMonitor.get_hit_ratio()

            # Nombre d'utilisateurs actifs (dernières 24h)
            from base.models import User
            yesterday = timezone.now() - timedelta(days=1)
            active_users = User.objects.filter(last_login__gte=yesterday).count()

            # Nombre de notifications non lues
            from notifications.models import Notification
            unread_notifications = Notification.objects.filter(statut__in=['PENDING', 'SENT']).count()

            # Alertes de stock
            from stock_Pme.models import Stock
            from django.db.models import F
            stock_alerts = Stock.objects.filter(quantite_disponible__lte=F('seuil_alerte')).count()

            return {
                'timestamp': timezone.now().isoformat(),
                'cache': {
                    'hit_ratio': cache_hit_ratio,
                    'stats': cache_stats
                },
                'users': {
                    'active_24h': active_users,
                    'total': User.objects.count()
                },
                'notifications': {
                    'unread': unread_notifications,
                    'total': Notification.objects.count()
                },
                'stock': {
                    'alerts': stock_alerts,
                    'total_products': Stock.objects.count()
                }
            }
        except Exception as e:
            logger.error(f"Erreur récupération métriques app: {str(e)}")
            return {}


class PerformanceLogger:
    """Logger de performance pour les requêtes et opérations"""

    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
        self.initial_queries = 0
        self.final_queries = 0

    def __enter__(self):
        self.start_time = time.time()
        if settings.DEBUG:
            self.initial_queries = len(connection.queries)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        execution_time = self.end_time - self.start_time

        if settings.DEBUG:
            self.final_queries = len(connection.queries)
            queries_count = self.final_queries - self.initial_queries

            # Log de performance
            logger.info(
                f"Performance: {self.operation_name} - "
                f"Time: {execution_time:.3f}s - "
                f"Queries: {queries_count}"
            )

            # Alerte si performance dégradée
            if execution_time > 1.0:  # Plus d'1 seconde
                logger.warning(
                    f"Slow operation detected: {self.operation_name} took {execution_time:.3f}s"
                )

            if queries_count > 20:  # Plus de 20 requêtes
                logger.warning(
                    f"High query count: {self.operation_name} executed {queries_count} queries"
                )

        # Stocker les métriques dans le cache pour analyse
        self._store_metrics(execution_time)

    def _store_metrics(self, execution_time: float):
        """Stocke les métriques de performance"""
        try:
            metrics_key = f"performance_metrics:{self.operation_name}"
            current_metrics = cache.get(metrics_key, [])

            # Ajouter la nouvelle métrique
            current_metrics.append({
                'timestamp': timezone.now().isoformat(),
                'execution_time': execution_time,
                'queries_count': getattr(self, 'final_queries', 0) - getattr(self, 'initial_queries', 0)
            })

            # Garder seulement les 100 dernières métriques
            if len(current_metrics) > 100:
                current_metrics = current_metrics[-100:]

            # Stocker pour 24h
            cache.set(metrics_key, current_metrics, 86400)

        except Exception as e:
            logger.error(f"Erreur stockage métriques: {str(e)}")


class AlertManager:
    """Gestionnaire d'alertes de performance"""

    THRESHOLDS = {
        'cpu_percent': 80,
        'memory_percent': 85,
        'disk_percent': 90,
        'response_time': 2.0,  # secondes
        'cache_hit_ratio': 70,  # pourcentage
        'active_connections': 50
    }

    @classmethod
    def check_system_alerts(cls) -> List[Dict]:
        """Vérifie les alertes système"""
        alerts = []
        metrics = PerformanceMonitor.get_system_metrics()

        if not metrics:
            return alerts

        # Alerte CPU
        if metrics.get('cpu', {}).get('percent', 0) > cls.THRESHOLDS['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'level': 'warning',
                'message': f"CPU usage high: {metrics['cpu']['percent']:.1f}%",
                'value': metrics['cpu']['percent'],
                'threshold': cls.THRESHOLDS['cpu_percent']
            })

        # Alerte mémoire
        if metrics.get('memory', {}).get('percent', 0) > cls.THRESHOLDS['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'level': 'warning',
                'message': f"Memory usage high: {metrics['memory']['percent']:.1f}%",
                'value': metrics['memory']['percent'],
                'threshold': cls.THRESHOLDS['memory_percent']
            })

        # Alerte disque
        if metrics.get('disk', {}).get('percent', 0) > cls.THRESHOLDS['disk_percent']:
            alerts.append({
                'type': 'disk_high',
                'level': 'critical',
                'message': f"Disk usage critical: {metrics['disk']['percent']:.1f}%",
                'value': metrics['disk']['percent'],
                'threshold': cls.THRESHOLDS['disk_percent']
            })

        return alerts

    @classmethod
    def check_application_alerts(cls) -> List[Dict]:
        """Vérifie les alertes application"""
        alerts = []
        metrics = PerformanceMonitor.get_application_metrics()

        if not metrics:
            return alerts

        # Alerte ratio de cache
        cache_hit_ratio = metrics.get('cache', {}).get('hit_ratio', 100)
        if cache_hit_ratio < cls.THRESHOLDS['cache_hit_ratio']:
            alerts.append({
                'type': 'cache_low_hit_ratio',
                'level': 'warning',
                'message': f"Cache hit ratio low: {cache_hit_ratio:.1f}%",
                'value': cache_hit_ratio,
                'threshold': cls.THRESHOLDS['cache_hit_ratio']
            })

        # Alerte stock
        stock_alerts = metrics.get('stock', {}).get('alerts', 0)
        if stock_alerts > 0:
            alerts.append({
                'type': 'stock_alerts',
                'level': 'info',
                'message': f"{stock_alerts} products below stock threshold",
                'value': stock_alerts
            })

        return alerts

    @classmethod
    def send_alert_notification(cls, alert: Dict):
        """Envoie une notification d'alerte"""
        try:
            from notifications.services import NotificationService
            from base.models import User

            # Envoyer aux administrateurs
            admins = User.objects.filter(role='ADMIN', statut=True)

            if admins.exists():
                NotificationService.creer_et_envoyer_notification(
                    type_notification='SYSTEM_ALERT',
                    destinataires=list(admins),
                    titre=f"Alerte Système: {alert['type']}",
                    message=alert['message'],
                    donnees_contexte=alert,
                    canal='IN_APP',
                    priorite='HIGH' if alert['level'] == 'critical' else 'MEDIUM'
                )
        except Exception as e:
            logger.error(f"Erreur envoi alerte: {str(e)}")


class PerformanceReporter:
    """Générateur de rapports de performance"""

    @staticmethod
    def generate_daily_report() -> Dict:
        """Génère un rapport journalier de performance"""
        try:
            # Métriques actuelles
            system_metrics = PerformanceMonitor.get_system_metrics()
            db_metrics = PerformanceMonitor.get_database_metrics()
            app_metrics = PerformanceMonitor.get_application_metrics()

            # Alertes
            system_alerts = AlertManager.check_system_alerts()
            app_alerts = AlertManager.check_application_alerts()

            # Métriques de performance des opérations
            performance_summary = PerformanceReporter._get_performance_summary()

            report = {
                'date': timezone.now().date().isoformat(),
                'timestamp': timezone.now().isoformat(),
                'system': system_metrics,
                'database': db_metrics,
                'application': app_metrics,
                'alerts': {
                    'system': system_alerts,
                    'application': app_alerts,
                    'total_count': len(system_alerts) + len(app_alerts)
                },
                'performance': performance_summary,
                'summary': {
                    'status': 'healthy' if not (system_alerts + app_alerts) else 'warning',
                    'critical_alerts': len([a for a in system_alerts + app_alerts if a.get('level') == 'critical']),
                    'warning_alerts': len([a for a in system_alerts + app_alerts if a.get('level') == 'warning'])
                }
            }

            return report

        except Exception as e:
            logger.error(f"Erreur génération rapport: {str(e)}")
            return {}

    @staticmethod
    def _get_performance_summary() -> Dict:
        """Résumé des performances des opérations"""
        try:
            # Récupérer les métriques de performance du cache
            operations = ['api_request', 'database_query', 'cache_operation']
            summary = {}

            for operation in operations:
                metrics_key = f"performance_metrics:{operation}"
                metrics = cache.get(metrics_key, [])

                if metrics:
                    execution_times = [m['execution_time'] for m in metrics]
                    summary[operation] = {
                        'count': len(metrics),
                        'avg_time': sum(execution_times) / len(execution_times),
                        'max_time': max(execution_times),
                        'min_time': min(execution_times)
                    }
                else:
                    summary[operation] = {
                        'count': 0,
                        'avg_time': 0,
                        'max_time': 0,
                        'min_time': 0
                    }

            return summary

        except Exception as e:
            logger.error(f"Erreur résumé performance: {str(e)}")
            return {}


# Décorateur pour monitorer les performances
def monitor_performance(operation_name: str):
    """Décorateur pour monitorer les performances d'une fonction"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceLogger(operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator
