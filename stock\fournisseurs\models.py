from django.db import models
from base.models import User

class Fournisseur(models.Model):
    STATUT_CHOICES = (
        ('ACTIF', 'Actif'),
        ('INACTIF', 'Inactif'),
    )

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        limit_choices_to={'role': User.Role.FOURNISSEUR}
    )
    reference_fiscale = models.CharField(max_length=50, unique=True, null=True, blank=True)
    categorie_produits = models.CharField(max_length=100)
    statut = models.CharField(max_length=10, choices=STATUT_CHOICES, default='ACTIF')
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user.nom} ({self.reference_fiscale or 'sans référence'})"

    def creer_commande(self, data):
        from achat_magasin.models import AchatMagasin
        return AchatMagasin.objects.create(
            fournisseur=self,
            date_commande=data.get('date_commande'),
            date_reception_prevue=data.get('date_reception_prevue'),
            statut='EN_ATTENTE',
            montant_total=0,
        )

    def mettre_a_jour_statut(self, new_statut):
        if new_statut not in dict(self.STATUT_CHOICES):
            raise ValueError("Statut invalide")
        self.statut = new_statut
        self.save()