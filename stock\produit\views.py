from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.db import models
from .models import Produit
from .serializers import ProduitSerializer
from django.core.exceptions import ValidationError
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from entreprise.models import Entreprise
from django.db.models import Q
from base.permissions import ProduitPermissions


class ProduitListCreateView(APIView):
    permission_classes = [IsAuthenticated, ProduitPermissions]
    @swagger_auto_schema(
        operation_description="Récupérer la liste de tous les produits",
        responses={
            200: openapi.Response(
                description="Liste des produits",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    )
    def get(self, request):
        produits = Produit.getAllProducts()
        serializer = ProduitSerializer(produits, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau produit",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['nom', 'reference', 'prix', 'unite_mesure', 'prix_achat', 'prix_vente', 'entreprise', 'categorie_nom', 'nom_magasin'],
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING, max_length=100, description="Nom du produit"),
                'reference': openapi.Schema(type=openapi.TYPE_STRING, max_length=50, description="Référence unique du produit"),
                'categorie_nom': openapi.Schema(type=openapi.TYPE_STRING, description="Nom de la catégorie (requis pour création)"),
                'nom_magasin': openapi.Schema(type=openapi.TYPE_STRING, description="Nom du magasin (requis pour création)"),
                'type_stock': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['FINIS', 'MATIERE', 'EMBALLAGE', 'SEMI_FINI', 'CONSOMMABLE'],
                    default='FINIS',
                    description="Type de stock du produit"
                ),
                'prix': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal', description="Prix du produit"),
                'date_peremption': openapi.Schema(type=openapi.TYPE_STRING, format='date', nullable=True, description="Date de péremption (optionnel)"),
                'code_barre': openapi.Schema(type=openapi.TYPE_INTEGER, nullable=True, description="Code barre (optionnel)"),
                'marque': openapi.Schema(type=openapi.TYPE_STRING, max_length=50, description="Marque du produit (optionnel)"),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description="Description du produit (optionnel)"),
                'unite_mesure': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['kg', 'g', 'L', 'ml', 'unité'],
                    description="Unité de mesure du produit"
                ),
                'prix_achat': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal', description="Prix d'achat du produit"),
                'prix_vente': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal', description="Prix de vente du produit"),
                'TVA': openapi.Schema(type=openapi.TYPE_NUMBER, format='float', default=0.2, description="Taux de TVA (défaut: 0.2)"),
                'entreprise': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', description="ID de l'entreprise"),
                'stock_initial': openapi.Schema(type=openapi.TYPE_INTEGER, default=0, description="Stock initial (optionnel, défaut: 0)")
            }
        ),
        responses={
            201: openapi.Response(
                description="Produit créé avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request - Données invalides ou champs manquants"
        }
    )
    def post(self, request):
        serializer = ProduitSerializer(data=request.data)
        if serializer.is_valid():
            produit = serializer.save()
            return Response(ProduitSerializer(produit).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ProduitDetailView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un produit spécifique",
        responses={
            200: openapi.Response(
                description="Détails du produit",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            serializer = ProduitSerializer(produit)
            return Response(serializer.data)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un produit existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'reference': openapi.Schema(type=openapi.TYPE_STRING, max_length=50),
                'categorie': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'magasin': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True),
                'type_stock': openapi.Schema(type=openapi.TYPE_STRING, enum=['FINIS', 'MATIERE', 'EMBALLAGE', 'SEMI_FINI', 'CONSOMMABLE']),
                'prix': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                'date_peremption': openapi.Schema(type=openapi.TYPE_STRING, format='date', nullable=True),
                'code_barre': openapi.Schema(type=openapi.TYPE_INTEGER, nullable=True),
                'marque': openapi.Schema(type=openapi.TYPE_STRING, max_length=50),
                'description': openapi.Schema(type=openapi.TYPE_STRING),
                'unite_mesure': openapi.Schema(type=openapi.TYPE_STRING, enum=['kg', 'g', 'L', 'ml', 'unité']),
                'prix_achat': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                'prix_vente': openapi.Schema(type=openapi.TYPE_NUMBER, format='decimal'),
                'TVA': openapi.Schema(type=openapi.TYPE_NUMBER, format='float'),
                'entreprise': openapi.Schema(type=openapi.TYPE_STRING, format='uuid')
            }
        ),
        responses={
            200: openapi.Response(
                description="Produit mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            serializer = ProduitSerializer(produit, data=request.data, partial=True)
            if serializer.is_valid():
                produit.mettreÀJourProduit(serializer.validated_data)
                return Response(ProduitSerializer(produit).data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un produit",
        responses={
            200: "Produit supprimé avec succès",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        produit = Produit.getProductById(id)
        if produit:
            try:
                resultat = produit.supprimer_produit()
                return Response({
                    "message": "Produit supprimé avec succès",
                    "stock_restant": resultat.get('nouvelle_quantite', 0)
                }, status=status.HTTP_200_OK)
            except ValidationError as e:
                return Response(
                    {"error": str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {"error": f"Erreur lors de la suppression du produit : {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(status=status.HTTP_404_NOT_FOUND)


class ProduitsParEntrepriseView(APIView):
    @swagger_auto_schema(
        operation_description="Récupérer tous les produits d'une entreprise, groupés par magasin",
        manual_parameters=[
            openapi.Parameter(
                'entreprise_id',
                openapi.IN_PATH,
                description="ID de l'entreprise",
                type=openapi.TYPE_STRING,
                format='uuid',
                required=True
            ),
        ],
        responses={
            200: openapi.Response(
                description="Produits de l'entreprise groupés par magasin",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'entreprise': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'magasins': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        )
                    }
                )
            ),
            404: "Entreprise non trouvée"
        }
    )
    def get(self, request, entreprise_id):
        try:
            entreprise = Entreprise.objects.get(id=entreprise_id)
        except Entreprise.DoesNotExist:
            return Response(
                {"error": "Entreprise non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Récupérer tous les produits de cette entreprise
        produits = Produit.objects.filter(entreprise=entreprise).select_related('magasin', 'categorie')

        # Grouper par magasin
        result = {
            'entreprise': {
                'id': entreprise.id,
                'nom': entreprise.nom,
                'description': entreprise.description
            },
            'magasins': []
        }

        # Créer un dictionnaire pour grouper les produits par magasin
        magasins_produits = {}

        for produit in produits:
            magasin_key = produit.magasin.id if produit.magasin else 'sans_magasin'

            if magasin_key not in magasins_produits:
                magasin_data = {
                    'id': produit.magasin.id if produit.magasin else None,
                    'nom': produit.magasin.nom if produit.magasin else 'Sans magasin',
                    'produits': []
                }
                magasins_produits[magasin_key] = magasin_data

            # Sérialiser le produit
            produit_data = ProduitSerializer(produit).data
            magasins_produits[magasin_key]['produits'].append(produit_data)

        # Convertir le dictionnaire en liste
        result['magasins'] = list(magasins_produits.values())

        return Response(result)


class ProduitsParMagasinView(APIView):
    permission_classes = [IsAuthenticated, ProduitPermissions]

    @swagger_auto_schema(
        operation_description="Récupérer tous les produits d'un magasin spécifique avec leurs informations de stock",
        manual_parameters=[
            openapi.Parameter(
                'magasin_id',
                openapi.IN_PATH,
                description="ID du magasin",
                type=openapi.TYPE_STRING,
                format='uuid',
                required=True
            ),
            openapi.Parameter(
                'search',
                openapi.IN_QUERY,
                description="Rechercher par nom ou référence de produit",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'categorie',
                openapi.IN_QUERY,
                description="Filtrer par nom de catégorie",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'type_stock',
                openapi.IN_QUERY,
                description="Filtrer par type de stock (FINIS, MATIERE_PREMIERE, SEMI_FINIS)",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={
            200: openapi.Response(
                description="Produits du magasin avec informations de stock",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'magasin': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING),
                                'nom': openapi.Schema(type=openapi.TYPE_STRING),
                                'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                            }
                        ),
                        'total_produits': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'produits': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        )
                    }
                )
            ),
            404: "Magasin non trouvé",
            403: "Accès non autorisé"
        }
    )
    def get(self, request, magasin_id):
        try:
            from magasins.models import Magasin
            magasin = Magasin.objects.select_related('localisation').get(id=magasin_id)
        except Magasin.DoesNotExist:
            return Response(
                {"error": "Magasin non trouvé"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Vérifier les permissions d'accès au magasin
        user = request.user
        if user.role == 'RESPONSABLE_MAGASIN':
            try:
                if user.responsable_magasin.magasin.id != magasin.id:
                    return Response(
                        {"error": "Accès non autorisé à ce magasin"},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except:
                return Response(
                    {"error": "Accès non autorisé"},
                    status=status.HTTP_403_FORBIDDEN
                )
        elif user.role not in ['ADMIN', 'RESPONSABLE_ENTREPOT']:
            return Response(
                {"error": "Accès non autorisé"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Récupérer tous les produits de ce magasin
        produits = Produit.objects.filter(magasin=magasin).select_related('categorie')

        # Appliquer les filtres
        search = request.query_params.get('search')
        if search:
            from django.db.models import Q
            produits = produits.filter(
                Q(nom__icontains=search) | Q(reference__icontains=search)
            )

        categorie = request.query_params.get('categorie')
        if categorie:
            produits = produits.filter(categorie__nom__icontains=categorie)

        type_stock = request.query_params.get('type_stock')
        if type_stock:
            produits = produits.filter(type_stock=type_stock)

        # Préparer les données de réponse
        produits_data = []
        for produit in produits:
            # Récupérer les informations de stock
            from stock_Pme.models import Stock
            stock_info = Stock.objects.filter(produit=produit, magasin=magasin).aggregate(
                total_stock=models.Sum('quantite_disponible'),
                seuil_alerte=models.Min('seuil_alerte')
            )

            produit_data = {
                'id_produit': produit.id_produit,
                'nom': produit.nom,
                'reference': produit.reference,
                'prix_vente': produit.prix_vente,
                'prix_achat': produit.prix_achat,
                'TVA': produit.TVA,
                'unite_mesure': produit.unite_mesure,
                'description': produit.description,
                'type_stock': produit.type_stock,
                'date_peremption': produit.date_peremption,
                'marque': produit.marque,
                'code_barre': produit.code_barre,
                'categorie': {
                    'id': produit.categorie.id if produit.categorie else None,
                    'nom': produit.categorie.nom if produit.categorie else None
                },
                'stock': {
                    'quantite_disponible': stock_info['total_stock'] or 0,
                    'seuil_alerte': stock_info['seuil_alerte'] or 0,
                    'en_rupture': (stock_info['total_stock'] or 0) <= (stock_info['seuil_alerte'] or 0)
                },
                'created_at': produit.created_at,
                'updated_at': produit.updated_at
            }
            produits_data.append(produit_data)

        # Préparer la réponse
        result = {
            'magasin': {
                'id': magasin.id,
                'nom': magasin.nom,
                'adresse': magasin.localisation.adresse if magasin.localisation else None,
                'ville': magasin.localisation.ville if magasin.localisation else None,
                'telephone': magasin.localisation.telephone if magasin.localisation else None,
            },
            'total_produits': len(produits_data),
            'produits': produits_data
        }

        return Response(result)

           
            
    