# Fonctionnalité : Récupération des Produits par Magasin

## 📋 Description

Cette fonctionnalité permet de récupérer tous les produits associés à un magasin spécifique avec leurs informations de stock détaillées.

## 🚀 Fonctionnalités Ajoutées

### 1. Nouvelle Méthode dans le Modèle Produit

**Fichier :** `stock/produit/models.py`

```python
@classmethod
def getProductsByMagasin(cls, magasin_id):
    """Récupère tous les produits d'un magasin spécifique avec leurs informations de stock"""
    return cls.objects.filter(magasin_id=magasin_id).select_related('categorie', 'magasin').prefetch_related('stock_set')
```

### 2. Nouvelle Vue API

**Fichier :** `stock/produit/views.py`

**Classe :** `ProduitsParMagasinView`

**Fonctionnalités :**
- Récupération des produits par magasin
- Filtrage par recherche (nom/référence)
- Filtrage par catégorie
- Filtrage par type de stock
- Informations de stock détaillées
- Contrôle d'accès basé sur les rôles

### 3. Nouvel Endpoint API

**URL :** `/api/produits/magasin/<uuid:magasin_id>/produits/`

**Méthode :** `GET`

**Paramètres de requête optionnels :**
- `search` : Recherche par nom ou référence de produit
- `categorie` : Filtrage par nom de catégorie
- `type_stock` : Filtrage par type de stock (FINIS, MATIERE_PREMIERE, SEMI_FINIS)

## 📊 Format de Réponse

```json
{
  "magasin": {
    "id": "uuid-du-magasin",
    "nom": "Nom du Magasin",
    "adresse": "Adresse du magasin",
    "ville": "Ville",
    "telephone": "+261xxxxxxxxx"
  },
  "total_produits": 15,
  "produits": [
    {
      "id_produit": "uuid-du-produit",
      "nom": "Nom du Produit",
      "reference": "REF001",
      "prix_vente": "12.00",
      "prix_achat": "8.00",
      "TVA": 0.2,
      "unite_mesure": "unité",
      "description": "Description du produit",
      "type_stock": "FINIS",
      "date_peremption": "2024-12-31",
      "marque": "Marque",
      "code_barre": 1234567890,
      "categorie": {
        "id": "uuid-de-la-categorie",
        "nom": "Nom de la Catégorie"
      },
      "stock": {
        "quantite_disponible": 50,
        "seuil_alerte": 10,
        "en_rupture": false
      },
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-15T14:30:00Z"
    }
  ]
}
```

## 🔐 Contrôle d'Accès

### Permissions par Rôle

- **ADMIN** : Accès à tous les magasins
- **RESPONSABLE_MAGASIN** : Accès uniquement à son magasin
- **RESPONSABLE_ENTREPOT** : Accès à tous les magasins
- **Autres rôles** : Accès refusé

### Codes de Réponse

- `200` : Succès
- `403` : Accès non autorisé
- `404` : Magasin non trouvé

## 📝 Exemples d'Utilisation

### 1. Récupérer tous les produits d'un magasin

```bash
GET /api/produits/magasin/12345678-1234-1234-1234-123456789012/produits/
```

### 2. Rechercher des produits par nom

```bash
GET /api/produits/magasin/12345678-1234-1234-1234-123456789012/produits/?search=laptop
```

### 3. Filtrer par catégorie

```bash
GET /api/produits/magasin/12345678-1234-1234-1234-123456789012/produits/?categorie=Électronique
```

### 4. Filtrer par type de stock

```bash
GET /api/produits/magasin/12345678-1234-1234-1234-123456789012/produits/?type_stock=FINIS
```

### 5. Combinaison de filtres

```bash
GET /api/produits/magasin/12345678-1234-1234-1234-123456789012/produits/?search=laptop&categorie=Électronique&type_stock=FINIS
```

## 🧪 Tests

### Test de la Méthode du Modèle

```python
# Dans le shell Django
from produit.models import Produit
from magasins.models import Magasin

# Récupérer un magasin
magasin = Magasin.objects.first()

# Tester la méthode
produits = Produit.getProductsByMagasin(magasin.id)
print(f"Nombre de produits: {produits.count()}")
```

### Test de l'API

```python
# Avec un client de test
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

client = APIClient()
# Authentification requise
response = client.get(f'/api/produits/magasin/{magasin_id}/produits/')
```

## 📁 Fichiers Modifiés

1. **`stock/produit/models.py`** : Ajout de la méthode `getProductsByMagasin`
2. **`stock/produit/views.py`** : Ajout de la classe `ProduitsParMagasinView`
3. **`stock/produit/urls.py`** : Ajout de l'URL pour le nouvel endpoint

## 🔄 Intégration avec l'Existant

Cette fonctionnalité s'intègre parfaitement avec :
- Le système de permissions existant
- Les modèles Produit, Magasin et Stock
- La documentation Swagger automatique
- Les filtres et recherches existants

## 📈 Avantages

1. **Performance** : Utilisation de `select_related` et `prefetch_related` pour optimiser les requêtes
2. **Sécurité** : Contrôle d'accès basé sur les rôles utilisateur
3. **Flexibilité** : Multiples options de filtrage
4. **Cohérence** : Suit les patterns existants de l'API
5. **Documentation** : Intégration automatique avec Swagger

## 🚀 Prochaines Étapes

1. Tester l'endpoint avec différents rôles d'utilisateur
2. Ajouter des tests unitaires complets
3. Optimiser les performances si nécessaire
4. Ajouter des métriques de monitoring
