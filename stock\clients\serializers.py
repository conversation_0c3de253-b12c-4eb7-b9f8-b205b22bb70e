from rest_framework import serializers
from .models import Client

class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = [
            'id_client', 'nom', 'prenom', 'telephone', 'email',
            'adresse', 'created_at', 'updated_at'
        ]

class ClientAvecVentesSerializer(serializers.ModelSerializer):
    total_achats = serializers.SerializerMethodField()
    
    class Meta:
        model = Client
        fields = ['id_client', 'nom', 'email', 'telephone', 'total_achats']
        
    def get_total_achats(self, obj):
        return obj.vente_set.count()