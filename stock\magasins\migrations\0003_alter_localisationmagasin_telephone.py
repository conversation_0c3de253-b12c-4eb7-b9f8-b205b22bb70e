# Generated by Django 5.2.4 on 2025-08-16 10:32

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('magasins', '0002_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='localisationmagasin',
            name='telephone',
            field=models.CharField(max_length=13, unique=True, validators=[django.core.validators.RegexValidator('^\\+261\\d{7,9}$', 'Le numéro doit être au format +261xxxxxxx')]),
        ),
    ]
