# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('achat_magasin', '0002_initial'),
        ('magasins', '0001_initial'),
        ('produit', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='achatmagasin',
            name='magasin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='achats', to='magasins.magasin'),
        ),
        migrations.AddField(
            model_name='achatmagasin',
            name='responsable',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='achats_crees', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='detailachatmagasin',
            name='achat',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details_achats', to='achat_magasin.achatmagasin'),
        ),
        migrations.AddField(
            model_name='detailachatmagasin',
            name='produit',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='produit.produit'),
        ),
        migrations.AddIndex(
            model_name='achatmagasin',
            index=models.Index(fields=['magasin'], name='achat_magas_magasin_d863f8_idx'),
        ),
        migrations.AddIndex(
            model_name='achatmagasin',
            index=models.Index(fields=['fournisseur'], name='achat_magas_fournis_b736cf_idx'),
        ),
        migrations.AddIndex(
            model_name='achatmagasin',
            index=models.Index(fields=['responsable'], name='achat_magas_respons_644511_idx'),
        ),
        migrations.AddIndex(
            model_name='achatmagasin',
            index=models.Index(fields=['statut'], name='achat_magas_statut_1963dd_idx'),
        ),
    ]
