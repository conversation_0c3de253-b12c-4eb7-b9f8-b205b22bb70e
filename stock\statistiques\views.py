from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Avg, F, ExpressionWrapper, DurationField, Q
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from .models import Statistique
from .serializers import StatistiqueSerializer, GenerateStatistiqueSerializer
from base.permissions import StatistiquePermissions, IsAdmin


def get_user_entreprise(user):
    """Récupère l'entreprise d'un utilisateur selon son rôle"""
    try:
        if user.role == 'ADMIN':
            # Pour les admins, via le UserProfile
            if hasattr(user, 'profile') and user.profile.entreprise:
                return user.profile.entreprise
        elif user.role == 'VENDEUR':
            # Pour les vendeurs, via le magasin
            if hasattr(user, 'vendeur') and user.vendeur.magasin:
                return user.vendeur.magasin.entreprise
        elif user.role == 'RESPONSABLE_MAGASIN':
            # Pour les responsables magasin, via le magasin
            if hasattr(user, 'responsable_magasin') and user.responsable_magasin.magasin:
                return user.responsable_magasin.magasin.entreprise
        elif user.role == 'RESPONSABLE_ENTREPOT':
            # Pour les responsables entrepôt, via l'entrepôt
            if hasattr(user, 'responsableentrepot') and user.responsableentrepot.entrepot:
                return user.responsableentrepot.entrepot.entreprise
    except Exception:
        pass
    return None


def get_user_magasin(user):
    """Récupère le magasin d'un utilisateur selon son rôle"""
    try:
        if user.role == 'VENDEUR':
            if hasattr(user, 'vendeur'):
                return user.vendeur.magasin
        elif user.role == 'RESPONSABLE_MAGASIN':
            if hasattr(user, 'responsable_magasin'):
                return user.responsable_magasin.magasin
    except Exception:
        pass
    return None
from magasins.models import Magasin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class StatistiqueViewSet(viewsets.ModelViewSet):
    serializer_class = StatistiqueSerializer
    permission_classes = [IsAuthenticated, StatistiquePermissions]
    
    def get_queryset(self):
        # Vérification pour la génération du schéma Swagger
        if getattr(self, 'swagger_fake_view', False):
            return Statistique.objects.none()

        user = self.request.user
        queryset = Statistique.objects.all()

        # Filtrage par entreprise
        user_entreprise = get_user_entreprise(user)
        user_magasin = get_user_magasin(user)

        if user_entreprise:
            queryset = queryset.filter(entreprise=user_entreprise)
        # Filtrage par magasin
        elif user_magasin:
            queryset = queryset.filter(magasin=user_magasin)

        # Filtrage par date
        date_debut = self.request.query_params.get('date_debut')
        date_fin = self.request.query_params.get('date_fin')
        if date_debut:
            queryset = queryset.filter(date_statistique__gte=date_debut)
        if date_fin:
            queryset = queryset.filter(date_statistique__lte=date_fin)
            
        # Filtrage par magasin/entrepot (uniquement pour l'entreprise)
        if user_entreprise:
            magasin_id = self.request.query_params.get('magasin_id')
            entrepot_id = self.request.query_params.get('entrepot_id')
            if magasin_id:
                queryset = queryset.filter(magasin_id=magasin_id)
            if entrepot_id:
                queryset = queryset.filter(entrepot_id=entrepot_id)
            
        # Filtrage par période
        periode = self.request.query_params.get('periode')
        if periode:
            queryset = queryset.filter(periode=periode)
            
        return queryset.order_by('-date_statistique', '-periode')
    
    @swagger_auto_schema(
        operation_description="Lister toutes les statistiques",
        responses={
            200: StatistiqueSerializer(many=True),
            400: "Bad Request"
        }
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Créer une nouvelle statistique",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['periode'],
            properties={
                'periode': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['JOUR', 'SEMAINE', 'MOIS', 'ANNEE']
                ),
                'magasin_id': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'entrepot_id': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID)
            }
        ),
        responses={
            201: openapi.Response(
                description="Statistique créée avec succès",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request"
        }
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'une statistique spécifique",
        responses={
            200: openapi.Response(
                description="Détails de la statistique",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Mettre à jour une statistique existante",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'periode': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['JOUR', 'SEMAINE', 'MOIS', 'ANNEE']
                ),
                'magasin_id': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID),
                'entrepot_id': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_UUID)
            }
        ),
        responses={
            200: openapi.Response(
                description="Statistique mise à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Supprimer une statistique",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated, IsAdmin])
    def generer(self, request):
        serializer = GenerateStatistiqueSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        magasin_id = None
        entrepot_id = None
        
        # Déterminer le contexte de génération
        user_entreprise = get_user_entreprise(user)
        user_magasin = get_user_magasin(user)

        if user_magasin:
            magasin_id = user_magasin.id
        elif user_entreprise:
            magasin_id = serializer.validated_data.get('magasin_id')
            entrepot_id = serializer.validated_data.get('entrepot_id')

        stats = Statistique.calculer_statistiques_completes(
            entreprise_id=user_entreprise.id if user_entreprise else user_magasin.entreprise.id,
            magasin_id=magasin_id,
            entrepot_id=entrepot_id,
            periode=serializer.validated_data.get('periode', 'JOUR')
        )
        
        return Response(
            self.get_serializer(stats).data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=False, methods=['get'])
    def rapport_consolide(self, request):
        queryset = self.get_queryset()
        user = request.user
        
        # Statistiques globales
        stats = queryset.aggregate(
            # Ventes
            total_ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            montant_ht=Sum('montant_ht'),
            montant_tva=Sum('montant_tva'),
            # Financier
            marge_brute=Sum('marge_brute'),
            benefice_net=Sum('benefice_net'),
            # Clients
            nouveaux_clients=Sum('nouveaux_clients'),
            clients_actifs=Sum('clients_actifs'),
            panier_moyen=Avg('panier_moyen'),
            # Facturation
            factures_emises=Sum('factures_emises'),
            factures_payees=Sum('factures_payees'),
            montant_impaye=Sum('montant_impaye'),
            # Stock
            valeur_stock=Sum('valeur_stock'),
            # Performance
            taux_retour=Avg('taux_retour'),
            satisfaction_client=Avg('satisfaction_client')
        )
        
        # Calcul des taux
        if stats['chiffre_affaire']:
            stats['taux_marge'] = (stats['marge_brute'] / stats['chiffre_affaire'] * 100)
        if stats['clients_actifs']:
            stats['taux_fidelite'] = (stats['clients_actifs'] / queryset.count() * 100)
        
        # Statistiques par jour
        stats_par_jour = queryset.values('date_statistique').annotate(
            total_ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients'),
            marge_brute=Sum('marge_brute')
        ).order_by('date_statistique')
        
        # Statistiques par magasin (uniquement pour l'entreprise)
        stats_par_magasin = None
        user_entreprise = get_user_entreprise(user)
        if user_entreprise:
            stats_par_magasin = queryset.values('magasin__nom').annotate(
                total_ventes=Sum('total_ventes'),
                chiffre_affaire=Sum('chiffre_affaire'),
                marge_brute=Sum('marge_brute'),
                clients_actifs=Sum('clients_actifs')
            ).order_by('-chiffre_affaire')
        
        response_data = {
            'statistiques_globales': stats,
            'statistiques_par_jour': stats_par_jour
        }
        
        if stats_par_magasin:
            response_data['statistiques_par_magasin'] = stats_par_magasin
        
        return Response(response_data)
    
    @action(detail=False, methods=['get'])
    def produits_populaires(self, request):
        queryset = self.get_queryset()
        produits = {}
        
        for stat in queryset:
            for produit in stat.produits_populaires:
                if produit['produit__id'] not in produits:
                    produits[produit['produit__id']] = {
                        'nom': produit['produit__nom'],
                        'quantite_totale': 0,
                        'marge_totale': 0,
                        'magasins': set()
                    }
                produits[produit['produit__id']]['quantite_totale'] += produit['quantite']
                produits[produit['produit__id']]['marge_totale'] += produit['marge']
                if stat.magasin:
                    produits[produit['produit__id']]['magasins'].add(stat.magasin.nom)
        
        # Trier par quantité totale
        produits_tries = sorted(
            produits.items(),
            key=lambda x: x[1]['quantite_totale'],
            reverse=True
        )[:10]
        
        return Response([{
            'id': id,
            'nom': data['nom'],
            'quantite_totale': data['quantite_totale'],
            'marge_totale': data['marge_totale'],
            'marge_unitaire': data['marge_totale'] / data['quantite_totale'] if data['quantite_totale'] else 0,
            'magasins': list(data['magasins'])
        } for id, data in produits_tries])
    
    @action(detail=False, methods=['get'])
    def alertes_stock(self, request):
        queryset = self.get_queryset()
        alertes = {}
        
        for stat in queryset:
            for produit in stat.produits_en_alerte:
                if produit['produit__id'] not in alertes:
                    alertes[produit['produit__id']] = {
                        'nom': produit['produit__nom'],
                        'quantite': produit['quantite_disponible'],
                        'seuil': produit['seuil_alerte'],
                        'niveau_alerte': 'CRITIQUE' if produit['quantite_disponible'] == 0 else 'ALERTE',
                        'magasin': stat.magasin.nom if stat.magasin else None
                    }
        
        return Response(list(alertes.values()))
    
    @action(detail=False, methods=['get'])
    def performance(self, request):
        queryset = self.get_queryset()
        user = request.user
        
        # Performance globale
        performance = queryset.aggregate(
            temps_traitement_moyen=Avg('temps_traitement_moyen'),
            taux_retour=Avg('taux_retour'),
            satisfaction_client=Avg('satisfaction_client'),
            delai_paiement_moyen=Avg('delai_paiement_moyen')
        )
        
        # Performance par jour
        performance_par_jour = queryset.values('date_statistique').annotate(
            temps_traitement=Avg('temps_traitement_moyen'),
            taux_retour=Avg('taux_retour'),
            satisfaction=Avg('satisfaction_client')
        ).order_by('date_statistique')
        
        # Performance par magasin (uniquement pour l'entreprise)
        performance_par_magasin = None
        user_entreprise = get_user_entreprise(user)
        if user_entreprise:
            performance_par_magasin = queryset.values('magasin__nom').annotate(
                temps_traitement=Avg('temps_traitement_moyen'),
                taux_retour=Avg('taux_retour'),
                satisfaction=Avg('satisfaction_client')
            ).order_by('-satisfaction')
        
        response_data = {
            'performance_globale': performance,
            'performance_par_jour': performance_par_jour
        }
        
        if performance_par_magasin:
            response_data['performance_par_magasin'] = performance_par_magasin
        
        return Response(response_data)
    
    @action(detail=False, methods=['get'])
    def tendances(self, request):
        queryset = self.get_queryset()
        user = request.user
        aujourd_hui = timezone.now().date()
        
        # Statistiques de la période actuelle
        periode_actuelle = queryset.filter(
            date_statistique__gte=aujourd_hui - timedelta(days=30)
        ).aggregate(
            ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients')
        )
        
        # Statistiques de la période précédente
        periode_precedente = queryset.filter(
            date_statistique__gte=aujourd_hui - timedelta(days=60),
            date_statistique__lt=aujourd_hui - timedelta(days=30)
        ).aggregate(
            ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients')
        )
        
        # Calcul des variations
        variations = {}
        for key in ['ventes', 'chiffre_affaire', 'nouveaux_clients']:
            actuel = periode_actuelle[key] or 0
            precedent = periode_precedente[key] or 0
            if precedent:
                variations[key] = ((actuel - precedent) / precedent * 100)
            else:
                variations[key] = 100 if actuel > 0 else 0
        
        # Tendances par magasin (uniquement pour l'entreprise)
        tendances_par_magasin = None
        user_entreprise = get_user_entreprise(user)
        if user_entreprise:
            tendances_par_magasin = queryset.values('magasin__nom').annotate(
                variation_ventes=(
                    Sum('total_ventes', filter=Q(date_statistique__gte=aujourd_hui - timedelta(days=30))) -
                    Sum('total_ventes', filter=Q(
                        date_statistique__gte=aujourd_hui - timedelta(days=60),
                        date_statistique__lt=aujourd_hui - timedelta(days=30)
                    ))
                ) * 100.0 / Sum('total_ventes', filter=Q(
                    date_statistique__gte=aujourd_hui - timedelta(days=60),
                    date_statistique__lt=aujourd_hui - timedelta(days=30)
                ))
            ).order_by('-variation_ventes')
        
        response_data = {
            'periode_actuelle': periode_actuelle,
            'periode_precedente': periode_precedente,
            'variations': variations
        }
        
        if tendances_par_magasin:
            response_data['tendances_par_magasin'] = tendances_par_magasin
        
        return Response(response_data)
    
    @swagger_auto_schema(
        operation_description="Obtenir les statistiques détaillées d'un magasin",
        responses={
            200: openapi.Response(
                description="Statistiques détaillées du magasin",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'stats_generales': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'total_ventes': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'chiffre_affaire': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'montant_ht': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'montant_tva': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'marge_brute': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'benefice_net': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'nouveaux_clients': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'clients_actifs': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'panier_moyen': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'factures_emises': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'factures_payees': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'montant_impaye': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'valeur_stock': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'taux_retour': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'satisfaction_client': openapi.Schema(type=openapi.TYPE_NUMBER)
                            }
                        ),
                        'stats_par_jour': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'date_statistique': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                                    'total_ventes': openapi.Schema(type=openapi.TYPE_NUMBER),
                                    'chiffre_affaire': openapi.Schema(type=openapi.TYPE_NUMBER),
                                    'nouveaux_clients': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'marge_brute': openapi.Schema(type=openapi.TYPE_NUMBER)
                                }
                            )
                        ),
                        'produits_populaires': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'nom': openapi.Schema(type=openapi.TYPE_STRING),
                                    'quantite_totale': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'marge_totale': openapi.Schema(type=openapi.TYPE_NUMBER)
                                }
                            )
                        )
                    }
                )
            ),
            400: "Bad Request",
            403: "Forbidden"
        }
    )
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def magasin_detail(self, request):
        """
        Endpoint pour obtenir les statistiques détaillées d'un magasin spécifique.
        Accessible uniquement par l'entreprise.
        """
        magasin_id = request.query_params.get('magasin_id')
        if not magasin_id:
            return Response(
                {'error': 'Le paramètre magasin_id est requis'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            magasin = Magasin.objects.get(id=magasin_id, entreprise=request.user.entreprise)
        except Magasin.DoesNotExist:
            return Response(
                {'error': 'Magasin non trouvé'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Période de temps
        date_debut = request.query_params.get('date_debut')
        date_fin = request.query_params.get('date_fin')
        if not date_debut:
            date_debut = timezone.now().date() - timedelta(days=30)
        if not date_fin:
            date_fin = timezone.now().date()

        queryset = self.get_queryset().filter(
            magasin=magasin,
            date_statistique__gte=date_debut,
            date_statistique__lte=date_fin
        )

        # Statistiques générales du magasin
        stats_generales = queryset.aggregate(
            total_ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            montant_ht=Sum('montant_ht'),
            montant_tva=Sum('montant_tva'),
            marge_brute=Sum('marge_brute'),
            benefice_net=Sum('benefice_net'),
            nouveaux_clients=Sum('nouveaux_clients'),
            clients_actifs=Sum('clients_actifs'),
            panier_moyen=Avg('panier_moyen'),
            factures_emises=Sum('factures_emises'),
            factures_payees=Sum('factures_payees'),
            montant_impaye=Sum('montant_impaye'),
            valeur_stock=Sum('valeur_stock'),
            taux_retour=Avg('taux_retour'),
            satisfaction_client=Avg('satisfaction_client')
        )

        # Statistiques par jour
        stats_par_jour = queryset.values('date_statistique').annotate(
            total_ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients'),
            marge_brute=Sum('marge_brute')
        ).order_by('date_statistique')

        # Produits les plus vendus
        produits_populaires = {}
        for stat in queryset:
            for produit in stat.produits_populaires:
                if produit['produit__id'] not in produits_populaires:
                    produits_populaires[produit['produit__id']] = {
                        'nom': produit['produit__nom'],
                        'quantite_totale': 0,
                        'marge_totale': 0
                    }
                produits_populaires[produit['produit__id']]['quantite_totale'] += produit['quantite']
                produits_populaires[produit['produit__id']]['marge_totale'] += produit['marge']

        produits_tries = sorted(
            produits_populaires.items(),
            key=lambda x: x[1]['quantite_totale'],
            reverse=True
        )[:10]

        # Alertes de stock
        alertes_stock = {}
        for stat in queryset:
            for produit in stat.produits_en_alerte:
                if produit['produit__id'] not in alertes_stock:
                    alertes_stock[produit['produit__id']] = {
                        'nom': produit['produit__nom'],
                        'quantite': produit['quantite_disponible'],
                        'seuil': produit['seuil_alerte'],
                        'niveau_alerte': 'CRITIQUE' if produit['quantite_disponible'] == 0 else 'ALERTE'
                    }

        # Performance
        performance = queryset.aggregate(
            temps_traitement_moyen=Avg('temps_traitement_moyen'),
            taux_retour=Avg('taux_retour'),
            satisfaction_client=Avg('satisfaction_client'),
            delai_paiement_moyen=Avg('delai_paiement_moyen')
        )

        # Tendances
        periode_actuelle = queryset.filter(
            date_statistique__gte=timezone.now().date() - timedelta(days=30)
        ).aggregate(
            ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients')
        )

        periode_precedente = queryset.filter(
            date_statistique__gte=timezone.now().date() - timedelta(days=60),
            date_statistique__lt=timezone.now().date() - timedelta(days=30)
        ).aggregate(
            ventes=Sum('total_ventes'),
            chiffre_affaire=Sum('chiffre_affaire'),
            nouveaux_clients=Sum('nouveaux_clients')
        )

        variations = {}
        for key in ['ventes', 'chiffre_affaire', 'nouveaux_clients']:
            actuel = periode_actuelle[key] or 0
            precedent = periode_precedente[key] or 0
            if precedent:
                variations[key] = ((actuel - precedent) / precedent * 100)
            else:
                variations[key] = 100 if actuel > 0 else 0

        return Response({
            'magasin': {
                'id': magasin.id,
                'nom': magasin.nom,
                'adresse': magasin.adresse
            },
            'periode': {
                'debut': date_debut,
                'fin': date_fin
            },
            'stats_generales': stats_generales,
            'stats_par_jour': list(stats_par_jour),
            'produits_populaires': [{
                'id': id,
                'nom': data['nom'],
                'quantite_totale': data['quantite_totale'],
                'marge_totale': data['marge_totale'],
                'marge_unitaire': data['marge_totale'] / data['quantite_totale'] if data['quantite_totale'] else 0
            } for id, data in produits_tries],
            'alertes_stock': list(alertes_stock.values()),
            'performance': performance,
            'tendances': {
                'periode_actuelle': periode_actuelle,
                'periode_precedente': periode_precedente,
                'variations': variations
            }
        })
    
            
        
    
