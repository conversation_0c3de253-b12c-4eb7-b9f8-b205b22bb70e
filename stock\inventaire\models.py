import uuid
from django.db import models
from django.core.exceptions import ValidationError
from entrepot.models import Entrepot
from base.models import User
from produit.models import Produit
from stock_Pme.models import Stock
from django.utils import timezone
from django.db.models import Sum, F, Q

class Inventaire(models.Model):
    TYPE_INVENTAIRE_CHOICES = [
        ('COMPLET', 'Inventaire complet'),
        ('PARTIEL', 'Inventaire partiel'),
        ('CYCLIQUE', 'Inventaire cyclique'),
        ('ALERTE', 'Inventaire suite à alerte')
    ]
    
    STATUT_CHOICES = [
        ('PLANIFIE', 'Planifié'),
        ('EN_COURS', 'En cours'),
        ('TERMINE', 'Terminé'),
        ('ANNULE', 'Annulé')
    ]

    id_inventaire = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    numero_inventaire = models.CharField(max_length=50, unique=True)
    type_inventaire = models.CharField(max_length=20, choices=TYPE_INVENTAIRE_CHOICES, default='COMPLET')
    date_planification = models.DateField()
    date_debut = models.DateTimeField(null=True, blank=True)
    date_fin = models.DateTimeField(null=True, blank=True)
    statut = models.CharField(max_length=20, choices=STATUT_CHOICES, default='PLANIFIE')
    magasin = models.ForeignKey('magasins.Magasin', on_delete=models.CASCADE, related_name='inventaires')
    entrepot = models.ForeignKey(Entrepot, on_delete=models.CASCADE, related_name='inventaires')
    responsable = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='inventaires')
    commentaire = models.TextField(blank=True)
    tolerance_ecart = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, 
                                        help_text="Tolérance d'écart en pourcentage")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Inventaire"
        verbose_name_plural = "Inventaires"
        indexes = [
            models.Index(fields=['date_planification']),
            models.Index(fields=['magasin']),
            models.Index(fields=['entrepot']),
            models.Index(fields=['statut']),
            models.Index(fields=['type_inventaire']),
        ]
        ordering = ['-date_planification']

    def __str__(self):
        return f"Inventaire {self.numero_inventaire} - {self.magasin.nom}"

    def clean(self):
        # Vérifier que l'entrepôt correspond au stock du magasin
        stocks = Stock.objects.filter(magasin=self.magasin, entrepot=self.entrepot)
        if not stocks.exists():
            raise ValidationError("Aucun stock trouvé pour ce magasin et cet entrepôt.")
        
        if self.responsable and self.responsable.role not in ['ADMIN', 'RESPONSABLE']:
            raise ValidationError("Le responsable doit être un ADMIN ou RESPONSABLE.")
        if self.date_fin and self.date_debut and self.date_fin < self.date_debut:
            raise ValidationError("La date de fin ne peut pas être antérieure à la date de début.")

    def save(self, *args, **kwargs):
        if not self.numero_inventaire:
            self.numero_inventaire = self.generer_numero_inventaire()
        super().save(*args, **kwargs)

    def generer_numero_inventaire(self):
        """Génère un numéro d'inventaire unique"""
        date = timezone.now().strftime('%Y%m')
        dernier_numero = self.__class__.objects.filter(
            numero_inventaire__startswith=f'INV-{date}'
        ).order_by('-numero_inventaire').first()
        
        if dernier_numero:
            numero = int(dernier_numero.numero_inventaire.split('-')[-1]) + 1
        else:
            numero = 1
            
        return f'INV-{date}-{numero:04d}'

    def get_stocks_a_inventorier(self):
        """Récupère tous les stocks à inventorier pour ce magasin et cet entrepôt"""
        return Stock.objects.filter(
            magasin=self.magasin,
            entrepot=self.entrepot
        ).select_related('produit', 'location')

    def initialiser_details_inventaire(self):
        """Initialise les détails d'inventaire à partir des stocks existants"""
        stocks = self.get_stocks_a_inventorier()
        for stock in stocks:
            DetailInventaire.objects.get_or_create(
                inventaire=self,
                produit=stock.produit,
                defaults={
                    'quantite_theorique': stock.quantite_disponible,
                    'quantite_reelle': 0,
                    'statut': False
                }
            )
        return self.details.all()

    @classmethod
    def demarrerInventaire(cls, id_entrepot, id_magasin, id_responsable, type_inventaire='COMPLET'):
        entrepot = Entrepot.objects.get(id_entrepot=id_entrepot)
        from magasins.models import Magasin
        magasin = Magasin.objects.get(id_magasin=id_magasin)
        responsable = User.objects.get(id_utilisateur=id_responsable)
        
        # Vérifier qu'il y a des stocks à inventorier
        stocks = Stock.objects.filter(magasin=magasin, entrepot=entrepot)
        if not stocks.exists():
            raise ValidationError("Aucun stock trouvé pour ce magasin et cet entrepôt.")
        
        inventaire = cls.objects.create(
            entrepot=entrepot,
            magasin=magasin,
            responsable=responsable,
            type_inventaire=type_inventaire,
            date_planification=timezone.now().date(),
            date_debut=timezone.now()
        )
        
        # Initialiser les détails d'inventaire
        inventaire.initialiser_details_inventaire()
        
        return inventaire

    def terminerInventaire(self):
        if self.statut == 'TERMINE':
            raise ValidationError("L'inventaire est déjà terminé.")
        
        self.statut = 'TERMINE'
        self.date_fin = timezone.now()
        self.save()
        
        # Mettre à jour les stocks avec les quantités réelles
        for detail in self.details.all():
            stock = Stock.objects.filter(
                produit=detail.produit, 
                entrepot=self.entrepot, 
                magasin=self.magasin
            ).first()
            
            if stock:
                stock.quantite = detail.quantite_reelle
                stock.save()
                
                # Enregistrer l'historique des mouvements
                from stock_Pme.models import MouvementStock
                MouvementStock.objects.create(
                    stock=stock,
                    type_mouvement='INVENTAIRE',
                    quantite=detail.quantite_reelle - detail.quantite_theorique,
                    reference=self.numero_inventaire,
                    commentaire=f"Ajustement suite à l'inventaire {self.numero_inventaire}"
                )
        
        return self

    def annulerInventaire(self):
        if self.statut == 'TERMINE':
            raise ValidationError("Impossible d'annuler un inventaire terminé.")
        self.statut = 'ANNULE'
        self.save()
        return self

    def ajouterProduitInventaire(self, id_produit, quantite_comptée):
        produit = Produit.objects.get(id=id_produit)
        stock = Stock.objects.filter(
            produit=produit, 
            entrepot=self.entrepot, 
            magasin=self.magasin
        ).first()
        
        quantite_theorique = stock.quantite if stock else 0
        
        detail = DetailInventaire.objects.create(
            inventaire=self,
            produit=produit,
            quantite_theorique=quantite_theorique,
            quantite_reelle=quantite_comptée,
            statut=True
        )
        return detail

    def calculerEcarts(self):
        ecarts = []
        for detail in self.details.all():
            ecart = detail.quantite_reelle - detail.quantite_theorique
            ecart_pourcentage = (ecart / detail.quantite_theorique * 100) if detail.quantite_theorique > 0 else 0
            
            ecarts.append({
                'produit': detail.produit.nom,
                'reference': detail.produit.reference,
                'quantite_theorique': detail.quantite_theorique,
                'quantite_reelle': detail.quantite_reelle,
                'ecart': ecart,
                'ecart_pourcentage': ecart_pourcentage,
                'alerte': abs(ecart_pourcentage) > self.tolerance_ecart
            })
        return ecarts

    def genererRapportInventaire(self):
        details = self.details.all()
        total_produits = details.count()
        produits_avec_ecart = details.filter(
            ~Q(quantite_reelle=F('quantite_theorique'))
        ).count()
        
        return {
            'id_inventaire': str(self.id_inventaire),
            'numero_inventaire': self.numero_inventaire,
            'type_inventaire': self.get_type_inventaire_display(),
            'date_planification': self.date_planification,
            'date_debut': self.date_debut,
            'date_fin': self.date_fin,
            'magasin': self.magasin.nom,
            'entrepot': self.entrepot.nom,
            'responsable': self.responsable.get_full_name() if self.responsable else 'N/A',
            'statut': self.get_statut_display(),
            'total_produits': total_produits,
            'produits_avec_ecart': produits_avec_ecart,
            'taux_precision': ((total_produits - produits_avec_ecart) / total_produits * 100) if total_produits > 0 else 0,
            'details': [
                {
                    'produit': detail.produit.nom,
                    'reference': detail.produit.reference,
                    'quantite_theorique': detail.quantite_theorique,
                    'quantite_reelle': detail.quantite_reelle,
                    'ecart': detail.quantite_reelle - detail.quantite_theorique,
                    'ecart_pourcentage': (detail.quantite_reelle - detail.quantite_theorique) / detail.quantite_theorique * 100 if detail.quantite_theorique > 0 else 0
                } for detail in details
            ]
        }

    def verifier_progression(self):
        """Vérifie la progression de l'inventaire"""
        total_produits = self.magasin.produits.count()
        produits_inventories = self.details.count()
        
        return {
            'total_produits': total_produits,
            'produits_inventories': produits_inventories,
            'pourcentage_complete': (produits_inventories / total_produits * 100) if total_produits > 0 else 0
        }

class DetailInventaire(models.Model):
    id_detail = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inventaire = models.ForeignKey(Inventaire, on_delete=models.CASCADE, related_name='details')
    produit = models.ForeignKey(Produit, on_delete=models.CASCADE)
    quantite_theorique = models.IntegerField()
    quantite_reelle = models.IntegerField()
    statut = models.BooleanField(default=True)
    commentaire = models.TextField(blank=True)
    date_comptage = models.DateTimeField(auto_now_add=True)
    compteur = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='comptages')

    class Meta:
        verbose_name = "Détail Inventaire"
        verbose_name_plural = "Détails Inventaires"
        indexes = [
            models.Index(fields=['produit']),
            models.Index(fields=['date_comptage']),
        ]
        unique_together = ['inventaire', 'produit']

    def __str__(self):
        return f"Détail {self.id_detail} - {self.produit.nom}"

    def clean(self):
        if self.quantite_reelle < 0:
            raise ValidationError("La quantité réelle ne peut pas être négative.")
        if self.quantite_theorique < 0:
            raise ValidationError("La quantité théorique ne peut pas être négative.")

    @classmethod
    def ajouterProduitInventaire(cls, id_inventaire, id_produit, quantite_comptée, compteur=None):
        inventaire = Inventaire.objects.get(id_inventaire=id_inventaire)
        return inventaire.ajouterProduitInventaire(id_produit, quantite_comptée)

    def mettreAJourQuantite(self, quantite_comptée, compteur=None):
        if self.inventaire.statut == 'TERMINE':
            raise ValidationError("Impossible de modifier un inventaire terminé.")
        
        self.quantite_reelle = quantite_comptée
        if compteur:
            self.compteur = compteur
        self.save()
        return self