from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from .models import NotificationType, NotificationPreference, Notification, NotificationBatch
from .services import NotificationService, EmailNotificationService
from entreprise.models import Entreprise

User = get_user_model()


class NotificationTypeModelTest(TestCase):
    """Tests pour le modèle NotificationType"""
    
    def setUp(self):
        self.notification_type = NotificationType.objects.create(
            nom='TEST_NOTIFICATION',
            description='Notification de test',
            categorie='SYSTEM',
            priorite_defaut='MEDIUM',
            template_email='<p>Test email: {{ message }}</p>',
            template_sms='Test SMS: {{ message }}',
            actif=True
        )
    
    def test_creation_type_notification(self):
        """Test la création d'un type de notification"""
        self.assertEqual(self.notification_type.nom, 'TEST_NOTIFICATION')
        self.assertEqual(self.notification_type.categorie, 'SYSTEM')
        self.assertTrue(self.notification_type.actif)
    
    def test_str_representation(self):
        """Test la représentation string du type de notification"""
        expected = "TEST_NOTIFICATION (Système)"
        self.assertEqual(str(self.notification_type), expected)


class NotificationPreferenceModelTest(TestCase):
    """Tests pour le modèle NotificationPreference"""
    
    def setUp(self):
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )
        self.user = User.objects.create_user(
            username='testuser',
            telephone='1234567890',
            nom='Test',
            prenom='User',
            adresse='Test Address',
            role='ADMIN'
        )
        self.notification_type = NotificationType.objects.create(
            nom='TEST_NOTIFICATION',
            description='Test',
            categorie='STOCK',
            priorite_defaut='MEDIUM'
        )
        self.preferences = NotificationPreference.objects.create(
            user=self.user,
            email_enabled=True,
            stock_notifications=True,
            quiet_hours_start='22:00',
            quiet_hours_end='08:00'
        )
    
    def test_creation_preferences(self):
        """Test la création des préférences"""
        self.assertEqual(self.preferences.user, self.user)
        self.assertTrue(self.preferences.email_enabled)
        self.assertTrue(self.preferences.stock_notifications)
    
    def test_is_notification_allowed_category(self):
        """Test la vérification des notifications par catégorie"""
        # Notification autorisée
        self.assertTrue(
            self.preferences.is_notification_allowed(self.notification_type)
        )
        
        # Désactiver les notifications de stock
        self.preferences.stock_notifications = False
        self.preferences.save()
        
        self.assertFalse(
            self.preferences.is_notification_allowed(self.notification_type)
        )
    
    def test_is_notification_allowed_quiet_hours(self):
        """Test la vérification des heures silencieuses"""
        # Simuler une heure dans les heures silencieuses (23:00)
        quiet_time = timezone.now().replace(hour=23, minute=0, second=0, microsecond=0)
        
        self.assertFalse(
            self.preferences.is_notification_allowed(self.notification_type, quiet_time)
        )
        
        # Simuler une heure normale (14:00)
        normal_time = timezone.now().replace(hour=14, minute=0, second=0, microsecond=0)
        
        self.assertTrue(
            self.preferences.is_notification_allowed(self.notification_type, normal_time)
        )


class NotificationModelTest(TestCase):
    """Tests pour le modèle Notification"""
    
    def setUp(self):
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )
        self.user = User.objects.create_user(
            username='testuser',
            telephone='1234567890',
            nom='Test',
            prenom='User',
            adresse='Test Address',
            role='ADMIN'
        )
        self.notification_type = NotificationType.objects.create(
            nom='TEST_NOTIFICATION',
            description='Test',
            categorie='SYSTEM',
            priorite_defaut='MEDIUM'
        )
        self.notification = Notification.objects.create(
            type_notification=self.notification_type,
            destinataire=self.user,
            titre='Test Notification',
            message='Ceci est un test',
            canal='IN_APP',
            priorite='MEDIUM'
        )
    
    def test_creation_notification(self):
        """Test la création d'une notification"""
        self.assertEqual(self.notification.titre, 'Test Notification')
        self.assertEqual(self.notification.destinataire, self.user)
        self.assertEqual(self.notification.statut, 'PENDING')
        self.assertFalse(self.notification.est_lue)
    
    def test_marquer_comme_lue(self):
        """Test marquer une notification comme lue"""
        # D'abord marquer comme envoyée
        self.notification.marquer_comme_envoyee()
        
        # Puis marquer comme lue
        self.notification.marquer_comme_lue()
        
        self.assertEqual(self.notification.statut, 'READ')
        self.assertTrue(self.notification.est_lue)
        self.assertIsNotNone(self.notification.date_lecture)
    
    def test_marquer_comme_echouee(self):
        """Test marquer une notification comme échouée"""
        erreur = "Erreur de test"
        self.notification.marquer_comme_echouee(erreur)
        
        self.assertEqual(self.notification.statut, 'FAILED')
        self.assertEqual(self.notification.erreur_envoi, erreur)
        self.assertEqual(self.notification.tentatives_envoi, 1)


class NotificationServiceTest(TestCase):
    """Tests pour le service de notifications"""
    
    def setUp(self):
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )
        self.user = User.objects.create_user(
            username='testuser',
            telephone='1234567890',
            nom='Test',
            prenom='User',
            adresse='Test Address',
            role='ADMIN',
            email='<EMAIL>'
        )
        self.notification_type = NotificationType.objects.create(
            nom='TEST_NOTIFICATION',
            description='Test',
            categorie='SYSTEM',
            priorite_defaut='MEDIUM'
        )
        # Créer les préférences par défaut
        NotificationPreference.objects.create(user=self.user)
    
    def test_creer_notification(self):
        """Test la création d'une notification via le service"""
        notification = NotificationService.creer_notification(
            type_notification='TEST_NOTIFICATION',
            destinataire=self.user,
            titre='Test Service',
            message='Message de test',
            canal='EMAIL'
        )
        
        self.assertIsNotNone(notification)
        self.assertEqual(notification.titre, 'Test Service')
        self.assertEqual(notification.canal, 'EMAIL')
        self.assertEqual(notification.statut, 'PENDING')
    
    def test_creer_notification_type_inexistant(self):
        """Test la création avec un type inexistant"""
        with self.assertRaises(ValueError):
            NotificationService.creer_notification(
                type_notification='TYPE_INEXISTANT',
                destinataire=self.user,
                titre='Test',
                message='Test'
            )
    
    @patch('notifications.services.EmailNotificationService.envoyer_email')
    def test_envoyer_notification_email(self, mock_envoyer_email):
        """Test l'envoi d'une notification email"""
        mock_envoyer_email.return_value = True
        
        notification = NotificationService.creer_notification(
            type_notification='TEST_NOTIFICATION',
            destinataire=self.user,
            titre='Test Email',
            message='Test message',
            canal='EMAIL'
        )
        
        success = NotificationService.envoyer_notification(notification)
        
        self.assertTrue(success)
        mock_envoyer_email.assert_called_once_with(notification)
        notification.refresh_from_db()
        self.assertEqual(notification.statut, 'SENT')
    
    def test_creer_et_envoyer_notification_multiple(self):
        """Test la création et envoi à plusieurs destinataires"""
        user2 = User.objects.create_user(
            username='testuser2',
            telephone='1234567891',
            nom='Test2',
            prenom='User2',
            adresse='Test Address 2',
            role='VENDEUR',
            email='<EMAIL>'
        )
        NotificationPreference.objects.create(user=user2)
        
        notifications = NotificationService.creer_et_envoyer_notification(
            type_notification='TEST_NOTIFICATION',
            destinataires=[self.user, user2],
            titre='Test Multiple',
            message='Message pour tous',
            envoyer_immediatement=False
        )
        
        self.assertEqual(len(notifications), 2)
        self.assertEqual(notifications[0].destinataire, self.user)
        self.assertEqual(notifications[1].destinataire, user2)


class NotificationAPITest(APITestCase):
    """Tests pour l'API des notifications"""
    
    def setUp(self):
        self.entreprise = Entreprise.objects.create(
            nom="Test Entreprise",
            adresse="123 Test Street",
            nif="1234567890123"
        )
        self.user = User.objects.create_user(
            username='testuser',
            telephone='1234567890',
            nom='Test',
            prenom='User',
            adresse='Test Address',
            role='ADMIN',
            email='<EMAIL>'
        )
        self.notification_type = NotificationType.objects.create(
            nom='TEST_NOTIFICATION',
            description='Test',
            categorie='SYSTEM',
            priorite_defaut='MEDIUM'
        )
        self.preferences = NotificationPreference.objects.create(user=self.user)
        
        # Authentifier l'utilisateur
        self.client.force_authenticate(user=self.user)
    
    def test_get_preferences(self):
        """Test récupération des préférences"""
        url = '/api/notifications/preferences/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user'], self.user.id_utilisateur)
    
    def test_update_preferences(self):
        """Test mise à jour des préférences"""
        url = '/api/notifications/preferences/'
        data = {
            'email_enabled': False,
            'sms_enabled': True,
            'stock_notifications': False
        }
        response = self.client.put(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.preferences.refresh_from_db()
        self.assertFalse(self.preferences.email_enabled)
        self.assertTrue(self.preferences.sms_enabled)
        self.assertFalse(self.preferences.stock_notifications)
    
    def test_list_notifications(self):
        """Test liste des notifications"""
        # Créer quelques notifications
        for i in range(3):
            Notification.objects.create(
                type_notification=self.notification_type,
                destinataire=self.user,
                titre=f'Test {i}',
                message=f'Message {i}',
                canal='IN_APP',
                priorite='MEDIUM'
            )
        
        url = '/api/notifications/notifications/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)
    
    def test_marquer_notification_comme_lue(self):
        """Test marquer une notification comme lue"""
        notification = Notification.objects.create(
            type_notification=self.notification_type,
            destinataire=self.user,
            titre='Test',
            message='Message',
            canal='IN_APP',
            priorite='MEDIUM',
            statut='SENT'
        )
        
        url = f'/api/notifications/notifications/{notification.id}/marquer_comme_lue/'
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        notification.refresh_from_db()
        self.assertEqual(notification.statut, 'READ')
    
    def test_statistiques_notifications(self):
        """Test récupération des statistiques"""
        # Créer des notifications avec différents statuts
        Notification.objects.create(
            type_notification=self.notification_type,
            destinataire=self.user,
            titre='Test 1',
            message='Message 1',
            canal='EMAIL',
            priorite='HIGH',
            statut='SENT'
        )
        Notification.objects.create(
            type_notification=self.notification_type,
            destinataire=self.user,
            titre='Test 2',
            message='Message 2',
            canal='SMS',
            priorite='LOW',
            statut='READ'
        )
        
        url = '/api/notifications/notifications/statistiques/'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_notifications'], 2)
        self.assertEqual(response.data['notifications_non_lues'], 1)
        self.assertIn('EMAIL', response.data['notifications_par_canal'])
        self.assertIn('SMS', response.data['notifications_par_canal'])
