from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Vendeur
from .serializers import VendeurSerializer, VendeurCreateSerializer
from base.views import IsAdmin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class VendeurListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Lister tous les vendeurs",
        responses={
            200: openapi.Response(
                description="Liste des vendeurs",
                schema=openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            ),
            400: "Bad Request"
        }
    ) 
    def get(self, request):
        vendeurs = Vendeur.objects.all()
        serializer = VendeurSerializer(vendeurs, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Créer un nouveau vendeur",
        request_body= VendeurCreateSerializer,
        responses={
            201: openapi.Response(
                description="Vendeur créé avec succès",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'vendeur': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            400: "Bad Request - Données invalides ou utilisateur déjà existant"
        }
    )
    def post(self, request):
        serializer = VendeurCreateSerializer(data=request.data)
        if serializer.is_valid():
            vendeur = serializer.save()
            response_serializer = VendeurSerializer(vendeur)
            return Response({
                'vendeur': response_serializer.data,
                'message': 'Vendeur créé avec succès'
            }, status=status.HTTP_201_CREATED)
        return Response({
            'errors': serializer.errors,
            'message': 'Erreur lors de la création du vendeur'
        }, status=status.HTTP_400_BAD_REQUEST)

class VendeurDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer les détails d'un vendeur spécifique",
        responses={
            200: openapi.Response(
                description="Détails du vendeur",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            404: "Not Found"
        }
    )
    def get(self, request, id):
        try:
            vendeur = Vendeur.objects.get(id_vendeur=id)
            serializer = VendeurSerializer(vendeur)
            return Response(serializer.data)
        except Vendeur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Mettre à jour un vendeur existant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'nom': openapi.Schema(type=openapi.TYPE_STRING),
                        'prenom': openapi.Schema(type=openapi.TYPE_STRING),
                        'email': openapi.Schema(type=openapi.TYPE_STRING),
                        'telephone': openapi.Schema(type=openapi.TYPE_STRING),
                        'adresse': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                ),
                'date_embauche': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                'taux_commission': openapi.Schema(type=openapi.TYPE_NUMBER),
                'notes': openapi.Schema(type=openapi.TYPE_STRING)
            }
        ),
        responses={
            200: openapi.Response(
                description="Vendeur mis à jour",
                schema=openapi.Schema(type=openapi.TYPE_OBJECT)
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def put(self, request, id):
        try:
            vendeur = Vendeur.objects.get(id_vendeur=id)
            serializer = VendeurSerializer(vendeur, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Vendeur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_description="Supprimer un vendeur",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def delete(self, request, id):
        try:
            vendeur = Vendeur.objects.get(id_vendeur=id)
            vendeur.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Vendeur.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)