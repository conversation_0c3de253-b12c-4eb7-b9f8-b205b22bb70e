from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import Statistique, StatistiqueVendeur, StatistiqueMagasin
from ventes.models import Vente
from magasins.models import Magasin
from vendeurs.models import Vendeur
import logging

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Statistique)
def validate_statistique(sender, instance, **kwargs):
    """
    Valide les statistiques avant la sauvegarde
    """
    if not instance.magasin:
        raise ValueError("Une statistique doit être associée à un magasin")
    
    if instance.montant_total < 0:
        raise ValueError("Le montant total ne peut pas être négatif")
    
    if instance.nombre_ventes < 0:
        raise ValueError("Le nombre de ventes ne peut pas être négatif")

@receiver(post_save, sender=Vente)
def update_statistiques_after_vente(sender, instance, created, **kwargs):
    """
    Met à jour les statistiques après une vente
    """
    try:
        with transaction.atomic():
            # Mise à jour des statistiques du magasin
            magasin_stats, _ = Statistique.objects.get_or_create(
                magasin=instance.magasin,
                defaults={
                    'nombre_ventes': 0,
                    'montant_total': 0,
                    'moyenne_vente': 0
                }
            )
            
            magasin_stats.nombre_ventes += 1
            magasin_stats.montant_total += instance.montant_total
            magasin_stats.moyenne_vente = magasin_stats.montant_total / magasin_stats.nombre_ventes
            magasin_stats.save()
            
            # Mise à jour des statistiques du vendeur
            if instance.vendeur:
                vendeur_stats, _ = StatistiqueVendeur.objects.get_or_create(
                    vendeur=instance.vendeur,
                    defaults={
                        'nombre_ventes': 0,
                        'montant_total': 0,
                        'moyenne_vente': 0
                    }
                )
                
                vendeur_stats.nombre_ventes += 1
                vendeur_stats.montant_total += instance.montant_total
                vendeur_stats.moyenne_vente = vendeur_stats.montant_total / vendeur_stats.nombre_ventes
                vendeur_stats.save()
                
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des statistiques: {str(e)}")

@receiver(post_delete, sender=Statistique)
def handle_statistique_deletion(sender, instance, **kwargs):
    """
    Gère la suppression des statistiques
    """
    try:
        # Réinitialiser les statistiques du magasin
        Statistique.objects.create(
            magasin=instance.magasin,
            nombre_ventes=0,
            montant_total=0,
            moyenne_vente=0
        )
    except Exception as e:
        logger.error(f"Erreur lors de la suppression des statistiques: {str(e)}")

@receiver(post_save, sender=Magasin)
def create_magasin_statistics(sender, instance, created, **kwargs):
    """
    Crée les statistiques initiales pour un nouveau magasin
    """
    if created:
        try:
            Statistique.objects.create(
                magasin=instance,
                nombre_ventes=0,
                montant_total=0,
                moyenne_vente=0
            )
        except Exception as e:
            logger.error(f"Erreur lors de la création des statistiques du magasin: {str(e)}")

@receiver(post_save, sender=Vendeur)
def create_vendeur_statistics(sender, instance, created, **kwargs):
    """
    Crée les statistiques initiales pour un nouveau vendeur
    """
    if created:
        try:
            StatistiqueVendeur.objects.create(
                vendeur=instance,
                nombre_ventes=0,
                montant_total=0,
                moyenne_vente=0
            )
        except Exception as e:
            logger.error(f"Erreur lors de la création des statistiques du vendeur: {str(e)}") 