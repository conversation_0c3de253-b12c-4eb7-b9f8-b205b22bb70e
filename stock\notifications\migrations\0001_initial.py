# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_enabled', models.BooleanField(default=True)),
                ('sms_enabled', models.BooleanField(default=False)),
                ('push_enabled', models.BooleanField(default=True)),
                ('in_app_enabled', models.BooleanField(default=True)),
                ('stock_notifications', models.BooleanField(default=True)),
                ('vente_notifications', models.BooleanField(default=True)),
                ('achat_notifications', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('inventaire_notifications', models.BooleanField(default=True)),
                ('finance_notifications', models.Bo<PERSON>anField(default=True)),
                ('system_notifications', models.BooleanField(default=True)),
                ('security_notifications', models.BooleanField(default=True)),
                ('quiet_hours_start', models.TimeField(default='22:00', help_text='Début des heures silencieuses')),
                ('quiet_hours_end', models.TimeField(default='08:00', help_text='Fin des heures silencieuses')),
                ('weekend_notifications', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Préférence de Notification',
                'verbose_name_plural': 'Préférences de Notifications',
            },
        ),
        migrations.CreateModel(
            name='NotificationType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField()),
                ('categorie', models.CharField(choices=[('STOCK', 'Gestion de Stock'), ('VENTE', 'Ventes'), ('ACHAT', 'Achats'), ('INVENTAIRE', 'Inventaire'), ('FINANCE', 'Finance'), ('SYSTEM', 'Système'), ('SECURITY', 'Sécurité'), ('USER', 'Utilisateur')], max_length=20)),
                ('priorite_defaut', models.CharField(choices=[('LOW', 'Basse'), ('MEDIUM', 'Moyenne'), ('HIGH', 'Haute'), ('CRITICAL', 'Critique')], default='MEDIUM', max_length=10)),
                ('template_email', models.TextField(blank=True, help_text='Template HTML pour email')),
                ('template_sms', models.CharField(blank=True, help_text='Template SMS (160 caractères max)', max_length=160)),
                ('template_push', models.CharField(blank=True, help_text='Template notification push', max_length=100)),
                ('template_in_app', models.TextField(blank=True, help_text='Template notification in-app')),
                ('actif', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Type de Notification',
                'verbose_name_plural': 'Types de Notifications',
                'indexes': [models.Index(fields=['categorie'], name='notificatio_categor_e019de_idx'), models.Index(fields=['priorite_defaut'], name='notificatio_priorit_14d8e6_idx')],
            },
        ),
        migrations.CreateModel(
            name='NotificationBatch',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('criteres_selection', models.JSONField(default=dict, help_text='Critères pour sélectionner les destinataires')),
                ('statut', models.CharField(choices=[('PENDING', 'En attente'), ('PROCESSING', 'En cours'), ('COMPLETED', 'Terminé'), ('FAILED', 'Échec'), ('CANCELLED', 'Annulé')], default='PENDING', max_length=15)),
                ('total_destinataires', models.PositiveIntegerField(default=0)),
                ('notifications_envoyees', models.PositiveIntegerField(default=0)),
                ('notifications_echouees', models.PositiveIntegerField(default=0)),
                ('date_planification', models.DateTimeField(blank=True, null=True)),
                ('date_debut_traitement', models.DateTimeField(blank=True, null=True)),
                ('date_fin_traitement', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('createur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('type_notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notifications.notificationtype')),
            ],
            options={
                'verbose_name': 'Lot de Notifications',
                'verbose_name_plural': 'Lots de Notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['statut'], name='notificatio_statut_dbdb32_idx'), models.Index(fields=['date_planification'], name='notificatio_date_pl_87efc6_idx'), models.Index(fields=['createur'], name='notificatio_createu_29f066_idx')],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('titre', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('donnees_contexte', models.JSONField(blank=True, default=dict, help_text='Données contextuelles pour les templates')),
                ('object_id', models.CharField(blank=True, max_length=100, null=True)),
                ('canal', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PUSH', 'Push'), ('IN_APP', 'In-App')], max_length=10)),
                ('statut', models.CharField(choices=[('PENDING', 'En attente'), ('SENT', 'Envoyée'), ('DELIVERED', 'Délivrée'), ('READ', 'Lue'), ('FAILED', 'Échec'), ('CANCELLED', 'Annulée')], default='PENDING', max_length=15)),
                ('priorite', models.CharField(choices=[('LOW', 'Basse'), ('MEDIUM', 'Moyenne'), ('HIGH', 'Haute'), ('CRITICAL', 'Critique')], max_length=10)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_envoi', models.DateTimeField(blank=True, null=True)),
                ('date_lecture', models.DateTimeField(blank=True, null=True)),
                ('date_expiration', models.DateTimeField(blank=True, null=True)),
                ('tentatives_envoi', models.PositiveIntegerField(default=0)),
                ('erreur_envoi', models.TextField(blank=True)),
                ('identifiant_externe', models.CharField(blank=True, help_text='ID du service externe (Twilio, Firebase, etc.)', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('destinataire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('type_notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notifications.notificationtype')),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-date_creation'],
                'indexes': [models.Index(fields=['destinataire', 'statut'], name='notificatio_destina_10ae3b_idx'), models.Index(fields=['type_notification'], name='notificatio_type_no_b72568_idx'), models.Index(fields=['canal'], name='notificatio_canal_77cbd2_idx'), models.Index(fields=['priorite'], name='notificatio_priorit_9af988_idx'), models.Index(fields=['date_creation'], name='notificatio_date_cr_1b0271_idx'), models.Index(fields=['date_expiration'], name='notificatio_date_ex_73c4fe_idx')],
            },
        ),
    ]
