from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from notifications.models import Notification


class Command(BaseCommand):
    help = 'Nettoie les anciennes notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Nombre de jours à conserver (défaut: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulation sans suppression réelle'
        )
        parser.add_argument(
            '--keep-unread',
            action='store_true',
            help='Conserver les notifications non lues'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        keep_unread = options['keep_unread']
        
        # Calculer la date limite
        date_limite = timezone.now() - timedelta(days=days)
        
        self.stdout.write(f'🧹 Nettoyage des notifications antérieures au {date_limite.strftime("%d/%m/%Y %H:%M")}')
        
        # Construire la requête
        queryset = Notification.objects.filter(date_creation__lt=date_limite)
        
        if keep_unread:
            # Exclure les notifications non lues
            queryset = queryset.exclude(statut__in=['PENDING', 'SENT', 'DELIVERED'])
            self.stdout.write('📖 Conservation des notifications non lues')
        
        # Statistiques avant suppression
        total_count = queryset.count()
        
        if total_count == 0:
            self.stdout.write(self.style.SUCCESS('✅ Aucune notification à supprimer'))
            return
        
        # Statistiques par statut
        stats_by_status = {}
        for status_choice in Notification.STATUS_CHOICES:
            status_code = status_choice[0]
            count = queryset.filter(statut=status_code).count()
            if count > 0:
                stats_by_status[status_choice[1]] = count
        
        # Statistiques par canal
        stats_by_channel = {}
        for channel_choice in Notification.CHANNEL_CHOICES:
            channel_code = channel_choice[0]
            count = queryset.filter(canal=channel_code).count()
            if count > 0:
                stats_by_channel[channel_choice[1]] = count
        
        # Afficher les statistiques
        self.stdout.write(f'\n📊 Notifications à supprimer: {total_count}')
        
        if stats_by_status:
            self.stdout.write('\n📈 Répartition par statut:')
            for status, count in stats_by_status.items():
                self.stdout.write(f'  • {status}: {count}')
        
        if stats_by_channel:
            self.stdout.write('\n📱 Répartition par canal:')
            for channel, count in stats_by_channel.items():
                self.stdout.write(f'  • {channel}: {count}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\n🔍 Mode simulation - Aucune suppression effectuée')
            )
            return
        
        # Demander confirmation
        self.stdout.write(f'\n⚠️  Êtes-vous sûr de vouloir supprimer {total_count} notifications?')
        confirm = input('Tapez "oui" pour confirmer: ')
        
        if confirm.lower() != 'oui':
            self.stdout.write(self.style.ERROR('❌ Suppression annulée'))
            return
        
        # Effectuer la suppression
        try:
            deleted_count, deleted_details = queryset.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'\n✅ {deleted_count} notifications supprimées avec succès')
            )
            
            # Afficher les détails de suppression
            if deleted_details:
                self.stdout.write('\n🗑️  Détails de suppression:')
                for model, count in deleted_details.items():
                    if count > 0:
                        self.stdout.write(f'  • {model}: {count}')
            
            # Calculer l'espace libéré (estimation)
            estimated_space = deleted_count * 2  # Estimation: 2KB par notification
            if estimated_space > 1024:
                space_mb = estimated_space / 1024
                self.stdout.write(f'💾 Espace libéré (estimation): {space_mb:.1f} MB')
            else:
                self.stdout.write(f'💾 Espace libéré (estimation): {estimated_space} KB')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Erreur lors de la suppression: {str(e)}')
            )
            return
        
        # Statistiques finales
        remaining_count = Notification.objects.count()
        self.stdout.write(f'\n📊 Notifications restantes: {remaining_count}')
        
        # Suggestions d'optimisation
        if remaining_count > 10000:
            self.stdout.write(
                self.style.WARNING(
                    '\n💡 Suggestion: Considérez un nettoyage plus fréquent '
                    'pour maintenir de bonnes performances'
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS('\n🎉 Nettoyage terminé avec succès!')
        )
