# Generated by Django 5.2.4 on 2025-07-23 07:34

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id_client', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('nom', models.Char<PERSON>ield(max_length=100)),
                ('prenom', models.Char<PERSON>ield(max_length=100)),
                ('telephone', models.Char<PERSON>ield(max_length=15)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('adresse', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Client',
                'verbose_name_plural': 'Clients',
                'indexes': [models.Index(fields=['nom', 'prenom'], name='clients_cli_nom_f99d14_idx'), models.Index(fields=['telephone'], name='clients_cli_telepho_8c69d9_idx'), models.Index(fields=['email'], name='clients_cli_email_56b9fc_idx')],
            },
        ),
    ]
