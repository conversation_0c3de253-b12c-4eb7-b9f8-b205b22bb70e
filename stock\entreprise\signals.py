from django.db.models.signals import post_migrate, pre_save, post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import Permission
from django.contrib.auth import get_user_model
from entreprise.models import Entreprise
from personalisation_user.models import UserProfile
from datetime import date
from django.apps import apps
from django.db import transaction
import logging
from statistiques.models import Statistique
from stock_Pme.models import Stock

logger = logging.getLogger(__name__)

@receiver(post_migrate)
def create_super_admin(sender, **kwargs):
    if sender.name != 'entreprise':
        return

    # Créer l'entreprise par défaut
    entreprise, created = Entreprise.objects.get_or_create(
        nom="Entreprise Principale",
        defaults={
            'adresse': "Adresse par défaut",
            'nif': "0000000000000",  # NIF par défaut
            'date_creation': date.today(),
            'statut': True
        }
    )

    # Obtenir le modèle d'utilisateur de manière sûre
    User = apps.get_model('base', 'User')

    # Créer le super admin
    super_admin, created = User.objects.get_or_create(
        username='superadmin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'nom': 'Super',
            'prenom': 'Admin',
            'telephone': '0000000000',
            'adresse': 'Adresse du super admin',
            'role': 'ADMIN',
            'statut': True
        }
    )

    if created:
        super_admin.set_password('superadmin123')  # Changer ce mot de passe en production
        super_admin.save()

    # Donner tous les droits au super admin
    all_permissions = Permission.objects.all()
    super_admin.user_permissions.set(all_permissions)

    # Créer le profil du super admin
    UserProfile.objects.get_or_create(
        user=super_admin,
        defaults={
            'entreprise': entreprise,
            'role': 'SUPER_ADMIN',
            'telephone': '0000000000',
            'adresse': 'Adresse du super admin'
        }
    )

@receiver(pre_save, sender=Entreprise)
def validate_entreprise(sender, instance, **kwargs):
    """
    Valide l'entreprise avant la sauvegarde
    """
    if not instance.nom:
        raise ValueError("Le nom de l'entreprise est obligatoire")
    if not instance.adresse:
        raise ValueError("L'adresse de l'entreprise est obligatoire")
    if not instance.nif:
        raise ValueError("Le NIF est obligatoire")
    if not instance.date_creation:
        raise ValueError("La date de création est obligatoire")
    
    # Vérifier si le NIF est unique
    if Entreprise.objects.filter(nif=instance.nif).exclude(id=instance.id).exists():
        raise ValueError("Une entreprise avec ce NIF existe déjà")
    
    # Vérifier la validité du NIF
    if not instance.verifier_validite_nif():
        raise ValueError("Le NIF n'est pas valide")

@receiver(post_save, sender=Entreprise)
def create_initial_statistics(sender, instance, created, **kwargs):
    """
    Crée les statistiques initiales pour une nouvelle entreprise
    """
    if created:
        try:
            with transaction.atomic():
                Statistique.objects.create(
                    entreprise=instance,
                    date_statistique=instance.date_creation
                )
                logger.info(f"Statistiques initiales créées pour l'entreprise {instance.nom}")
        except Exception as e:
            logger.error(f"Erreur lors de la création des statistiques: {str(e)}")

@receiver(post_delete, sender=Entreprise)
def handle_entreprise_deletion(sender, instance, **kwargs):
    """
    Gère la suppression d'une entreprise
    """
    try:
        with transaction.atomic():
            # Supprimer les statistiques associées
            Statistique.objects.filter(entreprise=instance).delete()
            logger.info(f"Statistiques supprimées pour l'entreprise {instance.nom}")
            
            # Supprimer les stocks associés
            Stock.objects.filter(produit__entreprise=instance).delete()
            logger.info(f"Stocks supprimés pour l'entreprise {instance.nom}")
            
            logger.info(f"Entreprise {instance.nom} supprimée avec succès")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de l'entreprise: {str(e)}")

@receiver(post_save, sender=Entreprise)
def update_entreprise_status(sender, instance, **kwargs):
    """
    Met à jour le statut des entités associées lorsque le statut de l'entreprise change
    """
    if not instance.statut:
        try:
            with transaction.atomic():
                # Désactiver tous les magasins de l'entreprise
                instance.magasins.all().update(actif=False)
                logger.info(f"Magasins désactivés pour l'entreprise {instance.nom}")
                
                # Désactiver tous les entrepôts de l'entreprise
                instance.entrepot_set.all().update(statut=False)
                logger.info(f"Entrepôts désactivés pour l'entreprise {instance.nom}")
                
                logger.info(f"Statut mis à jour pour l'entreprise {instance.nom}")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour du statut: {str(e)}") 