from rest_framework.permissions import IsAuthenticated
from base.views import IsAdmin
from rest_framework.views import APIView
from rest_framework.response import Response
from base.models import User
from base.serializer import UserSerializer
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

#liste utilisateur 
class List_User_View(APIView):
    # permission_classes = [IsAuthenticated ]

    @swagger_auto_schema(
        operation_description="Lister tous les utilisateurs",
        responses={200: UserSerializer(many=True)}
    )
    def get(self, request) :
        users = User.objects.all()
        serializer = UserSerializer(users, many=True, context={'request': request})
        return Response(serializer.data) 
       

#utilisateur specifique
class GetUserView(APIView):
    # permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        operation_description="Récupérer l'utilisateur connecté",
        responses={200: UserSerializer()}
    )
    def get(self, request):
        try:
            user = request.user  
            serializer = UserSerializer(user, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
    
     # modification de users et photo profil   
    @swagger_auto_schema(
        operation_description="Modifier l'utilisateur connecté (partiel)",
        request_body=UserSerializer,
        responses={200: UserSerializer(), 400: "Bad Request"}
    )
    def patch(self , request):
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True)
        data = request.data.dict() if hasattr(request.data, 'dict') else request.data
        # Gestion spécifique du fichier
        if 'avatar' in request.FILES:
            data['avatar'] = request.FILES['avatar']
        serializer = UserSerializer(
            user,
            data=data,
            partial=True,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# filtrer utilisateur par role 
class Filter_Users_By_Role(APIView):
    permission_classes = [IsAuthenticated , IsAdmin]

    @swagger_auto_schema(
        operation_description="Filtrer les utilisateurs par rôle",
        manual_parameters=[openapi.Parameter('role', openapi.IN_QUERY, description="Rôle à filtrer", type=openapi.TYPE_STRING)],
        responses={200: UserSerializer(many=True)}
    )
    def get(self, request) :
        role = request.query_params.get('role', None)   # Récupérer le paramètre 'role' dans l'URL 
        if role :
            users = User.objects.filter(role=role) #filtrer par role
        else :
            users = User.objects.all() #si pas de role, tous les utilisateurs
        serializer = UserSerializer(users, many=True, context={'request': request})
        return Response(serializer.data)

class GetUserByIdView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Récupérer un utilisateur par son ID",
        manual_parameters=[openapi.Parameter('user_id', openapi.IN_PATH, description="ID utilisateur", type=openapi.TYPE_STRING)],
        responses={200: UserSerializer(), 404: "Utilisateur non trouvé"}
    )
    def get(self, request, user_id):
        try:
            user = User.objects.get(id_utilisateur=user_id)
            serializer = UserSerializer(user, context={'request': request})
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response({"error": "Utilisateur non trouvé"}, status=status.HTTP_404_NOT_FOUND)


class FilterUsersByEntrepriseView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Filtrer les utilisateurs par entreprise",
        manual_parameters=[
            openapi.Parameter('entreprise_id', openapi.IN_QUERY, description="ID de l'entreprise", type=openapi.TYPE_STRING, required=True)
        ],
        responses={
            200: UserSerializer(many=True),
            400: "ID entreprise requis",
            404: "Entreprise non trouvée"
        }
    )
    def get(self, request):
        entreprise_id = request.query_params.get('entreprise_id', None)

        if not entreprise_id:
            return Response({"error": "ID entreprise requis"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from entreprise.models import Entreprise
            entreprise = Entreprise.objects.get(id=entreprise_id)
        except Entreprise.DoesNotExist:
            return Response({"error": "Entreprise non trouvée"}, status=status.HTTP_404_NOT_FOUND)

        # Récupérer tous les utilisateurs liés à cette entreprise
        users = []

        # 1. Admins via UserProfile
        admin_users = User.objects.filter(
            profile__entreprise=entreprise,
            role=User.Role.ADMIN
        )
        users.extend(admin_users)

        # 2. Responsables magasin via leurs magasins
        responsable_magasin_users = User.objects.filter(
            responsable_magasin__magasin__entreprise=entreprise,
            role=User.Role.RESPONSABLE_MAGASIN
        )
        users.extend(responsable_magasin_users)

        # 3. Vendeurs via leurs magasins
        vendeur_users = User.objects.filter(
            vendeur__magasin__entreprise=entreprise,
            role=User.Role.VENDEUR
        )
        users.extend(vendeur_users)

        # 4. Responsables entrepôt via leurs entrepôts
        responsable_entrepot_users = User.objects.filter(
            responsableentrepot__entrepot__entreprise=entreprise,
            role=User.Role.RESPONSABLE_ENTREPOT
        )
        users.extend(responsable_entrepot_users)

        # Supprimer les doublons et trier
        unique_users = list(set(users))
        unique_users.sort(key=lambda x: (x.nom, x.prenom))

        serializer = UserSerializer(unique_users, many=True, context={'request': request})
        return Response({
            'entreprise': {
                'id': str(entreprise.id),
                'nom': entreprise.nom
            },
            'total_users': len(unique_users),
            'users': serializer.data
        })


class FilterUsersByMagasinView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Filtrer les utilisateurs par magasin",
        manual_parameters=[
            openapi.Parameter('magasin_id', openapi.IN_QUERY, description="ID du magasin", type=openapi.TYPE_STRING, required=True)
        ],
        responses={
            200: UserSerializer(many=True),
            400: "ID magasin requis",
            404: "Magasin non trouvé"
        }
    )
    def get(self, request):
        magasin_id = request.query_params.get('magasin_id', None)

        if not magasin_id:
            return Response({"error": "ID magasin requis"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from magasins.models import Magasin
            magasin = Magasin.objects.get(id=magasin_id)
        except Magasin.DoesNotExist:
            return Response({"error": "Magasin non trouvé"}, status=status.HTTP_404_NOT_FOUND)

        # Récupérer tous les utilisateurs liés à ce magasin
        users = []

        # 1. Responsables magasin
        responsable_magasin_users = User.objects.filter(
            responsable_magasin__magasin=magasin,
            role=User.Role.RESPONSABLE_MAGASIN
        )
        users.extend(responsable_magasin_users)

        # 2. Vendeurs
        vendeur_users = User.objects.filter(
            vendeur__magasin=magasin,
            role=User.Role.VENDEUR
        )
        users.extend(vendeur_users)

        # Supprimer les doublons et trier
        unique_users = list(set(users))
        unique_users.sort(key=lambda x: (x.nom, x.prenom))

        serializer = UserSerializer(unique_users, many=True, context={'request': request})
        return Response({
            'magasin': {
                'id': str(magasin.id),
                'nom': magasin.nom,
                'entreprise': {
                    'id': str(magasin.entreprise.id),
                    'nom': magasin.entreprise.nom
                }
            },
            'total_users': len(unique_users),
            'users': serializer.data
        })


class FilterUsersAdvancedView(APIView):
    permission_classes = [IsAuthenticated, IsAdmin]

    @swagger_auto_schema(
        operation_description="Filtrer les utilisateurs par entreprise et/ou magasin avec options avancées",
        manual_parameters=[
            openapi.Parameter('entreprise_id', openapi.IN_QUERY, description="ID de l'entreprise", type=openapi.TYPE_STRING),
            openapi.Parameter('magasin_id', openapi.IN_QUERY, description="ID du magasin", type=openapi.TYPE_STRING),
            openapi.Parameter('role', openapi.IN_QUERY, description="Rôle à filtrer", type=openapi.TYPE_STRING),
            openapi.Parameter('actif', openapi.IN_QUERY, description="Statut actif (true/false)", type=openapi.TYPE_BOOLEAN),
        ],
        responses={
            200: UserSerializer(many=True),
            400: "Paramètres invalides"
        }
    )
    def get(self, request):
        entreprise_id = request.query_params.get('entreprise_id', None)
        magasin_id = request.query_params.get('magasin_id', None)
        role_filter = request.query_params.get('role', None)
        actif_filter = request.query_params.get('actif', None)

        # Validation des paramètres
        if not entreprise_id and not magasin_id:
            return Response({
                "error": "Au moins un paramètre entreprise_id ou magasin_id est requis"
            }, status=status.HTTP_400_BAD_REQUEST)

        users = []
        context_info = {}

        try:
            # Filtrage par magasin (prioritaire si les deux sont fournis)
            if magasin_id:
                from magasins.models import Magasin
                magasin = Magasin.objects.get(id=magasin_id)
                context_info['magasin'] = {
                    'id': str(magasin.id),
                    'nom': magasin.nom,
                    'entreprise': {
                        'id': str(magasin.entreprise.id),
                        'nom': magasin.entreprise.nom
                    }
                }

                # Responsables magasin
                responsable_magasin_users = User.objects.filter(
                    responsable_magasin__magasin=magasin,
                    role=User.Role.RESPONSABLE_MAGASIN
                )
                users.extend(responsable_magasin_users)

                # Vendeurs
                vendeur_users = User.objects.filter(
                    vendeur__magasin=magasin,
                    role=User.Role.VENDEUR
                )
                users.extend(vendeur_users)

            # Filtrage par entreprise
            elif entreprise_id:
                from entreprise.models import Entreprise
                entreprise = Entreprise.objects.get(id=entreprise_id)
                context_info['entreprise'] = {
                    'id': str(entreprise.id),
                    'nom': entreprise.nom
                }

                # Admins via UserProfile
                admin_users = User.objects.filter(
                    profile__entreprise=entreprise,
                    role=User.Role.ADMIN
                )
                users.extend(admin_users)

                # Responsables magasin via leurs magasins
                responsable_magasin_users = User.objects.filter(
                    responsable_magasin__magasin__entreprise=entreprise,
                    role=User.Role.RESPONSABLE_MAGASIN
                )
                users.extend(responsable_magasin_users)

                # Vendeurs via leurs magasins
                vendeur_users = User.objects.filter(
                    vendeur__magasin__entreprise=entreprise,
                    role=User.Role.VENDEUR
                )
                users.extend(vendeur_users)

                # Responsables entrepôt via leurs entrepôts
                responsable_entrepot_users = User.objects.filter(
                    responsableentrepot__entrepot__entreprise=entreprise,
                    role=User.Role.RESPONSABLE_ENTREPOT
                )
                users.extend(responsable_entrepot_users)

            # Supprimer les doublons
            unique_users = list(set(users))

            # Filtrage par rôle si spécifié
            if role_filter:
                unique_users = [user for user in unique_users if user.role == role_filter]

            # Filtrage par statut actif si spécifié
            if actif_filter is not None:
                actif_bool = actif_filter.lower() == 'true'
                unique_users = [user for user in unique_users if user.statut == actif_bool]

            # Trier par nom et prénom
            unique_users.sort(key=lambda x: (x.nom, x.prenom))

            serializer = UserSerializer(unique_users, many=True, context={'request': request})

            response_data = {
                **context_info,
                'filters_applied': {
                    'role': role_filter,
                    'actif': actif_filter
                },
                'total_users': len(unique_users),
                'users': serializer.data
            }

            return Response(response_data)

        except Exception as e:
            return Response({
                "error": f"Erreur lors du filtrage: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)