# Generated by Django 5.2.4 on 2025-07-23 07:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Inventaire',
            fields=[
                ('id_inventaire', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('numero_inventaire', models.CharField(max_length=50, unique=True)),
                ('type_inventaire', models.CharField(choices=[('COMPLET', 'Inventaire complet'), ('PARTIEL', 'Inventaire partiel'), ('CYCLIQUE', 'Inventaire cyclique'), ('ALERTE', 'Inventaire suite à alerte')], default='COMPLET', max_length=20)),
                ('date_planification', models.DateField()),
                ('date_debut', models.DateTimeField(blank=True, null=True)),
                ('date_fin', models.DateTimeField(blank=True, null=True)),
                ('statut', models.CharField(choices=[('PLANIFIE', 'Planifié'), ('EN_COURS', 'En cours'), ('TERMINE', 'Terminé'), ('ANNULE', 'Annulé')], default='PLANIFIE', max_length=20)),
                ('commentaire', models.TextField(blank=True)),
                ('tolerance_ecart', models.DecimalField(decimal_places=2, default=0.0, help_text="Tolérance d'écart en pourcentage", max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Inventaire',
                'verbose_name_plural': 'Inventaires',
                'ordering': ['-date_planification'],
            },
        ),
        migrations.CreateModel(
            name='DetailInventaire',
            fields=[
                ('id_detail', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantite_theorique', models.IntegerField()),
                ('quantite_reelle', models.IntegerField()),
                ('statut', models.BooleanField(default=True)),
                ('commentaire', models.TextField(blank=True)),
                ('date_comptage', models.DateTimeField(auto_now_add=True)),
                ('compteur', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comptages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Détail Inventaire',
                'verbose_name_plural': 'Détails Inventaires',
            },
        ),
    ]
