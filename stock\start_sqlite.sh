#!/bin/bash

# 🚀 Script de démarrage optimisé pour SQLite
# Phase 2 Performance - Configuration SQLite

echo "🚀 Démarrage de l'application avec SQLite optimisé..."

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'affichage coloré
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Vérifier si Python est installé
if ! command -v python &> /dev/null; then
    print_error "Python n'est pas installé"
    exit 1
fi

print_status "Python détecté"

# Créer les dossiers nécessaires
echo "📁 Création des dossiers..."
mkdir -p logs
mkdir -p data
mkdir -p static
mkdir -p media

print_status "Dossiers créés"

# Vérifier si l'environnement virtuel existe
if [ ! -d "venv" ]; then
    print_warning "Environnement virtuel non trouvé, création..."
    python -m venv venv
    print_status "Environnement virtuel créé"
fi

# Activer l'environnement virtuel
echo "🔧 Activation de l'environnement virtuel..."
source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null

if [ $? -eq 0 ]; then
    print_status "Environnement virtuel activé"
else
    print_error "Impossible d'activer l'environnement virtuel"
    exit 1
fi

# Installer les dépendances
echo "📦 Installation des dépendances..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    print_status "Dépendances installées"
else
    print_error "Erreur lors de l'installation des dépendances"
    exit 1
fi

# Installer les dépendances de performance
echo "⚡ Installation des dépendances de performance..."
pip install django-redis psutil

if [ $? -eq 0 ]; then
    print_status "Dépendances de performance installées"
else
    print_warning "Certaines dépendances de performance n'ont pas pu être installées"
fi

# Vérifier si Redis est disponible (optionnel pour SQLite)
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        print_status "Redis disponible et fonctionnel"
        export REDIS_AVAILABLE=true
    else
        print_warning "Redis installé mais non démarré"
        print_info "Démarrage de Redis recommandé pour de meilleures performances"
        export REDIS_AVAILABLE=false
    fi
else
    print_warning "Redis non installé"
    print_info "L'application fonctionnera sans cache Redis"
    export REDIS_AVAILABLE=false
fi

# Migrations de base de données
echo "🗄️  Préparation de la base de données SQLite..."

# Créer les migrations si nécessaire
python manage.py makemigrations

if [ $? -eq 0 ]; then
    print_status "Migrations créées"
else
    print_warning "Aucune nouvelle migration"
fi

# Appliquer les migrations
python manage.py migrate

if [ $? -eq 0 ]; then
    print_status "Base de données SQLite configurée"
else
    print_error "Erreur lors de la configuration de la base de données"
    exit 1
fi

# Créer les types de notifications
echo "📱 Configuration des notifications..."
python manage.py create_notification_types

if [ $? -eq 0 ]; then
    print_status "Types de notifications créés"
else
    print_warning "Types de notifications déjà existants ou erreur"
fi

# Optimiser la base de données SQLite
echo "⚡ Optimisation de SQLite..."
python manage.py optimize_database --create-indexes --analyze-tables

if [ $? -eq 0 ]; then
    print_status "Base de données SQLite optimisée"
else
    print_warning "Optimisation partielle de SQLite"
fi

# Collecter les fichiers statiques
echo "📄 Collection des fichiers statiques..."
python manage.py collectstatic --noinput

if [ $? -eq 0 ]; then
    print_status "Fichiers statiques collectés"
else
    print_warning "Erreur lors de la collection des fichiers statiques"
fi

# Créer un superutilisateur si nécessaire
echo "👤 Vérification du superutilisateur..."
python manage.py shell -c "
from base.models import User
if not User.objects.filter(role='ADMIN').exists():
    print('Création du superutilisateur...')
    User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        telephone='0000000000',
        nom='Admin',
        prenom='System',
        adresse='System Address'
    )
    print('Superutilisateur créé: admin/admin123')
else:
    print('Superutilisateur déjà existant')
"

# Afficher les informations de démarrage
echo ""
echo "🎉 Configuration terminée !"
echo ""
print_info "=== INFORMATIONS DE DÉMARRAGE ==="
print_info "Base de données: SQLite ($(ls -lh db.sqlite3 2>/dev/null | awk '{print $5}' || echo 'nouveau fichier'))"
print_info "Cache: $([ "$REDIS_AVAILABLE" = "true" ] && echo "Redis activé" || echo "Cache Django par défaut")"
print_info "Environnement: Développement"
print_info "Superutilisateur: admin / admin123"
echo ""

# Proposer de démarrer le serveur
read -p "🚀 Démarrer le serveur de développement ? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌐 Démarrage du serveur Django..."
    echo ""
    print_status "Serveur disponible sur: http://localhost:8000"
    print_status "Admin: http://localhost:8000/admin"
    print_status "API Docs: http://localhost:8000/swagger/"
    print_status "Performance: http://localhost:8000/api/stocks/performance/"
    echo ""
    print_info "Arrêt avec Ctrl+C"
    echo ""
    
    # Démarrer le serveur
    python manage.py runserver
else
    echo ""
    print_info "Pour démarrer le serveur manuellement:"
    print_info "  source venv/bin/activate"
    print_info "  python manage.py runserver"
    echo ""
    print_info "URLs importantes:"
    print_info "  http://localhost:8000 - Application"
    print_info "  http://localhost:8000/admin - Administration"
    print_info "  http://localhost:8000/swagger/ - Documentation API"
    print_info "  http://localhost:8000/api/stocks/performance/ - Métriques"
    echo ""
    print_info "Commandes utiles:"
    print_info "  python manage.py performance_monitor - Monitoring temps réel"
    print_info "  python manage.py optimize_database --all - Optimisation SQLite"
    print_info "  python manage.py run_load_tests - Tests de charge"
fi

echo ""
print_status "🎉 Prêt à l'emploi avec SQLite optimisé !"
