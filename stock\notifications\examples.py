"""
Exemples d'utilisation de l'application Notifications
"""

from django.contrib.auth import get_user_model
from notifications.services import NotificationService
from notifications.models import NotificationType, NotificationPreference

User = get_user_model()


def exemple_notification_stock_bas():
    """Exemple : Notification de stock bas"""
    
    # Récupérer un utilisateur (responsable magasin)
    user = User.objects.filter(role='RESPONSABLE_MAGASIN').first()
    
    if user:
        # Créer et envoyer la notification
        notification = NotificationService.creer_et_envoyer_notification(
            type_notification='ALERTE_STOCK_BAS',
            destinataires=[user],
            titre='Alerte Stock - Produit ABC',
            message='Le stock du produit ABC est en dessous du seuil d\'alerte',
            donnees_contexte={
                'produit_nom': 'Produit ABC',
                'produit_reference': 'REF-ABC-001',
                'quantite_actuelle': 5,
                'seuil_alerte': 10,
                'magasin_nom': 'Magasin Principal',
                'entrepot_nom': 'Entrepôt Central'
            },
            canal='EMAIL',
            priorite='HIGH'
        )
        
        print(f"Notification créée : {notification[0].id if notification else 'Échec'}")


def exemple_notification_multiple_canaux():
    """Exemple : Notification sur plusieurs canaux"""
    
    user = User.objects.filter(role='ADMIN').first()
    
    if user:
        # Email
        NotificationService.creer_et_envoyer_notification(
            type_notification='NOUVELLE_VENTE',
            destinataires=[user],
            titre='Nouvelle vente importante',
            message='Une vente de 1000€ vient d\'être réalisée',
            donnees_contexte={
                'montant_total': 1000,
                'vendeur_nom': 'Jean Dupont',
                'client_nom': 'Client VIP',
                'magasin_nom': 'Magasin Centre-Ville'
            },
            canal='EMAIL',
            priorite='MEDIUM'
        )
        
        # Notification in-app
        NotificationService.creer_et_envoyer_notification(
            type_notification='NOUVELLE_VENTE',
            destinataires=[user],
            titre='Vente 1000€',
            message='Nouvelle vente importante',
            donnees_contexte={
                'montant_total': 1000,
                'vendeur_nom': 'Jean Dupont'
            },
            canal='IN_APP',
            priorite='MEDIUM'
        )


def exemple_notification_lot():
    """Exemple : Notification en lot à tous les administrateurs"""
    
    # Récupérer tous les administrateurs
    admins = User.objects.filter(role='ADMIN', statut=True)
    
    if admins.exists():
        NotificationService.creer_et_envoyer_notification(
            type_notification='MAINTENANCE_PROGRAMMEE',
            destinataires=list(admins),
            titre='Maintenance programmée ce soir',
            message='Le système sera indisponible de 22h à 2h pour maintenance',
            donnees_contexte={
                'date_maintenance': '2024-01-15 22:00',
                'duree': '4 heures',
                'impact': 'Système complet'
            },
            canal='EMAIL',
            priorite='HIGH'
        )


def exemple_configuration_preferences():
    """Exemple : Configuration des préférences utilisateur"""
    
    user = User.objects.first()
    
    if user:
        # Récupérer ou créer les préférences
        preferences, created = NotificationPreference.objects.get_or_create(
            user=user,
            defaults={
                'email_enabled': True,
                'sms_enabled': False,
                'push_enabled': True,
                'in_app_enabled': True,
                'stock_notifications': True,
                'vente_notifications': True,
                'achat_notifications': False,  # Désactiver les notifications d'achat
                'quiet_hours_start': '22:00',
                'quiet_hours_end': '08:00',
                'weekend_notifications': False
            }
        )
        
        print(f"Préférences {'créées' if created else 'récupérées'} pour {user.username}")


def exemple_notification_avec_objet_lie():
    """Exemple : Notification liée à un objet métier"""
    
    from stock_Pme.models import Stock
    from produit.models import Produit
    
    # Récupérer un stock
    stock = Stock.objects.first()
    
    if stock:
        # Récupérer les responsables
        responsables = User.objects.filter(role='RESPONSABLE_MAGASIN')
        
        if responsables.exists():
            NotificationService.creer_et_envoyer_notification(
                type_notification='ALERTE_STOCK_BAS',
                destinataires=list(responsables),
                titre=f'Stock critique - {stock.produit.nom}',
                message=f'Le stock de {stock.produit.nom} nécessite un réapprovisionnement urgent',
                donnees_contexte={
                    'produit_nom': stock.produit.nom,
                    'quantite_actuelle': stock.quantite_disponible,
                    'seuil_alerte': stock.seuil_alerte
                },
                canal='IN_APP',
                priorite='CRITICAL',
                objet_lie=stock  # Lier la notification au stock
            )


def exemple_notification_conditionnelle():
    """Exemple : Notification conditionnelle selon les préférences"""
    
    from django.utils import timezone
    
    user = User.objects.first()
    
    if user:
        # Vérifier les préférences avant d'envoyer
        preferences = getattr(user, 'notification_preferences', None)
        
        if preferences:
            # Créer le type de notification
            try:
                notification_type = NotificationType.objects.get(nom='NOUVELLE_VENTE')
                
                # Vérifier si la notification est autorisée
                if preferences.is_notification_allowed(notification_type, timezone.now()):
                    NotificationService.creer_et_envoyer_notification(
                        type_notification='NOUVELLE_VENTE',
                        destinataires=[user],
                        titre='Vente autorisée',
                        message='Cette notification respecte vos préférences',
                        canal='IN_APP'
                    )
                    print("Notification envoyée")
                else:
                    print("Notification bloquée par les préférences utilisateur")
                    
            except NotificationType.DoesNotExist:
                print("Type de notification non trouvé")


def exemple_notification_urgente():
    """Exemple : Notification urgente multi-canal"""
    
    # Situation d'urgence : rupture de stock critique
    admins = User.objects.filter(role='ADMIN', statut=True)
    responsables = User.objects.filter(role='RESPONSABLE_MAGASIN', statut=True)
    
    destinataires = list(admins) + list(responsables)
    
    if destinataires:
        # Email urgent
        NotificationService.creer_et_envoyer_notification(
            type_notification='STOCK_EPUISE',
            destinataires=destinataires,
            titre='🚨 URGENT - Rupture de stock',
            message='Rupture de stock détectée sur un produit critique',
            donnees_contexte={
                'produit_nom': 'Produit Critique',
                'impact': 'Arrêt des ventes possible',
                'action_requise': 'Réapprovisionnement immédiat'
            },
            canal='EMAIL',
            priorite='CRITICAL'
        )
        
        # Notification in-app pour suivi
        NotificationService.creer_et_envoyer_notification(
            type_notification='STOCK_EPUISE',
            destinataires=destinataires,
            titre='Rupture de stock',
            message='Action requise immédiatement',
            canal='IN_APP',
            priorite='CRITICAL'
        )


def exemple_statistiques_notifications():
    """Exemple : Récupération des statistiques"""
    
    from notifications.models import Notification
    from django.db.models import Count
    
    user = User.objects.first()
    
    if user:
        # Statistiques pour l'utilisateur
        notifications = Notification.objects.filter(destinataire=user)
        
        stats = {
            'total': notifications.count(),
            'non_lues': notifications.exclude(statut='READ').count(),
            'par_canal': dict(
                notifications.values('canal').annotate(count=Count('id')).values_list('canal', 'count')
            ),
            'par_priorite': dict(
                notifications.values('priorite').annotate(count=Count('id')).values_list('priorite', 'count')
            )
        }
        
        print(f"Statistiques pour {user.username}:")
        print(f"- Total: {stats['total']}")
        print(f"- Non lues: {stats['non_lues']}")
        print(f"- Par canal: {stats['par_canal']}")
        print(f"- Par priorité: {stats['par_priorite']}")


# Fonction pour tester tous les exemples
def executer_tous_les_exemples():
    """Exécute tous les exemples (à utiliser avec précaution)"""
    
    print("🚀 Exécution des exemples de notifications...")
    
    try:
        print("\n1. Notification stock bas...")
        exemple_notification_stock_bas()
        
        print("\n2. Configuration préférences...")
        exemple_configuration_preferences()
        
        print("\n3. Notification conditionnelle...")
        exemple_notification_conditionnelle()
        
        print("\n4. Statistiques...")
        exemple_statistiques_notifications()
        
        print("\n✅ Tous les exemples exécutés avec succès!")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de l'exécution: {str(e)}")


if __name__ == "__main__":
    # Décommenter pour tester
    # executer_tous_les_exemples()
    pass
