from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Entreprise
from .serializers import EntrepriseSerializer, RapportEntrepriseSerializer
from base.views import IsAdmin
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class EntrepriseViewSet(viewsets.ModelViewSet):
    queryset = Entreprise.objects.all()
    serializer_class = EntrepriseSerializer
    permission_classes = [AllowAny]  # Temporairement pour les tests
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']  # Explicitement définir les méthodes autorisées

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.prefetch_related('magasins', 'magasins__localisation')

    @swagger_auto_schema(
        operation_description="Créer une nouvelle entreprise",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['nom', 'nif', 'adresse', 'date_creation'],
            properties={
                'nom': openapi.Schema(type=openapi.TYPE_STRING, max_length=100),
                'nif': openapi.Schema(type=openapi.TYPE_STRING, max_length=20, pattern='^[0-9]{13}$'),
                'adresse': openapi.Schema(type=openapi.TYPE_STRING),
                'date_creation': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'statut': openapi.Schema(type=openapi.TYPE_BOOLEAN, default=True),
                'administrateur': openapi.Schema(type=openapi.TYPE_STRING, format='uuid', nullable=True)
            }
        ),
        responses={
            201: openapi.Response(
                description="Entreprise créée avec succès",
                schema=EntrepriseSerializer
            ),
            400: "Bad Request"
        }
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @swagger_auto_schema(
        operation_description="Mettre à jour une entreprise existante",
        request_body=EntrepriseSerializer,
        responses={
            200: EntrepriseSerializer,
            400: "Bad Request",
            404: "Not Found"
        }
    )
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Supprimer une entreprise",
        responses={
            204: "No Content",
            404: "Not Found"
        }
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get'])
    def verifier_nif(self, request, pk=None):
        entreprise = self.get_object()
        est_valide = entreprise.verifier_validite_nif()
        return Response({'valide': est_valide})

    @action(detail=False, methods=['get'])
    def rapport(self, request):
        data = Entreprise.generer_rapport_entreprises()
        serializer = RapportEntrepriseSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def magasins(self, request, pk=None):
        entreprise = self.get_object()
        magasins = entreprise.magasins.all()
        from magasins.serializers import MagasinSerializer
        serializer = MagasinSerializer(magasins, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def ajouter_magasin(self, request, pk=None):
        entreprise = self.get_object()
        from magasins.serializers import MagasinSerializer
        serializer = MagasinSerializer(data=request.data)
        if serializer.is_valid():
            magasin = serializer.save(entreprise=entreprise)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def retirer_magasin(self, request, pk=None):
        entreprise = self.get_object()
        magasin_id = request.data.get('magasin_id')
        try:
            magasin = entreprise.magasins.get(id=magasin_id)
            magasin.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response(
                {'error': 'Magasin non trouvé ou impossible à supprimer'},
                status=status.HTTP_404_NOT_FOUND
            )