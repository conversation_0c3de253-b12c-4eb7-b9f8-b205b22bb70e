import uuid
from django.db import models
from django.core.exceptions import ValidationError
from categorie.models import Categorie
from entreprise.models import Entreprise
import logging
import barcode
from barcode.writer import ImageWriter
import os
from django.conf import settings

logger = logging.getLogger(__name__)

class Produit(models.Model):
    TYPE_STOCK_CHOICES = [
        ('FINIS', 'Produit fini'),
        ('MATIERE', 'Matière première'),
        ('EMBALLAGE', 'Emballage'),
        ('SEMI_FINI', 'Produit semi-fini'),
        ('CONSOMMABLE', 'Consommable')
    ]
    UNITE_CHOICES = [
        ('kg', 'Kilogramme'),
        ('g', 'Gramme'),
        ('L', 'Litre'),
        ('ml', 'Millilitre'),
        ('unité', 'Unité'),
    ]

    id_produit = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100)
    reference = models.CharField(max_length=50, unique=True)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, related_name='produits')
    magasin = models.ForeignKey('magasins.Magasin', on_delete=models.SET_NULL, null=True, blank=True, related_name='produits')
    type_stock = models.CharField(max_length=50, choices=TYPE_STOCK_CHOICES, default='FINIS')
    prix = models.DecimalField(max_digits=10, decimal_places=2)
    date_peremption = models.DateField(null=True, blank=True)  # Rendu optionnel pour éviter les erreurs
    code_barre = models.BigIntegerField(unique=True, null=True, blank=True)  # Rendu optionnel
    marque = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)
    unite_mesure = models.CharField(max_length=10, choices=UNITE_CHOICES)
    prix_achat = models.DecimalField(max_digits=10, decimal_places=2)
    prix_vente = models.DecimalField(max_digits=10, decimal_places=2)
    TVA = models.FloatField(default=0.2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    entreprise = models.ForeignKey('entreprise.Entreprise', on_delete=models.CASCADE)
    qr_code = models.ImageField(upload_to='qr_codes/', blank=True, null=True)

    class Meta:
        verbose_name = "Produit"
        verbose_name_plural = "Produits"
        indexes = [
            models.Index(fields=['reference']),
            models.Index(fields=['code_barre']),
            models.Index(fields=['categorie']),
            models.Index(fields=['magasin']),
        ]

    def __str__(self):
        return f"{self.nom} ({self.reference})"

    def clean(self):
        if self.prix <= 0:
            raise ValidationError("Le prix doit être supérieur à 0.")
        if self.prix_achat <= 0:
            raise ValidationError("Le prix d'achat doit être supérieur à 0.")
        if self.prix_vente <= 0:
            raise ValidationError("Le prix de vente doit être supérieur à 0.")
        if self.TVA < 0:
            raise ValidationError("La TVA ne peut pas être négative.")
        if self.code_barre and self.code_barre < 0:
            raise ValidationError("Le code-barre doit être positif.")
        if self.magasin and self.magasin.entreprise != self.entreprise:
            raise ValidationError("Le magasin doit appartenir à la même entreprise que le produit.")

    @classmethod
    def créerProduit(cls, data):
        """Crée un nouveau produit avec gestion du stock initial"""
        # Génération du code-barres EAN-13
        if 'code_barre' not in data or not data['code_barre']:
            # Générer un code-barres unique à 12 chiffres (le 13ème est la clé de contrôle)
            code_barre = int(str(uuid.uuid4().int)[:12])
            # Créer le code-barres EAN-13
            ean = barcode.get('ean13', str(code_barre), writer=ImageWriter())
            # Sauvegarder l'image du code-barres
            barcode_path = os.path.join(settings.MEDIA_ROOT, 'barcodes')
            os.makedirs(barcode_path, exist_ok=True)
            filename = f"{code_barre}"
            ean.save(os.path.join(barcode_path, filename))
            # Ajouter le code-barres aux données
            data['code_barre'] = code_barre

        produit = cls.objects.create(**data)
        
        # Création automatique du stock associé
        from entrepot.models import Entrepot
        from stock_Pme.models import Stock
        
        try:
            entrepot = Entrepot.objects.get(type_stock=produit.type_stock)
            Stock.objects.create(
                produit=produit,
                entrepot=entrepot,
                quantite=0,
                magasin=produit.categorie.produits.first().stock_set.first().magasin if produit.categorie and produit.categorie.produits.exists() else None
            )
        except Entrepot.DoesNotExist:
            logger.error(f"Aucun entrepôt trouvé pour le type de stock {produit.type_stock}")
            raise ValueError(f"Aucun entrepôt configuré pour le type de stock {produit.type_stock}")
        
        return produit

    def mettreÀJourProduit(self, data):
        """Met à jour les informations du produit"""
        for field, value in data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
        return self

    def vérifierDisponibilité(self):
        """Vérifie la disponibilité du produit en stock"""
        from stock_Pme.models import Stock
        stock_total = Stock.objects.filter(produit=self, magasin=self.magasin).aggregate(models.Sum('quantite'))['quantite__sum'] or 0
        return stock_total > 0

    @classmethod
    def getProductById(cls, id):
        """Récupère un produit par son ID avec ses informations de stock"""
        try:
            produit = cls.objects.get(id_produit=id)
            return produit
        except cls.DoesNotExist:
            return None

    @classmethod
    def getAllProducts(cls):
        """Récupère tous les produits avec leurs stocks"""
        return cls.objects.prefetch_related('stock_set').all()

    def supprimer_produit(self):
        """Supprime un produit et diminue la quantité en stock"""
        from stock_Pme.models import Stock

        # Récupérer le stock associé à ce produit
        stock = Stock.objects.filter(produit=self).first()
        nouvelle_quantite = 0

        if stock:
            # Vérifier si la quantité est supérieure à 0
            if stock.quantite_disponible > 0:
                # Diminuer la quantité de 1
                stock.retirerStock(1)
                nouvelle_quantite = stock.quantite_disponible
            else:
                raise ValidationError("Impossible de supprimer le produit : stock épuisé")

        # Supprimer le produit
        self.delete()

        return {
            'success': True,
            'nouvelle_quantite': nouvelle_quantite
        }