from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from django.db.models import F
import logging

from .services import NotificationService
from .models import NotificationType, NotificationPreference

logger = logging.getLogger(__name__)


# Signaux pour la gestion de stock
@receiver(post_save, sender='stock_Pme.Stock')
def notification_alerte_stock(sender, instance, created, **kwargs):
    """Envoie une notification quand le stock atteint le seuil d'alerte"""
    
    if not created and instance.quantite_disponible <= instance.seuil_alerte:
        # Récupérer les responsables du magasin et administrateurs
        from base.models import User
        from responsable_magasin.models import Responsable_magasin
        
        destinataires = []
        
        # Ajouter le responsable du magasin
        try:
            responsable = Responsable_magasin.objects.get(magasin=instance.magasin)
            destinataires.append(responsable.user)
        except Responsable_magasin.DoesNotExist:
            pass
        
        # Ajouter les administrateurs
        admins = User.objects.filter(role='ADMIN', statut=True)
        destinataires.extend(admins)
        
        if destinataires:
            NotificationService.creer_et_envoyer_notification(
                type_notification='ALERTE_STOCK_BAS',
                destinataires=destinataires,
                titre=f'Alerte Stock - {instance.produit.nom}',
                message=f'Le stock du produit {instance.produit.nom} est en dessous du seuil d\'alerte. '
                       f'Quantité actuelle: {instance.quantite_disponible}, Seuil: {instance.seuil_alerte}',
                donnees_contexte={
                    'produit_nom': instance.produit.nom,
                    'produit_reference': instance.produit.reference,
                    'quantite_actuelle': instance.quantite_disponible,
                    'seuil_alerte': instance.seuil_alerte,
                    'magasin_nom': instance.magasin.nom,
                    'entrepot_nom': instance.entrepot.nom
                },
                canal='IN_APP',
                priorite='HIGH',
                objet_lie=instance
            )


@receiver(post_save, sender='ventes.Vente')
def notification_nouvelle_vente(sender, instance, created, **kwargs):
    """Envoie une notification lors d'une nouvelle vente"""
    
    if created:
        # Notifier le responsable du magasin
        from responsable_magasin.models import Responsable_magasin
        
        try:
            responsable = Responsable_magasin.objects.get(magasin=instance.magasin)
            
            NotificationService.creer_et_envoyer_notification(
                type_notification='NOUVELLE_VENTE',
                destinataires=[responsable.user],
                titre=f'Nouvelle vente - {instance.montant_total}€',
                message=f'Une nouvelle vente de {instance.montant_total}€ a été réalisée '
                       f'par {instance.vendeur.user.get_full_name() if instance.vendeur else "Système"}',
                donnees_contexte={
                    'vente_id': str(instance.id_vente),
                    'montant_total': float(instance.montant_total),
                    'vendeur_nom': instance.vendeur.user.get_full_name() if instance.vendeur else 'Système',
                    'client_nom': f'{instance.client.nom} {instance.client.prenom}' if instance.client else 'Client anonyme',
                    'magasin_nom': instance.magasin.nom,
                    'mode_paiement': instance.get_mode_paiement_display()
                },
                canal='IN_APP',
                priorite='MEDIUM',
                objet_lie=instance
            )
        except Responsable_magasin.DoesNotExist:
            pass


@receiver(post_save, sender='achat_magasin.AchatMagasin')
def notification_nouvel_achat(sender, instance, created, **kwargs):
    """Envoie une notification lors d'un nouvel achat ou changement de statut"""
    
    if created:
        # Notifier les administrateurs
        from base.models import User
        admins = User.objects.filter(role='ADMIN', statut=True)
        
        if admins.exists():
            NotificationService.creer_et_envoyer_notification(
                type_notification='NOUVEL_ACHAT',
                destinataires=list(admins),
                titre=f'Nouvelle commande fournisseur - {instance.montant_total}€',
                message=f'Une nouvelle commande de {instance.montant_total}€ a été passée '
                       f'auprès de {instance.fournisseur.user.nom}',
                donnees_contexte={
                    'achat_id': str(instance.id),
                    'montant_total': float(instance.montant_total),
                    'fournisseur_nom': instance.fournisseur.user.nom,
                    'date_commande': instance.date_commande.isoformat(),
                    'date_reception_prevue': instance.date_reception_prevue.isoformat(),
                    'statut': instance.get_statut_display()
                },
                canal='IN_APP',
                priorite='MEDIUM',
                objet_lie=instance
            )
    
    # Notification de changement de statut
    elif hasattr(instance, '_original_statut') and instance._original_statut != instance.statut:
        if instance.statut == 'LIVRE':
            # Notifier la réception de la commande
            from base.models import User
            admins = User.objects.filter(role='ADMIN', statut=True)
            
            if admins.exists():
                NotificationService.creer_et_envoyer_notification(
                    type_notification='COMMANDE_LIVREE',
                    destinataires=list(admins),
                    titre=f'Commande livrée - {instance.fournisseur.user.nom}',
                    message=f'La commande de {instance.montant_total}€ de {instance.fournisseur.user.nom} a été livrée',
                    donnees_contexte={
                        'achat_id': str(instance.id),
                        'montant_total': float(instance.montant_total),
                        'fournisseur_nom': instance.fournisseur.user.nom,
                        'date_livraison': timezone.now().isoformat()
                    },
                    canal='IN_APP',
                    priorite='MEDIUM',
                    objet_lie=instance
                )


@receiver(pre_save, sender='achat_magasin.AchatMagasin')
def stocker_statut_original_achat(sender, instance, **kwargs):
    """Stocke le statut original avant modification"""
    if instance.pk:
        try:
            original = sender.objects.get(pk=instance.pk)
            instance._original_statut = original.statut
        except sender.DoesNotExist:
            pass


@receiver(post_save, sender='inventaire.Inventaire')
def notification_inventaire(sender, instance, created, **kwargs):
    """Envoie des notifications pour les inventaires"""
    
    if created:
        # Notifier le responsable du magasin
        from responsable_magasin.models import Responsable_magasin
        
        try:
            responsable = Responsable_magasin.objects.get(magasin=instance.magasin)
            
            NotificationService.creer_et_envoyer_notification(
                type_notification='INVENTAIRE_PLANIFIE',
                destinataires=[responsable.user],
                titre=f'Inventaire planifié - {instance.magasin.nom}',
                message=f'Un inventaire {instance.get_type_inventaire_display().lower()} '
                       f'a été planifié pour le {instance.date_planification}',
                donnees_contexte={
                    'inventaire_id': str(instance.id_inventaire),
                    'numero_inventaire': instance.numero_inventaire,
                    'type_inventaire': instance.get_type_inventaire_display(),
                    'magasin_nom': instance.magasin.nom,
                    'entrepot_nom': instance.entrepot.nom,
                    'date_planification': instance.date_planification.isoformat()
                },
                canal='IN_APP',
                priorite='MEDIUM',
                objet_lie=instance
            )
        except Responsable_magasin.DoesNotExist:
            pass
    
    # Notification de fin d'inventaire
    elif hasattr(instance, '_original_statut') and instance._original_statut != instance.statut:
        if instance.statut == 'TERMINE':
            # Calculer les écarts
            ecarts = instance.calculerEcarts()
            ecarts_significatifs = [e for e in ecarts if e['alerte']]
            
            # Notifier les administrateurs
            from base.models import User
            admins = User.objects.filter(role='ADMIN', statut=True)
            
            if admins.exists():
                NotificationService.creer_et_envoyer_notification(
                    type_notification='INVENTAIRE_TERMINE',
                    destinataires=list(admins),
                    titre=f'Inventaire terminé - {instance.magasin.nom}',
                    message=f'L\'inventaire {instance.numero_inventaire} est terminé. '
                           f'{len(ecarts_significatifs)} écarts significatifs détectés.',
                    donnees_contexte={
                        'inventaire_id': str(instance.id_inventaire),
                        'numero_inventaire': instance.numero_inventaire,
                        'magasin_nom': instance.magasin.nom,
                        'total_ecarts': len(ecarts_significatifs),
                        'date_fin': instance.date_fin.isoformat() if instance.date_fin else None
                    },
                    canal='IN_APP',
                    priorite='HIGH' if ecarts_significatifs else 'MEDIUM',
                    objet_lie=instance
                )


@receiver(pre_save, sender='inventaire.Inventaire')
def stocker_statut_original_inventaire(sender, instance, **kwargs):
    """Stocke le statut original avant modification"""
    if instance.pk:
        try:
            original = sender.objects.get(pk=instance.pk)
            instance._original_statut = original.statut
        except sender.DoesNotExist:
            pass


@receiver(post_save, sender='base.User')
def creer_preferences_notification(sender, instance, created, **kwargs):
    """Crée les préférences de notification pour un nouvel utilisateur"""
    
    if created:
        NotificationPreference.objects.get_or_create(
            user=instance,
            defaults={
                'email_enabled': True,
                'sms_enabled': False,
                'push_enabled': True,
                'in_app_enabled': True,
                'stock_notifications': True,
                'vente_notifications': True,
                'achat_notifications': True,
                'inventaire_notifications': True,
                'finance_notifications': True,
                'system_notifications': True,
                'security_notifications': True,
                'weekend_notifications': False
            }
        )
        
        # Envoyer une notification de bienvenue
        NotificationService.creer_et_envoyer_notification(
            type_notification='BIENVENUE',
            destinataires=[instance],
            titre='Bienvenue dans le système de gestion de stock',
            message=f'Bonjour {instance.get_full_name()}, bienvenue dans notre système de gestion de stock. '
                   f'Vous pouvez configurer vos préférences de notification dans votre profil.',
            donnees_contexte={
                'nom_utilisateur': instance.get_full_name(),
                'role': instance.get_role_display()
            },
            canal='IN_APP',
            priorite='LOW'
        )
