from django.core.management.base import BaseCommand
from django.utils import timezone
import time
import json
from performance.monitoring import Performance<PERSON>oni<PERSON>, AlertManager, PerformanceReporter


class Command(BaseCommand):
    help = 'Monitore les performances du système en temps réel'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Intervalle de monitoring en secondes (défaut: 60)'
        )
        parser.add_argument(
            '--duration',
            type=int,
            default=0,
            help='Durée totale en minutes (0 = infini)'
        )
        parser.add_argument(
            '--alerts-only',
            action='store_true',
            help='Afficher seulement les alertes'
        )
        parser.add_argument(
            '--output-file',
            type=str,
            help='Fichier de sortie pour les métriques JSON'
        )

    def handle(self, *args, **options):
        interval = options['interval']
        duration = options['duration']
        alerts_only = options['alerts_only']
        output_file = options['output_file']
        
        self.stdout.write(
            self.style.SUCCESS(f'🚀 Démarrage du monitoring (intervalle: {interval}s)')
        )
        
        if duration > 0:
            self.stdout.write(f'⏱️  Durée: {duration} minutes')
        else:
            self.stdout.write('⏱️  Durée: infinie (Ctrl+C pour arrêter)')
        
        start_time = time.time()
        iteration = 0
        
        try:
            while True:
                iteration += 1
                current_time = timezone.now()
                
                # Vérifier la durée
                if duration > 0:
                    elapsed_minutes = (time.time() - start_time) / 60
                    if elapsed_minutes >= duration:
                        break
                
                self.stdout.write(f'\n📊 Monitoring #{iteration} - {current_time.strftime("%H:%M:%S")}')
                
                # Récupérer les métriques
                system_metrics = PerformanceMonitor.get_system_metrics()
                db_metrics = PerformanceMonitor.get_database_metrics()
                app_metrics = PerformanceMonitor.get_application_metrics()
                
                # Vérifier les alertes
                system_alerts = AlertManager.check_system_alerts()
                app_alerts = AlertManager.check_application_alerts()
                all_alerts = system_alerts + app_alerts
                
                if not alerts_only:
                    # Afficher les métriques système
                    if system_metrics:
                        cpu = system_metrics.get('cpu', {})
                        memory = system_metrics.get('memory', {})
                        disk = system_metrics.get('disk', {})
                        
                        self.stdout.write(f'💻 CPU: {cpu.get("percent", 0):.1f}%')
                        self.stdout.write(f'🧠 RAM: {memory.get("percent", 0):.1f}% ({memory.get("used_gb", 0):.1f}GB/{memory.get("total_gb", 0):.1f}GB)')
                        self.stdout.write(f'💾 Disque: {disk.get("percent", 0):.1f}% ({disk.get("used_gb", 0):.1f}GB/{disk.get("total_gb", 0):.1f}GB)')
                    
                    # Afficher les métriques DB
                    if db_metrics:
                        self.stdout.write(f'🗄️  DB: {db_metrics.get("active_connections", 0)} connexions actives')
                        self.stdout.write(f'📏 Taille DB: {db_metrics.get("database_size", "N/A")}')
                    
                    # Afficher les métriques app
                    if app_metrics:
                        cache_stats = app_metrics.get('cache', {})
                        users_stats = app_metrics.get('users', {})
                        stock_stats = app_metrics.get('stock', {})
                        
                        self.stdout.write(f'⚡ Cache hit ratio: {cache_stats.get("hit_ratio", 0):.1f}%')
                        self.stdout.write(f'👥 Utilisateurs actifs (24h): {users_stats.get("active_24h", 0)}')
                        self.stdout.write(f'📦 Alertes stock: {stock_stats.get("alerts", 0)}')
                
                # Afficher les alertes
                if all_alerts:
                    self.stdout.write(self.style.WARNING(f'\n🚨 {len(all_alerts)} alerte(s) détectée(s):'))
                    for alert in all_alerts:
                        level_style = self.style.ERROR if alert['level'] == 'critical' else self.style.WARNING
                        self.stdout.write(level_style(f'   • {alert["message"]}'))
                        
                        # Envoyer les alertes critiques
                        if alert['level'] == 'critical':
                            AlertManager.send_alert_notification(alert)
                else:
                    if not alerts_only:
                        self.stdout.write(self.style.SUCCESS('✅ Aucune alerte'))
                
                # Sauvegarder dans un fichier si demandé
                if output_file:
                    metrics_data = {
                        'timestamp': current_time.isoformat(),
                        'iteration': iteration,
                        'system': system_metrics,
                        'database': db_metrics,
                        'application': app_metrics,
                        'alerts': all_alerts
                    }
                    
                    try:
                        with open(output_file, 'a') as f:
                            f.write(json.dumps(metrics_data) + '\n')
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f'Erreur écriture fichier: {e}'))
                
                # Attendre l'intervalle suivant
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS('\n🛑 Monitoring arrêté par l\'utilisateur'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'\n❌ Erreur monitoring: {e}'))
        
        # Statistiques finales
        total_time = time.time() - start_time
        self.stdout.write(f'\n📈 Monitoring terminé:')
        self.stdout.write(f'   • Durée: {total_time/60:.1f} minutes')
        self.stdout.write(f'   • Itérations: {iteration}')
        self.stdout.write(f'   • Intervalle moyen: {total_time/iteration:.1f}s' if iteration > 0 else '')
        
        if output_file:
            self.stdout.write(f'   • Données sauvées dans: {output_file}')
