import uuid
from django.db import models
from django.db.models import Su<PERSON>, Count, Avg
from magasins.models import Magasin
from entrepot.models import Entrepot
from vendeurs.models import Vendeur
from datetime import date, timedelta

class Statistique(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    entreprise = models.ForeignKey('entreprise.Entreprise', on_delete=models.CASCADE)
    magasin = models.ForeignKey(Magasin, on_delete=models.SET_NULL, null=True, blank=True)
    entrepot = models.ForeignKey(Entrepot, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Statistiques de ventes
    total_ventes = models.PositiveIntegerField(default=0)
    chiffre_affaire = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    montant_ht = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    montant_tva = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    produits_populaires = models.JSONField(default=list)
    
    # Statistiques financières
    marge_brute = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    taux_marge = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    cout_achat = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    benefice_net = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Statistiques de stock
    total_produits = models.PositiveIntegerField(default=0)
    valeur_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    produits_en_alerte = models.JSONField(default=list)
    taux_rotation = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Statistiques de clients
    nouveaux_clients = models.PositiveIntegerField(default=0)
    clients_actifs = models.PositiveIntegerField(default=0)
    panier_moyen = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    taux_fidelite = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    # Statistiques de facturation
    factures_emises = models.PositiveIntegerField(default=0)
    factures_payees = models.PositiveIntegerField(default=0)
    montant_impaye = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    delai_paiement_moyen = models.PositiveIntegerField(default=0)  # en jours
    
    # Statistiques de performance
    temps_traitement_moyen = models.PositiveIntegerField(default=0)  # en minutes
    taux_retour = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    satisfaction_client = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    
    # Période de statistique
    date_statistique = models.DateField()
    periode = models.CharField(max_length=10, choices=[
        ('JOUR', 'Journalier'),
        ('SEMAINE', 'Hebdomadaire'),
        ('MOIS', 'Mensuel'),
        ('ANNEE', 'Annuel')
    ], default='JOUR')
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['entreprise', 'magasin', 'entrepot', 'date_statistique', 'periode']
        ordering = ['-date_statistique', '-periode']
    
    def __str__(self):
        return f"Statistique {self.periode} du {self.date_statistique} pour {self.entrepot.nom if self.entrepot else self.magasin.nom}"
    
    @classmethod
    def calculer_statistiques_completes(cls, entreprise_id, magasin_id=None, entrepot_id=None, periode='JOUR'):
        from ventes.models import Vente, DetailVente
        from clients.models import Client
        from factures.models import Facture
        from stock_Pme.models import Stock
        from django.db.models import Sum, Count, Avg, F, ExpressionWrapper, DurationField
        from datetime import date, timedelta
        
        aujourd_hui = date.today()
        
        # Déterminer la période
        if periode == 'SEMAINE':
            date_debut = aujourd_hui - timedelta(days=7)
        elif periode == 'MOIS':
            date_debut = aujourd_hui - timedelta(days=30)
        elif periode == 'ANNEE':
            date_debut = aujourd_hui - timedelta(days=365)
        else:  # JOUR
            date_debut = aujourd_hui
            
        # Filtres de base
        filtres = {'entreprise_id': entreprise_id}
        if magasin_id:
            filtres['magasin_id'] = magasin_id
        if entrepot_id:
            filtres['entrepot_id'] = entrepot_id
            
        # Statistiques de ventes et financières
        ventes_stats = Vente.objects.filter(
            date_vente__date__gte=date_debut,
            date_vente__date__lte=aujourd_hui,
            **filtres
        ).aggregate(
            total_ventes=Count('id'),
            chiffre_affaire=Sum('montant_total'),
            montant_ht=Sum('montant_ht'),
            montant_tva=Sum('montant_tva'),
            cout_achat=Sum('cout_total'),
            benefice_net=Sum('benefice_net')
        )
        
        # Calcul de la marge
        marge_brute = ventes_stats['chiffre_affaire'] - ventes_stats['cout_achat'] if ventes_stats['chiffre_affaire'] else 0
        taux_marge = (marge_brute / ventes_stats['chiffre_affaire'] * 100) if ventes_stats['chiffre_affaire'] else 0
        
        # Produits les plus vendus avec marge
        produits_populaires = DetailVente.objects.filter(
            vente__date_vente__date__gte=date_debut,
            vente__date_vente__date__lte=aujourd_hui,
            vente__magasin_id=magasin_id
        ).values('produit__id', 'produit__nom').annotate(
            quantite=Sum('quantite_vendue'),
            marge=Sum('marge_unitaire')
        ).order_by('-quantite')[:5]
        
        # Statistiques de clients
        clients_stats = Client.objects.filter(
            created_at__date__gte=date_debut,
            created_at__date__lte=aujourd_hui,
            **filtres
        ).aggregate(
            nouveaux_clients=Count('id')
        )
        
        clients_actifs = Vente.objects.filter(
            date_vente__date__gte=date_debut,
            date_vente__date__lte=aujourd_hui,
            **filtres
        ).values('client').distinct().count()
        
        # Calcul du taux de fidélité
        total_clients = Client.objects.filter(**filtres).count()
        taux_fidelite = (clients_actifs / total_clients * 100) if total_clients else 0
        
        panier_moyen = Vente.objects.filter(
            date_vente__date__gte=date_debut,
            date_vente__date__lte=aujourd_hui,
            **filtres
        ).aggregate(
            moyenne=Avg('montant_total')
        )['moyenne'] or 0
        
        # Statistiques de facturation
        factures_stats = Facture.objects.filter(
            date_emission__date__gte=date_debut,
            date_emission__date__lte=aujourd_hui,
            vente__magasin_id=magasin_id
        ).aggregate(
            total_emises=Count('id'),
            total_payees=Count('id', filter=models.Q(statut='PAYEE')),
            montant_impaye=Sum('montant_total', filter=models.Q(statut='EN_ATTENTE')),
            delai_moyen=Avg(
                ExpressionWrapper(
                    F('date_paiement') - F('date_emission'),
                    output_field=DurationField()
                ),
                filter=models.Q(statut='PAYEE')
            )
        )
        
        # Statistiques de stock
        stock_stats = Stock.objects.filter(
            **filtres
        ).aggregate(
            total_produits=Count('id'),
            valeur_stock=Sum(F('quantite_disponible') * F('produit__prix_achat'))
        )
        
        # Calcul du taux de rotation
        valeur_stock_moyenne = stock_stats['valeur_stock'] or 0
        cout_ventes = ventes_stats['cout_achat'] or 0
        taux_rotation = (cout_ventes / valeur_stock_moyenne * 100) if valeur_stock_moyenne else 0
        
        # Produits en alerte de stock
        produits_alerte = Stock.objects.filter(
            **filtres,
            quantite_disponible__lte=F('seuil_alerte')
        ).values('produit__id', 'produit__nom', 'quantite_disponible', 'seuil_alerte')
        
        # Statistiques de performance
        performance_stats = Vente.objects.filter(
            date_vente__date__gte=date_debut,
            date_vente__date__lte=aujourd_hui,
            **filtres
        ).aggregate(
            temps_moyen=Avg(
                ExpressionWrapper(
                    F('date_vente') - F('date_creation'),
                    output_field=DurationField()
                )
            ),
            taux_retour=Count('id', filter=models.Q(statut='RETOURNE')) * 100.0 / Count('id'),
            satisfaction=Avg('note_satisfaction')
        )
        
        return cls.objects.create(
            entreprise_id=entreprise_id,
            magasin_id=magasin_id,
            entrepot_id=entrepot_id,
            periode=periode,
            total_ventes=ventes_stats['total_ventes'] or 0,
            chiffre_affaire=ventes_stats['chiffre_affaire'] or 0,
            montant_ht=ventes_stats['montant_ht'] or 0,
            montant_tva=ventes_stats['montant_tva'] or 0,
            marge_brute=marge_brute,
            taux_marge=taux_marge,
            cout_achat=ventes_stats['cout_achat'] or 0,
            benefice_net=ventes_stats['benefice_net'] or 0,
            produits_populaires=list(produits_populaires),
            total_produits=stock_stats['total_produits'] or 0,
            valeur_stock=valeur_stock_moyenne,
            taux_rotation=taux_rotation,
            produits_en_alerte=list(produits_alerte),
            nouveaux_clients=clients_stats['nouveaux_clients'] or 0,
            clients_actifs=clients_actifs,
            taux_fidelite=taux_fidelite,
            panier_moyen=panier_moyen,
            factures_emises=factures_stats['total_emises'] or 0,
            factures_payees=factures_stats['total_payees'] or 0,
            montant_impaye=factures_stats['montant_impaye'] or 0,
            delai_paiement_moyen=factures_stats['delai_moyen'].days if factures_stats['delai_moyen'] else 0,
            temps_traitement_moyen=performance_stats['temps_moyen'].total_seconds() / 60 if performance_stats['temps_moyen'] else 0,
            taux_retour=performance_stats['taux_retour'] or 0,
            satisfaction_client=performance_stats['satisfaction'] or 0,
            date_statistique=aujourd_hui
        )

class StatistiqueVendeur(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    vendeur = models.ForeignKey(Vendeur, on_delete=models.CASCADE)
    nombre_ventes = models.PositiveIntegerField(default=0)
    montant_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    moyenne_vente = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Statistiques de {self.vendeur.nom}"

class StatistiqueMagasin(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    magasin = models.ForeignKey(Magasin, on_delete=models.CASCADE)
    nombre_ventes = models.PositiveIntegerField(default=0)
    montant_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    moyenne_vente = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Statistiques de {self.magasin.nom}"




