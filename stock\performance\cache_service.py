"""
Service de cache centralisé pour optimiser les performances
"""

import json
import logging
from typing import Any, Optional, List, Dict
from functools import wraps
from django.core.cache import cache, caches
from django.core.cache.utils import make_template_fragment_key
from django.conf import settings
from django.db.models import QuerySet
from django.core.serializers import serialize
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)


class CacheService:
    """Service centralisé pour la gestion du cache Redis"""
    
    # Durées de cache par défaut (en secondes)
    CACHE_TIMEOUTS = {
        'short': 300,      # 5 minutes
        'medium': 1800,    # 30 minutes
        'long': 3600,      # 1 heure
        'very_long': 86400 # 24 heures
    }
    
    # Préfixes pour les clés de cache
    CACHE_PREFIXES = {
        'user': 'user',
        'product': 'product',
        'stock': 'stock',
        'sales': 'sales',
        'stats': 'stats',
        'notifications': 'notifications',
        'api': 'api',
        'query': 'query'
    }
    
    @classmethod
    def get_cache_key(cls, prefix: str, *args) -> str:
        """Génère une clé de cache standardisée"""
        key_parts = [cls.CACHE_PREFIXES.get(prefix, prefix)]
        key_parts.extend(str(arg) for arg in args)
        return ':'.join(key_parts)
    
    @classmethod
    def set(cls, key: str, value: Any, timeout: Optional[int] = None, cache_alias: str = 'default') -> bool:
        """Stocke une valeur dans le cache"""
        try:
            cache_instance = caches[cache_alias]
            timeout = timeout or cls.CACHE_TIMEOUTS['medium']
            cache_instance.set(key, value, timeout)
            logger.debug(f"Cache SET: {key} (timeout: {timeout}s)")
            return True
        except Exception as e:
            logger.error(f"Erreur cache SET {key}: {str(e)}")
            return False
    
    @classmethod
    def get(cls, key: str, default: Any = None, cache_alias: str = 'default') -> Any:
        """Récupère une valeur du cache"""
        try:
            cache_instance = caches[cache_alias]
            value = cache_instance.get(key, default)
            if value is not None and value != default:
                logger.debug(f"Cache HIT: {key}")
            else:
                logger.debug(f"Cache MISS: {key}")
            return value
        except Exception as e:
            logger.error(f"Erreur cache GET {key}: {str(e)}")
            return default
    
    @classmethod
    def delete(cls, key: str, cache_alias: str = 'default') -> bool:
        """Supprime une clé du cache"""
        try:
            cache_instance = caches[cache_alias]
            cache_instance.delete(key)
            logger.debug(f"Cache DELETE: {key}")
            return True
        except Exception as e:
            logger.error(f"Erreur cache DELETE {key}: {str(e)}")
            return False
    
    @classmethod
    def delete_pattern(cls, pattern: str, cache_alias: str = 'default') -> int:
        """Supprime toutes les clés correspondant au pattern"""
        try:
            cache_instance = caches[cache_alias]
            if hasattr(cache_instance, 'delete_pattern'):
                deleted = cache_instance.delete_pattern(pattern)
                logger.debug(f"Cache DELETE_PATTERN: {pattern} ({deleted} clés supprimées)")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Erreur cache DELETE_PATTERN {pattern}: {str(e)}")
            return 0
    
    @classmethod
    def clear_all(cls, cache_alias: str = 'default') -> bool:
        """Vide complètement le cache"""
        try:
            cache_instance = caches[cache_alias]
            cache_instance.clear()
            logger.info(f"Cache {cache_alias} vidé complètement")
            return True
        except Exception as e:
            logger.error(f"Erreur cache CLEAR {cache_alias}: {str(e)}")
            return False
    
    @classmethod
    def get_or_set(cls, key: str, callable_func, timeout: Optional[int] = None, cache_alias: str = 'default') -> Any:
        """Récupère du cache ou exécute la fonction et met en cache"""
        try:
            cache_instance = caches[cache_alias]
            timeout = timeout or cls.CACHE_TIMEOUTS['medium']
            value = cache_instance.get_or_set(key, callable_func, timeout)
            logger.debug(f"Cache GET_OR_SET: {key}")
            return value
        except Exception as e:
            logger.error(f"Erreur cache GET_OR_SET {key}: {str(e)}")
            return callable_func() if callable(callable_func) else callable_func
    
    @classmethod
    def cache_queryset(cls, queryset: QuerySet, key: str, timeout: Optional[int] = None) -> List[Dict]:
        """Met en cache un QuerySet Django"""
        def get_queryset_data():
            return json.loads(serialize('json', queryset))
        
        return cls.get_or_set(key, get_queryset_data, timeout)
    
    @classmethod
    def invalidate_user_cache(cls, user_id: str) -> None:
        """Invalide tout le cache d'un utilisateur"""
        pattern = cls.get_cache_key('user', user_id, '*')
        cls.delete_pattern(pattern)
    
    @classmethod
    def invalidate_product_cache(cls, product_id: str) -> None:
        """Invalide le cache d'un produit"""
        pattern = cls.get_cache_key('product', product_id, '*')
        cls.delete_pattern(pattern)
    
    @classmethod
    def invalidate_stock_cache(cls, magasin_id: str = None) -> None:
        """Invalide le cache de stock"""
        if magasin_id:
            pattern = cls.get_cache_key('stock', magasin_id, '*')
        else:
            pattern = cls.get_cache_key('stock', '*')
        cls.delete_pattern(pattern)


def cache_result(timeout: int = None, cache_alias: str = 'default', key_prefix: str = 'api'):
    """Décorateur pour mettre en cache le résultat d'une fonction"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Générer une clé de cache basée sur la fonction et ses arguments
            cache_key = CacheService.get_cache_key(
                key_prefix,
                func.__name__,
                hash(str(args) + str(sorted(kwargs.items())))
            )
            
            # Essayer de récupérer du cache
            result = CacheService.get(cache_key, cache_alias=cache_alias)
            
            if result is None:
                # Exécuter la fonction et mettre en cache
                result = func(*args, **kwargs)
                CacheService.set(cache_key, result, timeout, cache_alias)
            
            return result
        return wrapper
    return decorator


def cache_page_data(timeout: int = None, vary_on: List[str] = None):
    """Décorateur pour mettre en cache les données d'une page/vue"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            # Construire la clé de cache
            cache_key_parts = ['page', func.__name__]
            
            # Ajouter les paramètres de variation
            if vary_on:
                for param in vary_on:
                    if hasattr(request, param):
                        cache_key_parts.append(str(getattr(request, param)))
                    elif param in request.GET:
                        cache_key_parts.append(str(request.GET[param]))
            
            # Ajouter l'utilisateur si authentifié
            if hasattr(request, 'user') and request.user.is_authenticated:
                cache_key_parts.append(str(request.user.id_utilisateur))
            
            cache_key = ':'.join(cache_key_parts)
            
            # Essayer de récupérer du cache
            result = CacheService.get(cache_key)
            
            if result is None:
                # Exécuter la vue et mettre en cache
                result = func(request, *args, **kwargs)
                CacheService.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


class StockCacheManager:
    """Gestionnaire de cache spécialisé pour les stocks"""
    
    @staticmethod
    def get_stock_alerts_key(magasin_id: str) -> str:
        """Clé pour les alertes de stock d'un magasin"""
        return CacheService.get_cache_key('stock', 'alerts', magasin_id)
    
    @staticmethod
    def get_stock_summary_key(magasin_id: str) -> str:
        """Clé pour le résumé de stock d'un magasin"""
        return CacheService.get_cache_key('stock', 'summary', magasin_id)
    
    @staticmethod
    def cache_stock_alerts(magasin_id: str, alerts_data: List[Dict]) -> None:
        """Met en cache les alertes de stock"""
        key = StockCacheManager.get_stock_alerts_key(magasin_id)
        CacheService.set(key, alerts_data, CacheService.CACHE_TIMEOUTS['short'])
    
    @staticmethod
    def get_cached_stock_alerts(magasin_id: str) -> Optional[List[Dict]]:
        """Récupère les alertes de stock du cache"""
        key = StockCacheManager.get_stock_alerts_key(magasin_id)
        return CacheService.get(key)
    
    @staticmethod
    def invalidate_stock_cache(magasin_id: str) -> None:
        """Invalide tout le cache de stock d'un magasin"""
        CacheService.delete_pattern(f"stock:{magasin_id}:*")


class StatsCacheManager:
    """Gestionnaire de cache pour les statistiques"""
    
    @staticmethod
    def get_daily_stats_key(date: str, magasin_id: str = None) -> str:
        """Clé pour les statistiques journalières"""
        if magasin_id:
            return CacheService.get_cache_key('stats', 'daily', date, magasin_id)
        return CacheService.get_cache_key('stats', 'daily', date)
    
    @staticmethod
    def get_monthly_stats_key(year_month: str, magasin_id: str = None) -> str:
        """Clé pour les statistiques mensuelles"""
        if magasin_id:
            return CacheService.get_cache_key('stats', 'monthly', year_month, magasin_id)
        return CacheService.get_cache_key('stats', 'monthly', year_month)
    
    @staticmethod
    def cache_daily_stats(date: str, stats_data: Dict, magasin_id: str = None) -> None:
        """Met en cache les statistiques journalières"""
        key = StatsCacheManager.get_daily_stats_key(date, magasin_id)
        # Les stats journalières sont cachées plus longtemps
        CacheService.set(key, stats_data, CacheService.CACHE_TIMEOUTS['long'])
    
    @staticmethod
    def get_cached_daily_stats(date: str, magasin_id: str = None) -> Optional[Dict]:
        """Récupère les statistiques journalières du cache"""
        key = StatsCacheManager.get_daily_stats_key(date, magasin_id)
        return CacheService.get(key)


class NotificationsCacheManager:
    """Gestionnaire de cache pour les notifications"""
    
    @staticmethod
    def get_user_notifications_key(user_id: str, unread_only: bool = False) -> str:
        """Clé pour les notifications d'un utilisateur"""
        suffix = 'unread' if unread_only else 'all'
        return CacheService.get_cache_key('notifications', user_id, suffix)
    
    @staticmethod
    def cache_user_notifications(user_id: str, notifications_data: List[Dict], unread_only: bool = False) -> None:
        """Met en cache les notifications d'un utilisateur"""
        key = NotificationsCacheManager.get_user_notifications_key(user_id, unread_only)
        CacheService.set(key, notifications_data, CacheService.CACHE_TIMEOUTS['short'], 'notifications')
    
    @staticmethod
    def get_cached_user_notifications(user_id: str, unread_only: bool = False) -> Optional[List[Dict]]:
        """Récupère les notifications d'un utilisateur du cache"""
        key = NotificationsCacheManager.get_user_notifications_key(user_id, unread_only)
        return CacheService.get(key, cache_alias='notifications')
    
    @staticmethod
    def invalidate_user_notifications(user_id: str) -> None:
        """Invalide le cache des notifications d'un utilisateur"""
        CacheService.delete_pattern(f"notifications:{user_id}:*", 'notifications')


# Utilitaires de monitoring du cache
class CacheMonitor:
    """Moniteur pour analyser les performances du cache"""
    
    @staticmethod
    def get_cache_stats(cache_alias: str = 'default') -> Dict:
        """Récupère les statistiques du cache"""
        try:
            cache_instance = caches[cache_alias]
            if hasattr(cache_instance, '_cache') and hasattr(cache_instance._cache, 'info'):
                # Redis stats
                info = cache_instance._cache.info()
                return {
                    'hits': info.get('keyspace_hits', 0),
                    'misses': info.get('keyspace_misses', 0),
                    'keys': info.get('db0', {}).get('keys', 0),
                    'memory_usage': info.get('used_memory_human', '0B'),
                    'connected_clients': info.get('connected_clients', 0)
                }
        except Exception as e:
            logger.error(f"Erreur récupération stats cache: {str(e)}")
        
        return {}
    
    @staticmethod
    def get_hit_ratio(cache_alias: str = 'default') -> float:
        """Calcule le ratio de succès du cache"""
        stats = CacheMonitor.get_cache_stats(cache_alias)
        hits = stats.get('hits', 0)
        misses = stats.get('misses', 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return (hits / total) * 100
