#!/usr/bin/env python3
"""
Script pour créer les dossiers nécessaires à l'application
"""

import os
import sys

def create_folders():
    """Crée tous les dossiers nécessaires"""
    
    folders = [
        'logs',
        'data', 
        'static',
        'media',
        'media/uploads',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    print("🚀 Création des dossiers nécessaires...")
    
    created_count = 0
    existing_count = 0
    
    for folder in folders:
        try:
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
                print(f"✅ Créé: {folder}")
                created_count += 1
            else:
                print(f"ℹ️  Existe déjà: {folder}")
                existing_count += 1
        except Exception as e:
            print(f"❌ Erreur création {folder}: {e}")
    
    print(f"\n🎉 Terminé!")
    print(f"   • {created_count} dossiers créés")
    print(f"   • {existing_count} dossiers existants")
    
    # Créer des fichiers .gitkeep pour garder les dossiers vides dans git
    gitkeep_folders = ['logs', 'data', 'media/uploads']
    
    for folder in gitkeep_folders:
        gitkeep_path = os.path.join(folder, '.gitkeep')
        if not os.path.exists(gitkeep_path):
            try:
                with open(gitkeep_path, 'w') as f:
                    f.write('# Ce fichier permet de garder le dossier dans git\n')
                print(f"📝 Créé .gitkeep dans {folder}")
            except Exception as e:
                print(f"⚠️  Impossible de créer .gitkeep dans {folder}: {e}")

if __name__ == "__main__":
    create_folders()
