"""
Optimiseur de requêtes pour améliorer les performances de la base de données
"""

import logging
import time
from functools import wraps
from typing import Dict, List, Any, Optional
from django.db import connection
from django.db.models import QuerySet, Prefetch, Q
from django.core.paginator import Paginator
from django.conf import settings

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """Optimiseur de requêtes Django ORM"""

    @staticmethod
    def log_queries(func):
        """Décorateur pour logger les requêtes SQL"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if settings.DEBUG:
                initial_queries = len(connection.queries)
                start_time = time.time()

                result = func(*args, **kwargs)

                end_time = time.time()
                final_queries = len(connection.queries)

                queries_count = final_queries - initial_queries
                execution_time = end_time - start_time

                logger.info(
                    f"Function {func.__name__}: {queries_count} queries in {execution_time:.3f}s"
                )

                if queries_count > 10:  # Seuil d'alerte
                    logger.warning(
                        f"High query count detected in {func.__name__}: {queries_count} queries"
                    )

                return result
            else:
                return func(*args, **kwargs)
        return wrapper

    @staticmethod
    def optimize_product_queries():
        """Requêtes optimisées pour les produits"""
        from produit.models import Produit

        return Produit.objects.select_related(
            'categorie',
            'magasin',
            'entreprise'
        ).prefetch_related(
            'stock_set__location__entrepot',
            'stock_set__magasin'
        )

    @staticmethod
    def optimize_stock_queries():
        """Requêtes optimisées pour les stocks"""
        from stock_Pme.models import Stock

        return Stock.objects.select_related(
            'produit__categorie',
            'produit__magasin',
            'location__entrepot',
            'magasin',
            'entrepot'
        )

    @staticmethod
    def optimize_vente_queries():
        """Requêtes optimisées pour les ventes"""
        from ventes.models import Vente

        return Vente.objects.select_related(
            'client',
            'vendeur__user',
            'magasin',
            'entreprise'
        ).prefetch_related(
            Prefetch(
                'details',
                queryset=Vente.objects.select_related('produit__categorie')
            )
        )

    @staticmethod
    def optimize_notification_queries():
        """Requêtes optimisées pour les notifications"""
        from notifications.models import Notification

        return Notification.objects.select_related(
            'type_notification',
            'destinataire'
        )

    @staticmethod
    def get_paginated_queryset(queryset: QuerySet, page: int, per_page: int = 20) -> Dict:
        """Pagination optimisée avec comptage efficace"""
        paginator = Paginator(queryset, per_page)

        try:
            page_obj = paginator.page(page)
        except:
            page_obj = paginator.page(1)

        return {
            'results': list(page_obj.object_list),
            'count': paginator.count,
            'num_pages': paginator.num_pages,
            'current_page': page_obj.number,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'next_page_number': page_obj.next_page_number() if page_obj.has_next() else None,
            'previous_page_number': page_obj.previous_page_number() if page_obj.has_previous() else None,
        }


class StockQueryOptimizer:
    """Optimiseur spécialisé pour les requêtes de stock"""

    @staticmethod
    @QueryOptimizer.log_queries
    def get_stock_alerts(magasin_id: str = None) -> QuerySet:
        """Récupère les alertes de stock de manière optimisée"""
        from stock_Pme.models import Stock
        from django.db.models import F

        queryset = Stock.objects.select_related(
            'produit__categorie',
            'location__entrepot',
            'magasin'
        ).filter(
            quantite_disponible__lte=F('seuil_alerte')
        ).order_by('quantite_disponible')

        if magasin_id:
            queryset = queryset.filter(magasin_id=magasin_id)

        return queryset

    @staticmethod
    @QueryOptimizer.log_queries
    def get_stock_summary(magasin_id: str = None) -> Dict:
        """Résumé de stock optimisé"""
        from stock_Pme.models import Stock
        from django.db.models import Sum, Count, F, Case, When, IntegerField

        queryset = Stock.objects.all()
        if magasin_id:
            queryset = queryset.filter(magasin_id=magasin_id)

        summary = queryset.aggregate(
            total_produits=Count('id_stock'),
            total_quantite=Sum('quantite_disponible'),
            produits_en_alerte=Count(
                Case(
                    When(quantite_disponible__lte=F('seuil_alerte'), then=1),
                    output_field=IntegerField()
                )
            ),
            produits_epuises=Count(
                Case(
                    When(quantite_disponible=0, then=1),
                    output_field=IntegerField()
                )
            )
        )

        return summary

    @staticmethod
    @QueryOptimizer.log_queries
    def get_stock_movements(magasin_id: str = None, limit: int = 100) -> QuerySet:
        """Mouvements de stock récents optimisés"""
        from stock_Pme.models import MouvementStock

        queryset = MouvementStock.objects.select_related(
            'produit__categorie',
            'location__entrepot'
        ).order_by('-date_mouvement')

        if magasin_id:
            queryset = queryset.filter(location__entrepot__magasins__id=magasin_id)

        return queryset[:limit]


class SalesQueryOptimizer:
    """Optimiseur spécialisé pour les requêtes de ventes"""

    @staticmethod
    @QueryOptimizer.log_queries
    def get_daily_sales(date_debut, date_fin, magasin_id: str = None) -> QuerySet:
        """Ventes journalières optimisées"""
        from ventes.models import Vente

        queryset = Vente.objects.select_related(
            'client',
            'vendeur__user',
            'magasin'
        ).filter(
            date_vente__date__range=[date_debut, date_fin]
        ).order_by('-date_vente')

        if magasin_id:
            queryset = queryset.filter(magasin_id=magasin_id)

        return queryset

    @staticmethod
    @QueryOptimizer.log_queries
    def get_sales_stats(date_debut, date_fin, magasin_id: str = None) -> Dict:
        """Statistiques de ventes optimisées"""
        from ventes.models import Vente
        from django.db.models import Sum, Count, Avg

        queryset = Vente.objects.filter(
            date_vente__date__range=[date_debut, date_fin]
        )

        if magasin_id:
            queryset = queryset.filter(magasin_id=magasin_id)

        stats = queryset.aggregate(
            total_ventes=Count('id_vente'),
            chiffre_affaire=Sum('montant_total'),
            montant_ht=Sum('montant_ht'),
            montant_tva=Sum('montant_tva'),
            panier_moyen=Avg('montant_total')
        )

        return stats

    @staticmethod
    @QueryOptimizer.log_queries
    def get_top_products(date_debut, date_fin, magasin_id: str = None, limit: int = 10) -> QuerySet:
        """Top produits vendus optimisé"""
        from ventes.models import DetailVente
        from django.db.models import Sum, Count

        queryset = DetailVente.objects.select_related(
            'produit__categorie',
            'vente__magasin'
        ).filter(
            vente__date_vente__date__range=[date_debut, date_fin]
        )

        if magasin_id:
            queryset = queryset.filter(vente__magasin_id=magasin_id)

        return queryset.values(
            'produit__id_produit',
            'produit__nom',
            'produit__reference'
        ).annotate(
            quantite_totale=Sum('quantite_vendue'),
            nombre_ventes=Count('id_detail_vente'),
            chiffre_affaire=Sum('montant_total')
        ).order_by('-quantite_totale')[:limit]


class DatabaseOptimizer:
    """Optimiseur pour la base de données"""

    @staticmethod
    def analyze_slow_queries(threshold_ms: int = 100) -> List[Dict]:
        """Analyse les requêtes lentes"""
        if not settings.DEBUG:
            return []

        slow_queries = []
        for query in connection.queries:
            time_ms = float(query['time']) * 1000
            if time_ms > threshold_ms:
                slow_queries.append({
                    'sql': query['sql'],
                    'time_ms': time_ms,
                    'formatted_time': f"{time_ms:.2f}ms"
                })

        return sorted(slow_queries, key=lambda x: x['time_ms'], reverse=True)

    @staticmethod
    def get_query_stats() -> Dict:
        """Statistiques des requêtes"""
        if not settings.DEBUG:
            return {}

        queries = connection.queries
        total_time = sum(float(q['time']) for q in queries)

        return {
            'total_queries': len(queries),
            'total_time': f"{total_time:.3f}s",
            'average_time': f"{(total_time / len(queries) * 1000):.2f}ms" if queries else "0ms",
            'slow_queries_count': len([q for q in queries if float(q['time']) > 0.1])
        }

    @staticmethod
    def suggest_indexes() -> List[str]:
        """Suggère des index à créer pour SQLite"""
        suggestions = []

        # Index optimisés pour SQLite
        sqlite_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_stock_quantite_alerte ON stock_pme_stock(quantite_disponible, seuil_alerte);",
            "CREATE INDEX IF NOT EXISTS idx_vente_date_magasin ON ventes_vente(date_vente, magasin_id);",
            "CREATE INDEX IF NOT EXISTS idx_notification_destinataire_statut ON notifications_notification(destinataire_id, statut);",
            "CREATE INDEX IF NOT EXISTS idx_produit_entreprise_categorie ON produit_produit(entreprise_id, categorie_id);",
            "CREATE INDEX IF NOT EXISTS idx_mouvement_stock_date ON stock_pme_mouvementstock(date_mouvement);",
            "CREATE INDEX IF NOT EXISTS idx_user_role_statut ON base_user(role, statut);",
            "CREATE INDEX IF NOT EXISTS idx_stock_magasin_produit ON stock_pme_stock(magasin_id, produit_id);",
            "CREATE INDEX IF NOT EXISTS idx_vente_vendeur_date ON ventes_vente(vendeur_id, date_vente);",
        ]

        suggestions.extend(sqlite_indexes)
        return suggestions


class QueryProfiler:
    """Profileur de requêtes pour le développement"""

    def __init__(self):
        self.queries_log = []

    def __enter__(self):
        if settings.DEBUG:
            self.initial_queries = len(connection.queries)
            self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if settings.DEBUG:
            self.end_time = time.time()
            self.final_queries = len(connection.queries)

            self.queries_count = self.final_queries - self.initial_queries
            self.execution_time = self.end_time - self.start_time

            # Log des nouvelles requêtes
            new_queries = connection.queries[self.initial_queries:]
            for query in new_queries:
                self.queries_log.append({
                    'sql': query['sql'],
                    'time': query['time']
                })

    def get_report(self) -> Dict:
        """Génère un rapport de performance"""
        if not settings.DEBUG:
            return {}

        return {
            'queries_count': self.queries_count,
            'execution_time': f"{self.execution_time:.3f}s",
            'queries': self.queries_log,
            'slow_queries': [q for q in self.queries_log if float(q['time']) > 0.1]
        }


# Décorateurs utiles
def optimize_queryset(select_related: List[str] = None, prefetch_related: List[str] = None):
    """Décorateur pour optimiser automatiquement un QuerySet"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)

            if isinstance(result, QuerySet):
                if select_related:
                    result = result.select_related(*select_related)
                if prefetch_related:
                    result = result.prefetch_related(*prefetch_related)

            return result
        return wrapper
    return decorator


def paginate_queryset(per_page: int = 20):
    """Décorateur pour paginer automatiquement un QuerySet"""
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            result = func(request, *args, **kwargs)

            if isinstance(result, QuerySet):
                page = int(request.GET.get('page', 1))
                return QueryOptimizer.get_paginated_queryset(result, page, per_page)

            return result
        return wrapper
    return decorator
