import uuid
from django.db import models
from django.core.exceptions import ValidationError

class Client(models.Model):
    id_client = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100)
    prenom = models.CharField(max_length=100)
    telephone = models.CharField(max_length=15)
    email = models.EmailField(blank=True, null=True)
    adresse = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Client"
        verbose_name_plural = "Clients"
        indexes = [
            models.Index(fields=['nom', 'prenom']),
            models.Index(fields=['telephone']),
            models.Index(fields=['email']),
        ]

    def __str__(self):
        return f"{self.nom} {self.prenom}"

    @classmethod
    def creer_client(cls, data):
        """Crée un nouveau client"""
        return cls.objects.create(**data)
    
    def mettre_a_jour_client(self, data):
        for field, value in data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
    
    def generer_historique_achats(self):
        # Utilise la relation inverse 'vente_set' au lieu d'importer Vente
        return self.vente_set.all().order_by('-date_vente')
        
        