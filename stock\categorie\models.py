import uuid
from django.db import models
from django.core.exceptions import ValidationError

class Categorie(models.Model):
    id_categorie = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=100, unique=True)

    class Meta:
        verbose_name = "Catégorie"
        verbose_name_plural = "Catégories"
        indexes = [
            models.Index(fields=['nom']),
        ]

    def __str__(self):
        return self.nom

    def clean(self):
        if not self.nom:
            raise ValidationError("Le nom de la catégorie ne peut pas être vide.")

    @classmethod
    def creerCategorie(cls, nom):
        return cls.objects.create(nom=nom)

    def mettreAJourCategorie(self, nom):
        self.nom = nom
        self.save()
        return self

    @classmethod
    def getCategorieById(cls, id_categorie):
        try:
            return cls.objects.get(id_categorie=id_categorie)
        except cls.DoesNotExist:
            raise ValidationError("Catégorie non trouvée.")
