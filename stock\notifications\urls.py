from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from .views import (
    NotificationTypeViewSet,
    NotificationPreferenceView,
    NotificationViewSet,
    NotificationBatchViewSet,
    EnvoyerNotificationView,
    create_notification_type,
)

# Configuration du router pour les ViewSets
router = DefaultRouter()
router.register(r'types', NotificationTypeViewSet, basename='notification-types')
router.register(r'notifications', NotificationViewSet, basename='notifications')
router.register(r'batches', NotificationBatchViewSet, basename='notification-batches')

urlpatterns = [
    # Routes du router
    path('', include(router.urls)),
    
    # Routes personnalisées
    path('preferences/', NotificationPreferenceView.as_view(), name='notification-preferences'),
    path('envoyer/', EnvoyerNotificationView.as_view(), name='envoyer-notification'),
    
    # Routes spécifiques pour les notifications
    path('notifications/non-lues/', NotificationViewSet.as_view({'get': 'list'}), 
         name='notifications-non-lues'),
    path('notifications/statistiques/', NotificationViewSet.as_view({'get': 'statistiques'}), 
         name='notifications-statistiques'),
    path('notifications/marquer-toutes-lues/', 
         NotificationViewSet.as_view({'post': 'marquer_toutes_comme_lues'}), 
         name='notifications-marquer-toutes-lues'),
    path('notifications/marquer-selection-lues/', 
         NotificationViewSet.as_view({'post': 'marquer_selection_comme_lues'}), 
         name='notifications-marquer-selection-lues'),
    path('create-notification-type/', create_notification_type, name='create-notification-type'),
]
