from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.db import transaction
from .models import AchatMagasin, DetailAchatMagasin
from stock_Pme.models import Stock
from magasins.models import Magasin
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=DetailAchatMagasin)
def update_stock_after_detail_achat(sender, instance, created, **kwargs):
    """
    Met à jour le stock après la création ou la modification d'un détail d'achat
    """
    try:
        with transaction.atomic():
            # Ne mettre à jour le stock que si l'achat est confirmé
            if instance.achat.statut != 'CONFIRME':
                return

            magasin_principal = Magasin.objects.filter(actif=True).first()
            if not magasin_principal:
                logger.error("Aucun magasin actif trouvé")
                return

            stock, created = Stock.objects.get_or_create(
                entrepot__type_stock=instance.produit.type_stock,
                produit=instance.produit,
                magasin=magasin_principal,
                defaults={'quantite': 0}
            )

            if created:
                # Nouveau détail d'achat
                stock.ajouter_produit(instance.quantite_achetee)
            else:
                # Modification d'un détail existant
                old_instance = DetailAchatMagasin.objects.get(pk=instance.pk)
                difference = instance.quantite_achetee - old_instance.quantite_achetee
                if difference > 0:
                    stock.ajouter_produit(difference)
                elif difference < 0:
                    stock.retirer_produit(abs(difference))

    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}")

@receiver(post_delete, sender=DetailAchatMagasin)
def restore_stock_after_detail_achat_delete(sender, instance, **kwargs):
    """
    Restaure le stock après la suppression d'un détail d'achat
    """
    try:
        with transaction.atomic():
            # Ne restaurer le stock que si l'achat était confirmé
            if instance.achat.statut != 'CONFIRME':
                return

            magasin_principal = Magasin.objects.filter(actif=True).first()
            if not magasin_principal:
                logger.error("Aucun magasin actif trouvé")
                return

            stock = Stock.objects.get(
                entrepot__type_stock=instance.produit.type_stock,
                produit=instance.produit,
                magasin=magasin_principal
            )
            stock.retirer_produit(instance.quantite_achetee)

    except Stock.DoesNotExist:
        logger.error(f"Stock non trouvé pour le produit {instance.produit.nom}")
    except Exception as e:
        logger.error(f"Erreur lors de la restauration du stock: {str(e)}")

@receiver(pre_save, sender=AchatMagasin)
def validate_achat(sender, instance, **kwargs):
    """
    Valide l'achat avant la sauvegarde
    """
    if instance.montant_total < 0:
        raise ValueError("Le montant total ne peut pas être négatif")
    
    if instance.date_reception_prevue < instance.date_commande:
        raise ValueError("La date de réception prévue ne peut pas être antérieure à la date de commande") 