from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from .models import Facture
from .serializers import FactureSerializer
from stock.ventes.models import Vente
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class FactureView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Récupérer ou générer une facture pour une vente donnée",
        manual_parameters=[
            openapi.Parameter('vente_id', openapi.IN_PATH, description="ID de la vente", type=openapi.TYPE_STRING)
        ],
        responses={
            200: openapi.Response(description="Facture existante ou générée", schema=openapi.Schema(type=openapi.TYPE_OBJECT)),
            201: openapi.Response(description="Facture générée", schema=openapi.Schema(type=openapi.TYPE_OBJECT)),
            404: "Vente non trouvée"
        }
    )
    def get(self, request, vente_id):
        try:
            vente = Vente.objects.get(id_vente=vente_id)
            # Vérifier si une facture existe déjà pour cette vente
            try:
                facture = Facture.objects.get(vente=vente)
                serializer = FactureSerializer(facture)
                return Response(serializer.data)
            except Facture.DoesNotExist:
                # Générer une nouvelle facture
                facture = Facture.generer_facture(vente)
                serializer = FactureSerializer(facture)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Vente.DoesNotExist:
            return Response(
                {"error": "Vente non trouvée"},
                status=status.HTTP_404_NOT_FOUND
            ) 