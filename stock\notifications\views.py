from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count
from django.utils import timezone
from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import (
    NotificationType, 
    NotificationPreference, 
    Notification, 
    NotificationBatch
)
from .serializers import (
    NotificationTypeSerializer,
    NotificationPreferenceSerializer,
    NotificationSerializer,
    NotificationCreateSerializer,
    NotificationBatchSerializer,
    NotificationStatsSerializer,
    NotificationMarkAsReadSerializer
)
from .services import NotificationService
from base.views import IsAdmin


class NotificationTypeViewSet(ModelViewSet):
    """ViewSet pour la gestion des types de notifications"""
    
    queryset = NotificationType.objects.all()
    serializer_class = NotificationTypeSerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    
    def get_queryset(self):
        # Vérification pour la génération du schéma Swagger
        if getattr(self, 'swagger_fake_view', False):
            return NotificationType.objects.none()

        queryset = super().get_queryset()

        # Filtrer par catégorie
        categorie = self.request.query_params.get('categorie')
        if categorie:
            queryset = queryset.filter(categorie=categorie)
        
        # Filtrer par statut actif
        actif = self.request.query_params.get('actif')
        if actif is not None:
            queryset = queryset.filter(actif=actif.lower() == 'true')
        
        return queryset.order_by('categorie', 'nom')

    @swagger_auto_schema(
        operation_description="Lister tous les types de notifications",
        responses={200: NotificationTypeSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Créer un type de notification",
        request_body=NotificationTypeSerializer,
        responses={201: NotificationTypeSerializer()}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)


class NotificationPreferenceView(APIView):
    """Vue pour gérer les préférences de notifications de l'utilisateur"""
    
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Récupérer les préférences de notification de l'utilisateur connecté",
        responses={200: NotificationPreferenceSerializer()}
    )
    def get(self, request):
        """Récupère les préférences de l'utilisateur connecté"""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=request.user
        )
        serializer = NotificationPreferenceSerializer(preferences)
        return Response(serializer.data)
    
    @swagger_auto_schema(
        operation_description="Mettre à jour les préférences de notification de l'utilisateur connecté",
        request_body=NotificationPreferenceSerializer,
        responses={200: NotificationPreferenceSerializer(), 400: "Bad Request"}
    )
    def put(self, request):
        """Met à jour les préférences de l'utilisateur connecté"""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=request.user
        )
        serializer = NotificationPreferenceSerializer(
            preferences, 
            data=request.data, 
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class NotificationViewSet(ModelViewSet):
    """ViewSet pour la gestion des notifications"""
    
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Retourne les notifications de l'utilisateur connecté"""
        # Vérification pour la génération du schéma Swagger
        if getattr(self, 'swagger_fake_view', False):
            return Notification.objects.none()

        queryset = Notification.objects.filter(destinataire=self.request.user)
        
        # Filtrer par statut
        statut = self.request.query_params.get('statut')
        if statut:
            queryset = queryset.filter(statut=statut)
        
        # Filtrer par canal
        canal = self.request.query_params.get('canal')
        if canal:
            queryset = queryset.filter(canal=canal)
        
        # Filtrer par priorité
        priorite = self.request.query_params.get('priorite')
        if priorite:
            queryset = queryset.filter(priorite=priorite)
        
        # Filtrer par type
        type_notification = self.request.query_params.get('type')
        if type_notification:
            queryset = queryset.filter(type_notification__nom=type_notification)
        
        # Filtrer les non lues
        non_lues = self.request.query_params.get('non_lues')
        if non_lues and non_lues.lower() == 'true':
            queryset = queryset.exclude(statut='READ')
        
        return queryset.order_by('-date_creation')
    
    def get_serializer_class(self):
        if self.action == 'create':
            return NotificationCreateSerializer
        return NotificationSerializer
    
    def create(self, request, *args, **kwargs):
        """Crée et envoie des notifications"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            notifications = serializer.save()
            
            # Envoyer les notifications
            for notification in notifications:
                NotificationService.envoyer_notification(notification)
            
            return Response(
                {'message': f'{len(notifications)} notifications créées et envoyées'},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def marquer_comme_lue(self, request, pk=None):
        """Marque une notification comme lue"""
        notification = self.get_object()
        notification.marquer_comme_lue()
        return Response({'message': 'Notification marquée comme lue'})
    
    @action(detail=False, methods=['post'])
    def marquer_toutes_comme_lues(self, request):
        """Marque toutes les notifications comme lues"""
        notifications = self.get_queryset().exclude(statut='READ')
        count = notifications.count()
        
        for notification in notifications:
            notification.marquer_comme_lue()
        
        return Response({'message': f'{count} notifications marquées comme lues'})
    
    @action(detail=False, methods=['post'])
    def marquer_selection_comme_lues(self, request):
        """Marque une sélection de notifications comme lues"""
        serializer = NotificationMarkAsReadSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            notification_ids = serializer.validated_data['notification_ids']
            notifications = Notification.objects.filter(
                id__in=notification_ids,
                destinataire=request.user
            )
            
            count = 0
            for notification in notifications:
                if notification.statut != 'READ':
                    notification.marquer_comme_lue()
                    count += 1
            
            return Response({'message': f'{count} notifications marquées comme lues'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def statistiques(self, request):
        """Retourne les statistiques des notifications de l'utilisateur"""
        queryset = self.get_queryset()
        
        # Statistiques générales
        total_notifications = queryset.count()
        notifications_non_lues = queryset.exclude(statut='READ').count()
        
        # Statistiques par canal
        notifications_par_canal = dict(
            queryset.values('canal').annotate(count=Count('id')).values_list('canal', 'count')
        )
        
        # Statistiques par priorité
        notifications_par_priorite = dict(
            queryset.values('priorite').annotate(count=Count('id')).values_list('priorite', 'count')
        )
        
        # Statistiques par statut
        notifications_par_statut = dict(
            queryset.values('statut').annotate(count=Count('id')).values_list('statut', 'count')
        )
        
        # Notifications récentes (7 derniers jours)
        date_limite = timezone.now() - timezone.timedelta(days=7)
        notifications_recentes = queryset.filter(date_creation__gte=date_limite)[:10]
        
        data = {
            'total_notifications': total_notifications,
            'notifications_non_lues': notifications_non_lues,
            'notifications_par_canal': notifications_par_canal,
            'notifications_par_priorite': notifications_par_priorite,
            'notifications_par_statut': notifications_par_statut,
            'notifications_recentes': NotificationSerializer(notifications_recentes, many=True).data
        }
        
        serializer = NotificationStatsSerializer(data)
        return Response(serializer.data)


class NotificationBatchViewSet(ModelViewSet):
    """ViewSet pour la gestion des lots de notifications"""
    
    queryset = NotificationBatch.objects.all()
    serializer_class = NotificationBatchSerializer
    permission_classes = [IsAuthenticated, IsAdmin]
    
    def get_queryset(self):
        # Vérification pour la génération du schéma Swagger
        if getattr(self, 'swagger_fake_view', False):
            return NotificationBatch.objects.none()

        queryset = super().get_queryset()

        # Filtrer par statut
        statut = self.request.query_params.get('statut')
        if statut:
            queryset = queryset.filter(statut=statut)
        
        # Filtrer par créateur
        createur = self.request.query_params.get('createur')
        if createur:
            queryset = queryset.filter(createur__username=createur)
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        """Assigne le créateur lors de la création"""
        serializer.save(createur=self.request.user)


class EnvoyerNotificationView(APIView):
    """Vue pour envoyer des notifications personnalisées"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Envoie une notification personnalisée"""
        data = request.data
        
        # Validation des données requises
        required_fields = ['type_notification', 'destinataires', 'titre', 'message']
        for field in required_fields:
            if field not in data:
                return Response(
                    {'error': f'Le champ {field} est requis'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        try:
            # Récupérer les destinataires
            from base.models import User
            destinataires = User.objects.filter(
                id_utilisateur__in=data['destinataires']
            )
            
            if not destinataires.exists():
                return Response(
                    {'error': 'Aucun destinataire valide trouvé'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Créer et envoyer les notifications
            notifications = NotificationService.creer_et_envoyer_notification(
                type_notification=data['type_notification'],
                destinataires=list(destinataires),
                titre=data['titre'],
                message=data['message'],
                donnees_contexte=data.get('donnees_contexte', {}),
                canal=data.get('canal', 'IN_APP'),
                priorite=data.get('priorite'),
                envoyer_immediatement=data.get('envoyer_immediatement', True)
            )
            
            return Response({
                'message': f'{len(notifications)} notifications créées et envoyées',
                'notifications_ids': [str(n.id) for n in notifications]
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': f'Erreur lors de l\'envoi: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_notification_type(request):
    """
    Vue temporaire pour créer le type de notification manquant.
    À supprimer une fois que le type est créé.
    """
    try:
        notification_type = NotificationType.objects.create(
            name="stock_alert",
            description="Alerte de stock bas",
            template="Le produit {product_name} a atteint le seuil d'alerte de {threshold} unités."
        )
        return Response({
            "status": "success",
            "message": "Type de notification créé avec succès",
            "data": {
                "id": notification_type.id,
                "name": notification_type.name,
                "description": notification_type.description
            }
        }, status=201)
    except Exception as e:
        return Response({
            "status": "error",
            "message": str(e)
        }, status=400)
